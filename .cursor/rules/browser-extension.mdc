---
description: 
globs: 
alwaysApply: false
---
# 浏览器扩展规则

## 扩展架构概述
浏览器扩展代码位于 [secure-password-ext/](mdc:secure-password-ext) 目录，与主应用通过原生消息传递进行通信。

## 扩展结构

### 核心文件
- **Manifest**：[secure-password-ext/src/manifest.ts](mdc:secure-password-ext/src/manifest.ts)
- **Background Script**：[secure-password-ext/src/background/index.ts](mdc:secure-password-ext/src/background/index.ts)
- **Content Script**：[secure-password-ext/src/contentScript/content-script-main.ts](mdc:secure-password-ext/src/contentScript/content-script-main.ts)
- **Popup**：[secure-password-ext/src/popup/](mdc:secure-password-ext/src/popup)
- **Options**：[secure-password-ext/src/options/](mdc:secure-password-ext/src/options)

### 扩展组件
- **侧边栏**：[secure-password-ext/src/sidepanel/](mdc:secure-password-ext/src/sidepanel)
- **开发者工具**：[secure-password-ext/src/devtools/](mdc:secure-password-ext/src/devtools)
- **新标签页**：[secure-password-ext/src/newtab/](mdc:secure-password-ext/src/newtab)

## 功能实现

### 表单检测和填充
- 表单检测测试：[secure-password-ext/src/tests/form-detection.test.ts](mdc:secure-password-ext/src/tests/form-detection.test.ts)
- 自动填充逻辑应安全且准确
- 支持各种表单类型和网站结构

### 密码生成
- 密码生成 Hook：[secure-password-ext/src/popup/hooks/usePasswordGenerator.ts](mdc:secure-password-ext/src/popup/hooks/usePasswordGenerator.ts)
- 密码选项组件：[secure-password-ext/src/popup/PasswordOptions.tsx](mdc:secure-password-ext/src/popup/PasswordOptions.tsx)
- 与主应用的密码生成策略保持一致

### 用户界面
- **下拉菜单**：[secure-password-ext/src/components/PasswordDropdown.tsx](mdc:secure-password-ext/src/components/PasswordDropdown.tsx)
- 使用统一的设计系统和样式
- 响应式设计适配不同屏幕尺寸

## 服务层

### 消息传递服务
- **背景消息服务**：[secure-password-ext/src/services/background-message.service.ts](mdc:secure-password-ext/src/services/background-message.service.ts)
- **跨框架定位服务**：[secure-password-ext/src/services/cross-iframe-positioner.service.ts](mdc:secure-password-ext/src/services/cross-iframe-positioner.service.ts)
- 安全的消息传递机制

### 原生消息传递
- **配置文件**：[secure-password-ext/public/com.secure_password.native_messaging_host_macos.json](mdc:secure-password-ext/public/com.secure_password.native_messaging_host_macos.json)
- 与 Tauri 主应用的安全通信
- 权限最小化原则

## 网站集成

### 网站服务
- **颜色服务**：[secure-password-ext/src/newtab/colorService.ts](mdc:secure-password-ext/src/newtab/colorService.ts)
- **图标服务**：[secure-password-ext/src/newtab/faviconService.ts](mdc:secure-password-ext/src/newtab/faviconService.ts)
- **历史服务**：[secure-password-ext/src/newtab/historyService.ts](mdc:secure-password-ext/src/newtab/historyService.ts)

### 网站适配
- 支持主流网站和服务
- 处理 SPA 和动态内容
- 兼容各种认证流程

## 安全考虑

### 内容脚本安全
- 严格的 CSP（内容安全策略）
- 防止 XSS 和代码注入
- 安全的 DOM 操作

### 数据隔离
- 扩展数据与网页数据完全隔离
- 敏感数据不在扩展内存储
- 通过原生消息传递获取数据

### 权限管理
- 最小权限原则
- 明确的权限声明
- 用户权限透明化

## 开发规范

### TypeScript 规范
- 严格的类型检查
- 使用 React + TypeScript
- 组件化开发

### 样式规范
- **TailwindCSS**：[secure-password-ext/src/styles/tailwind.css](mdc:secure-password-ext/src/styles/tailwind.css)
- 统一的设计语言
- 响应式设计

### 工具类
- **PostMessage 工具**：[secure-password-ext/src/utils/postMessageUtil.ts](mdc:secure-password-ext/src/utils/postMessageUtil.ts)
- **通用工具**：[secure-password-ext/src/utils/index.ts](mdc:secure-password-ext/src/utils/index.ts)

## 构建和部署

### 构建配置
- 支持多浏览器平台
- 优化打包大小
- 代码分割和懒加载

### 资源管理
- **图标资源**：[secure-password-ext/public/icons/](mdc:secure-password-ext/public/icons)
- **图片资源**：[secure-password-ext/public/img/](mdc:secure-password-ext/public/img)
- 优化资源加载性能

## 测试策略

### 单元测试
- 核心功能单元测试
- Mock 浏览器 API
- 异步操作测试

### 集成测试
- 与主应用的集成测试
- 表单检测和填充测试
- 跨浏览器兼容性测试

### 端到端测试
- 真实网站测试
- 用户流程测试
- 性能测试

## 用户体验

### 性能优化
- 快速启动和响应
- 最小化内存占用
- 后台处理优化

### 可访问性
- 键盘导航支持
- 屏幕阅读器兼容
- 高对比度模式

### 国际化
- 多语言支持
- 本地化适配
- 文化适应性

## 错误处理

### 错误监控
- 全局错误捕获
- 错误报告机制
- 用户友好的错误提示

### 降级策略
- 网络连接异常处理
- 主应用不可用时的降级
- 浏览器兼容性降级

## 更新和维护

### 版本管理
- **变更日志**：[secure-password-ext/CHANGELOG.md](mdc:secure-password-ext/CHANGELOG.md)
- 语义化版本控制
- 向后兼容性

### 发布流程
- 自动化构建和测试
- 多浏览器商店发布
- 逐步推送策略
