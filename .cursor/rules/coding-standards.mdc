---
description: 
globs: 
alwaysApply: false
---
# 编码规范规则

## 核心开发原则

### 🧪 测试驱动开发 (TDD)
- **测试覆盖率要求**: >95%
- **测试先行**: 新功能必须先编写测试，再实现代码
- **测试金字塔**: 单元测试 > 集成测试 > E2E 测试
- **持续验证**: 每个模块完成后立即进行功能验证

### 📦 模块化开发
1. **单一职责原则**: 每个模块功能独立，职责单一
2. **松耦合设计**: 模块间依赖关系清晰，易于测试
3. **增量交付**: 支持独立开发、测试和集成
4. **渐进式验证**: 每个模块完成后立即验证

## 通用规范
1. 使用中文注释和文档
2. 函数级注释必须包含功能说明、参数说明和返回值说明
3. **优先使用函数式编程**，编写纯函数和不可变数据结构
4. 避免复杂的函数或方法，单个函数不超过50行
5. 模块化实现，每个模块职责单一

## TypeScript/React 规范

### 基本原则
- 使用 React + TypeScript + Ant Design 技术栈
- **严格函数式编程**: 优先使用 React Hooks，完全避免 class 组件
- 使用 react-use 库提供的 hooks（如可用）
- 参考实现：[src/components/](mdc:src/components)、[src/hooks/](mdc:src/hooks)

### 函数式编程模式
```typescript
// ✅ 推荐：纯函数，无副作用
export const formatPassword = (password: string): string =>
  password.replace(/(.{4})/g, '$1 ').trim();

export const calculateStrength = (password: string): number =>
  password.length >= 8 ? Math.min(100, password.length * 5) : 0;

// ✅ 推荐：使用 map、filter、reduce
export const filterActiveCredentials = (credentials: Credential[]): Credential[] =>
  credentials.filter(cred => !cred.archived && !cred.deleted);

// ✅ 推荐：不可变数据操作
export const updateCredential = (
  credentials: Credential[], 
  id: string, 
  updates: Partial<Credential>
): Credential[] =>
  credentials.map(cred => 
    cred.id === id ? { ...cred, ...updates } : cred
  );
```

### 异步操作处理
```typescript
// ✅ 推荐：async/await 和完整错误处理
export async function encryptPassword(
  password: string,
  masterKey: string
): Promise<EncryptedData> {
  try {
    return await invoke('encrypt_password', {
      password,
      masterKey,
    });
  } catch (error) {
    console.error('密码加密失败:', error);
    throw new Error(`加密操作失败: ${error}`);
  }
}

// ✅ 推荐：Promise 链式处理和错误边界
export const processCredentials = (data: RawCredential[]) =>
  Promise.all(
    data.map(raw => 
      validateCredential(raw)
        .then(encryptSensitiveFields)
        .then(saveToStorage)
        .catch(error => {
          console.error(`处理凭据失败: ${raw.id}`, error);
          throw error;
        })
    )
  );
```

### 类型安全强化
```typescript
// ✅ 推荐：完整的类型定义和泛型约束
export interface CredentialConfig {
  readonly id: string;
  readonly title: string;
  readonly username: string;
  readonly website?: string;
  readonly category: CredentialCategory;
  readonly tags: readonly string[];
  readonly createdAt: number;
  readonly updatedAt: number;
}

// ✅ 推荐：严格的函数签名
export async function generateSecurePassword<T extends PasswordOptions>(
  options: T
): Promise<GeneratedPassword> {
  // 实现逻辑
}

// ❌ 避免：使用 any 类型
// ❌ 避免：可变的接口属性
```

### 状态管理
- 使用 React Context API，避免使用 Redux
- 上下文实现参考：[src/contexts/](mdc:src/contexts)
- **不可变状态**: 使用 `useState` 或 `useReducer`，严格避免直接修改状态
- **状态验证**: 每次状态变更都要进行类型和逻辑验证

### 测试要求
```typescript
// 每个组件都必须有对应测试
// ComponentName.test.tsx
describe('CredentialForm', () => {
  test('应该正确验证必填字段', () => {
    // 测试逻辑
  });
  
  test('应该正确处理异步提交', async () => {
    // 异步测试逻辑
  });
  
  test('应该正确处理错误状态', () => {
    // 错误场景测试
  });
});
```

## Rust 规范

### 函数式编程原则
```rust
// ✅ 推荐：纯函数，无副作用
/// 生成安全密码
/// 
/// # 参数
/// * `length` - 密码长度
/// * `options` - 密码生成选项
/// 
/// # 返回值
/// 包含密码和强度信息的 GeneratedPassword 结构
pub fn generate_secure_password(
    length: usize,
    options: &PasswordOptions,
) -> Result<GeneratedPassword, CryptoError> {
    // 纯函数实现
}

// ✅ 推荐：使用 Iterator 和函数式方法
pub fn filter_expired_credentials(
    credentials: &[Credential],
    current_time: u64,
) -> Vec<&Credential> {
    credentials
        .iter()
        .filter(|cred| cred.expires_at.map_or(true, |exp| exp > current_time))
        .collect()
}
```

### 错误处理模式
```rust
// ✅ 推荐：使用 Result 类型和 ? 操作符
pub fn decrypt_credential(
    encrypted_data: &str,
    key: &str,
) -> Result<Credential, CryptoError> {
    let decrypted_bytes = decrypt_aes_gcm(encrypted_data, key)
        .map_err(|e| CryptoError::DecryptionFailed(e.to_string()))?;
    
    serde_json::from_slice(&decrypted_bytes)
        .map_err(|e| CryptoError::DeserializationFailed(e.to_string()))
}

// ✅ 推荐：自定义错误类型转换
impl From<serde_json::Error> for CryptoError {
    fn from(err: serde_json::Error) -> Self {
        CryptoError::DeserializationFailed(err.to_string())
    }
}
```

### 性能优化
```rust
// ✅ 推荐：避免不必要的字符串克隆
pub fn create_credential_summary(config: &CredentialConfig) -> Result<Summary, Error> {
    Summary::new(
        config.title.as_str(),        // 避免克隆
        config.website.as_deref(),    // 智能借用
        config.category,              // Copy 类型
    )
}

// ✅ 推荐：使用 Box<str> 减少内存占用
pub struct CredentialMetadata {
    pub title: Box<str>,           // 不可变字符串
    pub website: Option<Box<str>>,
    pub created_at: u64,
}
```

### 命名约定
- 变量和函数：snake_case
- 类型和结构体：PascalCase
- 常量：SCREAMING_SNAKE_CASE
- 使用表达性强的变量名（如 `is_credential_valid`、`has_encryption_key`）

### 异步编程
- 使用 `tokio` 作为异步运行时
- 使用 `async fn` 语法实现异步函数
- 参考实现：[src-tauri/src/async_handler/](mdc:src-tauri/src/async_handler)

### 模块结构
- 每个模块都有 `mod.rs` 文件
- 模块内按功能划分子模块
- 参考结构：[src-tauri/src/](mdc:src-tauri/src)

## 测试规范

### 测试覆盖率要求
- **单元测试覆盖率**: >95%
- **集成测试覆盖率**: >90%
- **E2E 测试覆盖率**: >80%

### Rust 测试标准
```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    /// 测试密码生成功能
    #[test]
    fn test_password_generation() {
        let options = PasswordOptions::default();
        let result = generate_secure_password(12, &options);
        
        assert!(result.is_ok());
        let password = result.unwrap();
        assert_eq!(password.password.len(), 12);
        assert!(password.strength >= 80);
    }
    
    /// 测试错误处理
    #[test]
    fn test_invalid_password_length() {
        let options = PasswordOptions::default();
        let result = generate_secure_password(0, &options);
        
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), CryptoError::InvalidLength(_)));
    }
    
    /// 测试异步功能
    #[tokio::test]
    async fn test_async_encryption() {
        let data = "test_password";
        let key = "test_key";
        
        let encrypted = encrypt_data(data, key).await.expect("加密失败");
        let decrypted = decrypt_data(&encrypted, key).await.expect("解密失败");
        
        assert_eq!(data, decrypted);
    }
}
```

### TypeScript 测试标准
```typescript
// ComponentName.test.tsx
describe('PasswordGenerator', () => {
  test('应该生成指定长度的密码', async () => {
    const length = 16;
    const password = await generatePassword({ length });
    
    expect(password.value).toHaveLength(length);
    expect(password.strength).toBeGreaterThan(80);
  });
  
  test('应该正确处理生成失败', async () => {
    await expect(generatePassword({ length: -1 }))
      .rejects.toThrow('密码长度必须大于0');
  });
  
  test('应该正确计算密码强度', () => {
    const weakPassword = 'password';
    const strongPassword = 'P@ssw0rd!2023#Secure';
    
    expect(calculatePasswordStrength(weakPassword)).toBeLessThan(50);
    expect(calculatePasswordStrength(strongPassword)).toBeGreaterThan(90);
  });
});
```

### 性能测试
```rust
/// 性能基准测试
#[test]
fn test_encryption_performance() {
    let data = "test_password";
    let iterations = 1000;
    
    let start = Instant::now();
    
    for _ in 0..iterations {
        let _ = encrypt_aes_gcm(data, "test_key").expect("加密失败");
    }
    
    let duration = start.elapsed();
    let avg_duration = duration / iterations;
    
    // 要求：单次加密 < 1ms
    assert!(avg_duration.as_millis() < 1, 
        "加密平均耗时 {}ms 超过 1ms 要求", avg_duration.as_millis());
}
```

## 文档规范
- 每个模块都应有 README 或文档说明
- 重要实现应有对应的实现文档
- **API 文档**: 使用 rustdoc 和 JSDoc 生成
- **示例代码**: 每个公开 API 都必须有使用示例
- 文档统一放在各模块的 `docs/` 目录
- 参考文档：[docs/](mdc:docs)、[src-tauri/src/*/docs/](mdc:src-tauri/src)

## 安全规范
- 所有加密相关操作必须使用经过验证的库
- 敏感数据必须加密存储，使用后立即清理内存
- **输入验证**: 所有用户输入必须验证和清理
- **错误处理**: 不在错误信息中泄露敏感信息
- 参考安全实现：[src-tauri/src/crypto/](mdc:src-tauri/src/crypto)、[src-tauri/src/auth/](mdc:src-tauri/src/auth)

## 代码质量门禁

### 自动化检查
```bash
# 代码质量检查（必须全部通过）
cargo clippy -- -D warnings     # Rust 代码质量
cargo fmt --check               # 代码格式化
npm run lint                    # TypeScript 代码检查
npm run type-check              # 类型检查
```

### 测试要求
```bash
# 测试覆盖率检查
cargo test                      # 单元测试
cargo tarpaulin --out html      # 覆盖率报告
npm test -- --coverage          # 前端测试覆盖率
```

### 性能基准
- 密码生成: <1ms
- 数据加密: <5ms
- 数据库查询: <10ms
- 应用启动: <2s
