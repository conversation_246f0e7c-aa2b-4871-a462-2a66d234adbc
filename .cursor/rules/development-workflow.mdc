---
description: 
globs: 
alwaysApply: false
---
# 开发工作流规则

## 🎯 增量开发方法论

### 核心原则
1. **测试驱动开发 (TDD)**: 测试先行，代码跟进
2. **模块化开发**: 功能独立，接口清晰
3. **持续集成**: 频繁集成，快速反馈
4. **里程碑管理**: 分阶段交付，风险可控

### 开发节奏
- **每日**: 代码提交，单元测试，代码审查
- **每周**: 模块集成，功能验收，进度回顾
- **每2周**: 里程碑交付，产品演示，计划调整

## 📦 模块化开发流程

### 模块开发生命周期

#### 1. 模块规划阶段 (1天)
```markdown
## 模块规划检查清单
- [ ] 模块功能范围明确
- [ ] 接口设计完成
- [ ] 依赖关系分析
- [ ] 测试策略制定
- [ ] 性能要求确定
- [ ] 风险识别和应对
```

#### 2. TDD 开发阶段 (2-5天)
```rust
// 开发流程：红-绿-重构
// 1. 红色：编写失败的测试
#[test]
fn test_password_encryption_should_be_secure() {
    let password = "sensitive_password";
    let key = "encryption_key";
    
    // 这个测试会失败，因为函数还没实现
    let encrypted = encrypt_password(password, key).expect("加密失败");
    assert_ne!(password, encrypted);
    assert!(encrypted.len() > password.len()); // 加密后应该更长
}

// 2. 绿色：实现最简功能让测试通过
pub fn encrypt_password(password: &str, key: &str) -> Result<String, CryptoError> {
    // 最简实现，让测试通过
    aes_gcm_encrypt(password.as_bytes(), key.as_bytes())
        .map(base64::encode)
        .map_err(CryptoError::from)
}

// 3. 重构：优化代码质量
pub fn encrypt_password(password: &str, key: &str) -> Result<String, CryptoError> {
    validate_input(password, key)?;
    
    let derived_key = derive_encryption_key(key)?;
    let nonce = generate_secure_nonce()?;
    
    aes_gcm_encrypt_with_nonce(password.as_bytes(), &derived_key, &nonce)
        .map(|ciphertext| encode_encrypted_data(&ciphertext, &nonce))
        .map_err(CryptoError::from)
}
```

#### 3. 集成测试阶段 (1天)
```typescript
// 模块集成测试
describe('加密模块集成测试', () => {
  test('与认证模块集成', async () => {
    // 1. 用户认证
    const authResult = await authenticate('user', 'password');
    expect(authResult.success).toBe(true);
    
    // 2. 获取加密密钥
    const encryptionKey = authResult.session.encryptionKey;
    
    // 3. 加密敏感数据
    const sensitiveData = 'credit_card_number';
    const encrypted = await encryptData(sensitiveData, encryptionKey);
    
    // 4. 验证加密结果
    expect(encrypted).not.toBe(sensitiveData);
    
    // 5. 解密验证
    const decrypted = await decryptData(encrypted, encryptionKey);
    expect(decrypted).toBe(sensitiveData);
  });
});
```

#### 4. 模块验收阶段 (半天)
```bash
# 模块验收检查清单
□ 单元测试覆盖率 >95%
□ 集成测试全部通过
□ 性能基准测试达标
□ 安全检查通过
□ 代码审查完成
□ 文档更新完整
□ API 接口冻结
```

## 🚀 开发环境设置

### 环境要求
- **Node.js**: 18+ 版本
- **Rust**: 最新稳定版本 (1.70+)
- **Tauri CLI**: `cargo install tauri-cli`
- **系统要求**: macOS 12+, Windows 10+, Ubuntu 20.04+

### 一键环境初始化
```bash
# 项目初始化脚本
./scripts/setup-dev-env.sh

# 脚本内容包括：
# 1. 依赖检查和安装
# 2. 开发工具配置
# 3. Git hooks 设置
# 4. 数据库初始化
# 5. 测试环境准备
```

### 开发工具配置
```json
// .vscode/settings.json
{
  "rust-analyzer.cargo.features": ["development"],
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.associations": {
    "*.rs": "rust"
  }
}
```

## 🔄 开发模式

### 桌面端开发
```bash
# 启动开发服务器 (热重载)
npm run tauri dev

# 或者分离前后端开发
npm run dev          # 前端开发服务器
cargo tauri dev      # 后端开发模式
```

### 移动端开发
```bash
# iOS 开发 (需要 macOS)
npm run dev:ios
# 或
cargo tauri ios dev

# Android 开发
npm run dev:android
# 或  
cargo tauri android dev
```

### 浏览器扩展开发
```bash
# 进入扩展目录
cd secure-password-ext

# 安装依赖
npm install

# 开发模式 (热重载)
npm run dev

# 加载到浏览器进行测试
# Chrome: chrome://extensions/ → 加载已解压的扩展程序
```

## 🧪 测试驱动开发流程

### 每日 TDD 循环
```mermaid
graph LR
    A[编写测试] --> B[运行测试失败]
    B --> C[编写最小实现]
    C --> D[测试通过]
    D --> E[重构代码]
    E --> F[测试仍通过]
    F --> A
```

### 测试优先级
1. **单元测试** (每次提交)
   ```bash
   # Rust 单元测试
   cargo test --lib
   
   # TypeScript 单元测试  
   npm test -- --watch
   ```

2. **集成测试** (每日集成)
   ```bash
   # 后端集成测试
   cargo test integration
   
   # 前端集成测试
   npm run test:integration
   ```

3. **端到端测试** (里程碑完成)
   ```bash
   # E2E 测试
   npm run test:e2e
   
   # 移动端测试
   npm run test:mobile
   ```

### 测试数据管理
```rust
// 测试数据构建器模式
pub struct TestCredentialBuilder {
    credential: Credential,
}

impl TestCredentialBuilder {
    pub fn new() -> Self {
        Self {
            credential: Credential {
                id: uuid::Uuid::new_v4().to_string(),
                title: "Test Service".to_string(),
                username: "<EMAIL>".to_string(),
                password: "TestP@ssw0rd!".to_string(),
                created_at: chrono::Utc::now().timestamp(),
                ..Default::default()
            }
        }
    }
    
    pub fn with_title(mut self, title: &str) -> Self {
        self.credential.title = title.to_string();
        self
    }
    
    pub fn with_category(mut self, category: CredentialCategory) -> Self {
        self.credential.category = category;
        self
    }
    
    pub fn build(self) -> Credential {
        self.credential
    }
}
```

## 📋 代码质量检查

### Git Pre-commit Hooks
```bash
#!/bin/sh
# .git/hooks/pre-commit

echo "🔍 运行代码质量检查..."

# 1. Rust 代码检查
cargo clippy -- -D warnings || exit 1
cargo fmt --check || exit 1

# 2. TypeScript 代码检查
npm run lint || exit 1
npm run type-check || exit 1

# 3. 测试检查
cargo test --lib || exit 1
npm test -- --passWithNoTests || exit 1

echo "✅ 代码质量检查通过"
```

### 持续集成检查
```yaml
# .github/workflows/quality-check.yml
name: 代码质量检查

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  quality-check:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: 🦀 设置 Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        components: rustfmt, clippy
        
    - name: 🟢 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 📦 安装依赖
      run: |
        npm ci
        cargo build
        
    - name: 🔍 Rust 代码检查
      run: |
        cargo clippy -- -D warnings
        cargo fmt --check
        
    - name: 🔍 TypeScript 代码检查
      run: |
        npm run lint
        npm run type-check
        
    - name: 🧪 运行测试
      run: |
        cargo test
        npm test -- --coverage
        
    - name: 📊 上传覆盖率报告
      uses: codecov/codecov-action@v3
```

## 🏗️ 构建和部署

### 本地构建
```bash
# 开发构建
npm run build:dev

# 生产构建
npm run build:prod

# 全平台构建
npm run build:all
```

### 平台特定构建
```bash
# 桌面端构建
cargo tauri build

# 移动端构建
cargo tauri ios build --release
cargo tauri android build --release

# 浏览器扩展构建
cd secure-password-ext
npm run build
npm run package
```

### 部署流水线
```yaml
# .github/workflows/deploy.yml
name: 部署流水线

on:
  push:
    tags:
      - 'v*'

jobs:
  deploy-desktop:
    strategy:
      matrix:
        platform: [macos-latest, ubuntu-20.04, windows-latest]
    runs-on: ${{ matrix.platform }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: 🏗️ 构建桌面应用
      run: |
        npm ci
        npm run build
        cargo tauri build
        
    - name: 📦 打包发布文件
      uses: actions/upload-artifact@v3
      with:
        name: ${{ matrix.platform }}-artifacts
        path: src-tauri/target/release/bundle/
        
  deploy-mobile:
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: 🏗️ 构建 iOS 应用
      run: |
        cargo tauri ios build --release
        
    - name: 🤖 构建 Android 应用
      run: |
        cargo tauri android build --release
```

## 📈 版本管理和发布

### 语义化版本控制
```bash
# 版本号格式: MAJOR.MINOR.PATCH
# 例如: 1.2.3

# 重大更新 (破坏性变更)
npm version major

# 功能更新 (向后兼容)
npm version minor  

# 错误修复 (向后兼容)
npm version patch
```

### 发布流程
```bash
# 1. 创建发布分支
git checkout -b release/v1.2.0

# 2. 更新版本号
npm version minor
cargo set-version 1.2.0

# 3. 更新变更日志
echo "## [1.2.0] - $(date +%Y-%m-%d)" >> CHANGELOG.md

# 4. 运行完整测试
npm run test:all

# 5. 创建 PR 并合并
git push origin release/v1.2.0

# 6. 创建发布标签
git tag v1.2.0
git push origin v1.2.0
```

### 自动版本更新
```json
// package.json
{
  "scripts": {
    "version": "npm run build && git add -A src",
    "postversion": "git push && git push --tags"
  }
}
```

## 🤝 团队协作

### Git 工作流
```bash
# 主分支结构
main                 # 生产就绪代码
├── develop         # 开发集成分支
├── feature/*       # 功能开发分支
├── release/*       # 发布准备分支
└── hotfix/*        # 紧急修复分支
```

### 分支命名规范
```bash
# 功能分支
feature/auth-module
feature/password-generator
feature/mobile-ui

# 修复分支
fix/encryption-bug
fix/ui-layout-issue

# 发布分支
release/v1.2.0
release/v2.0.0-beta

# 热修复分支
hotfix/critical-security-fix
```

### 提交消息规范
```bash
# 格式: <类型>(<范围>): <描述>

feat(auth): 添加生物识别认证功能
fix(crypto): 修复AES-GCM加密padding错误
docs(api): 更新密码管理API文档
test(storage): 增加数据库并发测试用例
refactor(ui): 重构凭据列表组件
style(lint): 修复ESLint代码格式问题
chore(deps): 升级Tauri到2.0版本
```

### 代码审查流程
```markdown
## 代码审查检查清单

### 🔍 代码质量
- [ ] 代码遵循项目编码规范
- [ ] 函数和变量命名清晰易懂
- [ ] 代码逻辑清晰，注释充分
- [ ] 没有重复代码 (DRY原则)

### 🧪 测试覆盖
- [ ] 新功能有对应的单元测试
- [ ] 测试用例覆盖主要业务逻辑
- [ ] 边界条件和错误场景有测试
- [ ] 测试代码质量良好

### 🔒 安全检查
- [ ] 没有硬编码的敏感信息
- [ ] 输入验证充分
- [ ] 错误处理不泄露敏感信息
- [ ] 遵循安全编码规范

### 📈 性能考虑
- [ ] 没有明显的性能问题
- [ ] 算法复杂度合理
- [ ] 内存使用优化
- [ ] 异步操作正确处理
```

## 🚨 故障排查和调试

### 调试工具配置
```rust
// 开发环境调试配置
#[cfg(debug_assertions)]
fn setup_debug_logging() {
    env_logger::Builder::from_env(
        env_logger::Env::default().default_filter_or("debug")
    ).init();
    
    tauri::debug!("调试模式已启用");
}
```

### 常见问题排查
```bash
# 1. 构建失败
cargo clean && cargo build
npm cache clean --force && npm install

# 2. 测试失败
cargo test -- --nocapture  # 显示测试输出
npm test -- --verbose      # 详细测试信息

# 3. 性能问题
cargo build --release      # 发布版本构建
npm run analyze            # 包大小分析
```

### 日志和监控
```rust
// 生产环境日志配置
use tracing::{info, warn, error};

pub fn setup_production_logging() {
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .with_target(false)
        .init();
}

// 结构化日志记录
#[instrument]
pub async fn encrypt_credential(
    credential: &Credential,
    key: &str,
) -> Result<EncryptedCredential, CryptoError> {
    info!("开始加密凭据: {}", credential.id);
    
    let start = std::time::Instant::now();
    let result = perform_encryption(credential, key).await;
    let duration = start.elapsed();
    
    match &result {
        Ok(_) => info!("凭据加密成功，耗时: {:?}", duration),
        Err(e) => error!("凭据加密失败: {:?}", e),
    }
    
    result
}
```

## 质量门控
每个模块完成后必须通过：
1. **代码质量检查**：Clippy + fmt + TypeScript
2. **测试要求**：覆盖率 >90%，所有测试通过
3. **功能验证**：与路线图规范一致
4. **性能基准**：满足性能要求

这套完整的开发工作流确保高质量、高效率的模块化开发，支持测试驱动和持续集成的最佳实践。
