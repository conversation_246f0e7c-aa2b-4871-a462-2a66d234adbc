---
description: 
globs: 
alwaysApply: false
---
# 项目架构规则

## 项目概述
这是一个基于 Tauri + React + Ant Design 的跨平台密码管理器项目，支持桌面端、移动端和浏览器扩展。
采用测试驱动开发(TDD)和模块化设计，确保代码质量和系统可维护性。

## 🏗️ 核心架构设计

### 架构设计原则
1. **单一职责原则**: 每个模块专注一个核心功能
2. **依赖倒置原则**: 依赖抽象而非具体实现
3. **开闭原则**: 对扩展开放，对修改封闭
4. **接口隔离原则**: 客户端不应依赖不需要的接口
5. **测试驱动设计**: 代码设计以可测试性为优先

### 分层架构模式
```
┌─────────────────────────────────────────┐
│           用户界面层 (UI Layer)            │
│  React Components + Ant Design         │
├─────────────────────────────────────────┤
│          应用服务层 (App Layer)           │
│  Context API + Hooks + State Mgmt      │
├─────────────────────────────────────────┤
│          业务逻辑层 (Domain Layer)        │
│  Tauri Commands + Business Logic       │
├─────────────────────────────────────────┤
│         基础设施层 (Infrastructure)      │
│  Database + Crypto + OS Integration    │
└─────────────────────────────────────────┘
```

## 🎯 前端架构（React + TypeScript）

### 主要前端结构
- **主要前端代码**: [src/](mdc:src) 目录
- **技术栈**: React + TypeScript + Ant Design
- **状态管理**: React Context API + Hooks
- **主要组件入口**: [src/App.tsx](mdc:src/App.tsx)

### 多平台适配架构
```typescript
// 平台路由架构
interface PlatformConfig {
  platform: 'desktop' | 'mobile' | 'web';
  components: ComponentMap;
  features: FeatureSet;
}

// 平台特定实现
const platformConfigs = {
  desktop: {
    components: DesktopComponents,
    features: ['full-encryption', 'system-tray', 'auto-backup']
  },
  mobile: {
    components: MobileComponents,
    features: ['biometric-auth', 'quick-access', 'secure-keyboard']
  }
};
```

### 组件架构模式
```typescript
// 容器组件模式
export const CredentialsContainer: React.FC = () => {
  const { credentials, actions } = useCredentials();
  const { filters, setFilters } = useFilters();
  
  return (
    <CredentialsList
      credentials={credentials}
      filters={filters}
      onFilter={setFilters}
      onAction={actions}
    />
  );
};

// 展示组件模式
export const CredentialsList: React.FC<CredentialsListProps> = ({
  credentials,
  filters,
  onFilter,
  onAction
}) => {
  // 纯展示逻辑，无业务逻辑
  return (
    <List>
      {filteredCredentials.map(credential => (
        <CredentialItem
          key={credential.id}
          credential={credential}
          onEdit={onAction.edit}
          onDelete={onAction.delete}
        />
      ))}
    </List>
  );
};
```

## ⚙️ 后端架构（Tauri + Rust）

### 主要后端结构
- **Rust 代码位置**: [src-tauri/src/](mdc:src-tauri/src) 目录
- **主要入口文件**: [src-tauri/src/lib.rs](mdc:src-tauri/src/lib.rs)
- **配置文件**: [src-tauri/Cargo.toml](mdc:src-tauri/Cargo.toml)

### 模块化设计
```rust
// 模块依赖关系图
pub mod auth {          // 认证模块
    pub mod commands;   // Tauri 命令
    pub mod service;    // 业务逻辑
    pub mod models;     // 数据模型
    pub mod tests;      // 测试模块
}

pub mod crypto {        // 加密模块
    pub mod aes;        // AES 加密
    pub mod key_derivation; // 密钥派生
    pub mod secure_storage; // 安全存储
}

pub mod storage {       // 存储模块
    pub mod database;   // 数据库操作
    pub mod entities;   // 实体定义
    pub mod migrations; // 数据迁移
}
```

### 依赖注入架构
```rust
// 服务容器模式
pub struct ServiceContainer {
    pub auth_service: Arc<dyn AuthService>,
    pub crypto_service: Arc<dyn CryptoService>,
    pub storage_service: Arc<dyn StorageService>,
}

impl ServiceContainer {
    pub fn new() -> Self {
        Self {
            auth_service: Arc::new(AuthServiceImpl::new()),
            crypto_service: Arc::new(CryptoServiceImpl::new()),
            storage_service: Arc::new(StorageServiceImpl::new()),
        }
    }
}

// 命令处理器注入依赖
#[tauri::command]
pub async fn create_credential(
    container: tauri::State<'_, ServiceContainer>,
    request: CreateCredentialRequest,
) -> Result<String, String> {
    let encrypted = container
        .crypto_service
        .encrypt(&request.password)
        .await?;
    
    container
        .storage_service
        .save_credential(&request, &encrypted)
        .await
        .map_err(|e| e.to_string())
}
```

## 🔧 核心模块详解

### 1. 认证模块 ([src-tauri/src/auth/](mdc:src-tauri/src/auth))
**职责**: 用户认证和会话管理  
**核心功能**:
- 主密码验证
- 会话生命周期管理
- 生物识别集成
- 多因素认证

```rust
// 认证服务接口
#[async_trait]
pub trait AuthService: Send + Sync {
    async fn authenticate(&self, password: &str) -> Result<Session, AuthError>;
    async fn verify_session(&self, token: &str) -> Result<bool, AuthError>;
    async fn logout(&self, session_id: &str) -> Result<(), AuthError>;
}

// 会话管理
pub struct Session {
    pub id: String,
    pub user_id: String,
    pub encryption_key: Vec<u8>,
    pub expires_at: i64,
    pub permissions: Vec<Permission>,
}
```

### 2. 加密模块 ([src-tauri/src/crypto/](mdc:src-tauri/src/crypto))
**职责**: 数据加密和密钥管理  
**核心功能**:
- AES-GCM 对称加密
- 密钥派生 (PBKDF2/Argon2)
- 安全随机数生成
- 密钥轮换机制

```rust
// 加密服务架构
pub struct CryptoService {
    key_derivation: Box<dyn KeyDerivation>,
    cipher: Box<dyn Cipher>,
    rng: Box<dyn SecureRng>,
}

impl CryptoService {
    pub async fn encrypt_credential(
        &self,
        credential: &Credential,
        master_key: &[u8],
    ) -> Result<EncryptedCredential, CryptoError> {
        let derived_key = self.key_derivation.derive(master_key, &credential.salt)?;
        let nonce = self.rng.generate_nonce()?;
        
        let encrypted_data = self.cipher.encrypt(
            &serde_json::to_vec(credential)?,
            &derived_key,
            &nonce,
        )?;
        
        Ok(EncryptedCredential {
            id: credential.id.clone(),
            data: encrypted_data,
            nonce,
            algorithm: "AES-256-GCM".to_string(),
        })
    }
}
```

### 3. 存储模块 ([src-tauri/src/hybrid_storage/](mdc:src-tauri/src/hybrid_storage))
**职责**: 数据持久化和管理  
**核心功能**:
- 混合存储架构 (SQLite + 系统密钥链)
- ORM 数据模型
- 数据迁移
- 查询优化

```rust
// 存储抽象层
#[async_trait]
pub trait Repository<T>: Send + Sync {
    async fn create(&self, entity: &T) -> Result<String, StorageError>;
    async fn read(&self, id: &str) -> Result<Option<T>, StorageError>;
    async fn update(&self, id: &str, entity: &T) -> Result<(), StorageError>;
    async fn delete(&self, id: &str) -> Result<(), StorageError>;
    async fn list(&self, filters: &FilterCriteria) -> Result<Vec<T>, StorageError>;
}

// 仓储实现
pub struct CredentialRepository {
    pool: DbPool,
    cache: Arc<RwLock<LruCache<String, Credential>>>,
}

impl Repository<Credential> for CredentialRepository {
    async fn create(&self, credential: &Credential) -> Result<String, StorageError> {
        // 1. 数据验证
        self.validate_credential(credential)?;
        
        // 2. 持久化到数据库
        let id = self.insert_to_db(credential).await?;
        
        // 3. 更新缓存
        self.cache.write().await.put(id.clone(), credential.clone());
        
        Ok(id)
    }
}
```

### 4. 同步备份模块 ([src-tauri/src/sync_backup/](mdc:src-tauri/src/sync_backup))
**职责**: 数据同步和备份  
**核心功能**:
- 增量同步
- 冲突解决
- 版本控制
- 备份恢复

### 5. 移动端适配 ([src-tauri/src/mobile/](mdc:src-tauri/src/mobile))
**职责**: 移动端特定功能  
**核心功能**:
- 生物识别认证
- 设备信息收集
- 平台特定 API
- 性能优化

### 6. 浏览器扩展 ([secure-password-ext/](mdc:secure-password-ext))
**职责**: 浏览器集成  
**核心功能**:
- 自动填充
- 密码捕获
- 网站识别
- 原生消息传递

## 🌍 跨平台支持架构

### 平台检测和适配
```typescript
// 平台检测服务
export class PlatformDetector {
  static getPlatform(): Platform {
    if (typeof window !== 'undefined') {
      if (window.__TAURI__) {
        return window.__TAURI__.os.platform();
      }
      return 'web';
    }
    return 'server';
  }
  
  static isMobile(): boolean {
    const platform = this.getPlatform();
    return platform === 'ios' || platform === 'android';
  }
}

// 平台特定组件路由
export const PlatformRouter: React.FC = () => {
  const platform = usePlatform();
  
  switch (platform) {
    case 'desktop':
      return <DesktopApp />;
    case 'mobile':
      return <MobileApp />;
    case 'web':
      return <WebApp />;
    default:
      return <UnsupportedPlatform />;
  }
};
```

### 配置文件管理
- **桌面端权限**: [src-tauri/capabilities/default.json](mdc:src-tauri/capabilities/default.json)
- **移动端权限**: [src-tauri/capabilities/mobile.json](mdc:src-tauri/capabilities/mobile.json)
- **iOS 配置**: [src-tauri/gen/apple/](mdc:src-tauri/gen/apple)
- **Android 配置**: [src-tauri/gen/android/](mdc:src-tauri/gen/android)

## 🧪 测试架构设计

### 测试金字塔
```
       ┌─────────────┐
       │  E2E Tests  │  ← 少量，覆盖关键用户流程
       │     🔺      │
       └─────────────┘
     ┌─────────────────┐
     │Integration Tests│  ← 中等，覆盖模块交互
     │       🔺        │
     └─────────────────┘
   ┌───────────────────────┐
   │    Unit Tests         │  ← 大量，覆盖所有函数
   │         🔺            │
   └───────────────────────┘
```

### 测试策略
```rust
// 单元测试 - 快速，独立
#[cfg(test)]
mod tests {
    #[test]
    fn test_password_encryption() {
        // 测试单个函数
    }
}

// 集成测试 - 模块交互
#[tokio::test]
async fn test_auth_crypto_integration() {
    // 测试模块间集成
}

// 端到端测试 - 完整流程
#[tokio::test]
async fn test_complete_user_workflow() {
    // 测试完整用户场景
}
```

## 📊 代码组织原则

### 目录结构规范
```
src-tauri/src/
├── auth/                   # 认证模块
│   ├── commands.rs        # Tauri 命令
│   ├── service.rs         # 业务逻辑
│   ├── models.rs          # 数据模型
│   ├── errors.rs          # 错误定义
│   ├── tests/             # 测试模块
│   └── mod.rs             # 模块入口
├── crypto/                # 加密模块
├── storage/               # 存储模块
├── shared/                # 共享组件
└── lib.rs                 # 主入口
```

### 依赖关系规则
1. **向上依赖**: 低层模块不依赖高层模块
2. **接口隔离**: 通过 trait 定义接口
3. **循环依赖**: 严格禁止循环依赖
4. **外部依赖**: 集中在基础设施层

### 命名约定
- **模块名**: snake_case (如: `user_auth`)
- **结构体**: PascalCase (如: `UserSession`)  
- **函数**: snake_case (如: `encrypt_password`)
- **常量**: SCREAMING_SNAKE_CASE (如: `MAX_RETRY_COUNT`)

## 🔒 安全架构考虑

### 零知识架构
```rust
// 客户端加密，服务端不可见
pub struct ZeroKnowledgeVault {
    client_key: ClientKey,    // 仅客户端持有
    encrypted_data: Vec<u8>,  // 服务端存储
}

impl ZeroKnowledgeVault {
    pub fn encrypt_locally(&self, data: &[u8]) -> Result<Vec<u8>, Error> {
        // 数据永远不以明文离开客户端
        self.client_key.encrypt(data)
    }
}
```

### 威胁模型
1. **内存泄露防护**: 敏感数据使用后立即清零
2. **时间攻击防护**: 验证时间恒定
3. **侧信道攻击**: 避免基于时间/内存的信息泄露
4. **供应链安全**: 依赖审计和锁定

## 🚀 性能架构优化

### 缓存策略
```rust
// 多级缓存架构
pub struct CacheManager {
    l1_cache: LruCache<String, Credential>,      // 内存缓存
    l2_cache: DiskCache,                         // 磁盘缓存
    l3_cache: Arc<RemoteCache>,                  // 远程缓存
}

impl CacheManager {
    pub async fn get(&self, key: &str) -> Option<Credential> {
        // L1 -> L2 -> L3 -> Database 查找顺序
        if let Some(value) = self.l1_cache.get(key) {
            return Some(value.clone());
        }
        
        if let Some(value) = self.l2_cache.get(key).await {
            self.l1_cache.put(key.to_string(), value.clone());
            return Some(value);
        }
        
        None
    }
}
```

### 异步架构
```rust
// 非阻塞异步操作
pub async fn batch_encrypt_credentials(
    credentials: Vec<Credential>,
    key: &[u8],
) -> Result<Vec<EncryptedCredential>, Error> {
    // 并行处理，而非串行
    let tasks: Vec<_> = credentials
        .into_iter()
        .map(|cred| encrypt_credential_async(cred, key))
        .collect();
    
    futures::try_join_all(tasks).await
}
```

这套架构设计确保了系统的可扩展性、可维护性和高性能，同时支持测试驱动开发和模块化设计原则。
