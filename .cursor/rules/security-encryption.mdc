---
description: 
globs: 
alwaysApply: false
---
# 安全和加密规则

## 安全架构概述
这是一个密码管理器项目，安全性是最高优先级。所有敏感数据必须加密处理，遵循零知识架构原则。

## 加密模块

### 核心加密实现
- 主要加密逻辑：[src-tauri/src/crypto/](mdc:src-tauri/src/crypto)
- 密钥派生：[src-tauri/src/crypto/key_derivation.rs](mdc:src-tauri/src/crypto/key_derivation.rs)
- 兼容性处理：[src-tauri/src/crypto/compat.rs](mdc:src-tauri/src/crypto/compat.rs)

### 加密原则
1. **零知识架构**：服务端永远无法获取用户的明文数据
2. **端到端加密**：数据在客户端加密，在客户端解密
3. **密钥派生**：使用 PBKDF2/Argon2 进行密钥派生
4. **强随机性**：使用密码学安全的随机数生成器

## 安全存储

### 系统密钥链集成
- 基于操作系统原生密钥链的安全存储系统
- **macOS**: Keychain Services
- **Windows**: Windows Credential Manager  
- **Linux**: Secret Service (libsecret)
- **移动端**: iOS Keychain / Android Keystore
- 参考实现：[src-tauri/src/crypto/keychain.rs](mdc:src-tauri/src/crypto/keychain.rs)

### 密钥链存储架构
```rust
// 密钥链管理器 - 跨平台统一接口
pub struct KeychainManager {
    service: String,    // 服务标识: "secure-password"
    account: String,    // 账户标识: 用户邮箱/ID
}

// 支持的密钥类型
pub enum KeyType {
    MasterKey,      // 主密钥 (从用户密码派生)
    DerivedKey,     // 派生密钥
    EncryptionKey,  // 对称加密密钥
    Custom(String), // 自定义密钥类型
}
```

### 密钥存储策略
1. **主密钥存储**：
   - 服务名: `secure-password`
   - 账户名: `{contact}-master`
   - 内容: 从用户密码派生的主密钥 (32字节)

2. **对称密钥存储**：
   - 服务名: `secure-password`
   - 账户名: `{contact}-symmetric`
   - 内容: 用于凭据加密的对称密钥 (32字节)

3. **私钥存储**：
   - 服务名: `secure-password`
   - 账户名: `{contact}-private`
   - 内容: Base64编码的私钥字符串

4. **公钥存储**：
   - 服务名: `secure-password`
   - 账户名: `{contact}-public`
   - 内容: Base64编码的公钥字符串

### 平台特定实现
```rust
// macOS/Windows - 使用 keyring crate
#[cfg(any(target_os = "macos", target_os = "windows"))]
fn store_password(&self, password: &str) -> VaultResult<()> {
    use keyring::Entry;
    let entry = Entry::new(&self.service, &self.account)?;
    entry.set_password(password)?;
    Ok(())
}

// 移动端 - 使用平台特定安全存储
#[cfg(any(target_os = "android", target_os = "ios"))]
fn store_password(&self, password: &str) -> VaultResult<()> {
    let storage = MOBILE_SECURE_STORAGE.clone();
    let storage_key = format!("{}:{}", self.service, self.account);
    storage.store(&storage_key, password).await?;
    Ok(())
}
```

### 存储策略
- **敏感密钥**：使用系统密钥链存储（主密钥、对称密钥、私钥等）
- **加密数据**：使用本地数据库存储（已加密的凭据数据）  
- **元数据**：使用本地数据库存储（非敏感的结构化数据）
- **缓存数据**：使用内存缓存，应用关闭时清除

### 数据库安全
- 数据库实现：[src-tauri/src/hybrid_storage/](mdc:src-tauri/src/hybrid_storage)
- 敏感密钥存储在系统密钥链，数据库仅存储加密后的凭据数据
- 使用 SQLite 存储结构化数据和元数据
- 数据库连接和操作通过加密密钥保护

## 认证和授权

### 用户认证
- 认证模块：[src-tauri/src/auth/](mdc:src-tauri/src/auth)
- 主密码验证机制
- 生物识别集成（移动端）
- 会话管理系统

### 密钥管理流程
```rust
// 注册流程
pub async fn register_user(&self, contact: &str, password: &str) -> VaultResult<()> {
    // 1. 派生主密钥
    let master_key = derive_master_key(password, contact)?;
    
    // 2. 生成对称密钥和密钥对
    let symmetric_key = generate_symmetric_key()?;
    let keypair = generate_keypair()?;
    
    // 3. 存储所有密钥到系统密钥链
    let keychain = RegistrationKeychainManager::new(contact);
    keychain.store_master_key(&master_key)?;
    keychain.store_symmetric_key(&symmetric_key)?;
    keychain.store_private_key(&keypair.private_key)?;
    keychain.store_public_key(&keypair.public_key)?;
    
    Ok(())
}

// 登录流程
pub async fn login_user(&self, contact: &str, password: &str) -> VaultResult<Session> {
    // 1. 派生主密钥
    let master_key = derive_master_key(password, contact)?;
    
    // 2. 从密钥链获取存储的主密钥进行验证
    let keychain = RegistrationKeychainManager::new(contact);
    let stored_master_key = keychain.get_master_key()?;
    
    // 3. 验证密钥匹配
    if master_key != stored_master_key {
        return Err(VaultError::AuthenticationFailed);
    }
    
    // 4. 创建会话
    Ok(Session::new(contact, master_key))
}
```

## 安全开发规范

### 内存安全
```rust
use zeroize::Zeroize;

// 敏感数据结构必须实现 Zeroize
pub struct SecureKey {
    key: [u8; 32],
}

impl Zeroize for SecureKey {
    fn zeroize(&mut self) {
        self.key.zeroize();
    }
}

impl Drop for SecureKey {
    fn drop(&mut self) {
        self.zeroize();
    }
}
```

### 错误处理
```rust
// 不泄露敏感信息的错误处理
pub enum VaultError {
    AuthenticationFailed,           // 不具体说明原因
    KeychainError(String),         // 系统级错误可以显示
    InternalError(String),         // 内部错误，记录日志但不暴露给用户
}

// 记录日志但不在用户界面显示敏感信息
log::error!("密钥验证失败: 用户 {} 的主密钥不匹配", contact);
return Err(VaultError::AuthenticationFailed);
```

### 时间攻击防护
```rust
use subtle::ConstantTimeEq;

// 使用常量时间比较避免时间攻击
pub fn verify_key_constant_time(key1: &[u8], key2: &[u8]) -> bool {
    if key1.len() != key2.len() {
        return false;
    }
    key1.ct_eq(key2).into()
}
```

### 随机数安全
```rust
use rand::rngs::OsRng;
use rand::RngCore;

// 使用密码学安全的随机数生成器
pub fn generate_secure_nonce() -> VaultResult<[u8; 12]> {
    let mut nonce = [0u8; 12];
    OsRng.fill_bytes(&mut nonce);
    Ok(nonce)
}
```

## 安全审计要求

### 密钥链安全检查
1. **访问权限**：确保只有应用自身能访问存储的密钥
2. **数据完整性**：验证从密钥链读取的数据未被篡改
3. **错误处理**：密钥链不可用时的降级策略
4. **清理机制**：应用卸载时清理所有存储的密钥

### 测试要求
```rust
#[cfg(test)]
mod security_tests {
    #[test]
    fn test_keychain_isolation() {
        // 测试不同用户的密钥隔离
        // 测试恶意应用无法访问密钥
    }
    
    #[test]
    fn test_key_rotation() {
        // 测试密钥轮换机制
        // 测试旧密钥的安全清理
    }
    
    #[test]
    fn test_fallback_behavior() {
        // 测试密钥链不可用时的行为
        // 测试错误恢复机制
    }
}
```

### 合规性要求
- **GDPR合规**：用户数据删除时同步清理密钥链
- **SOC2合规**：密钥存储和访问的审计日志
- **FIPS 140-2**：加密算法和密钥管理符合标准
- **Common Criteria**：安全功能的评估和认证

## 威胁模型

### 密钥链特定威胁
1. **恶意应用攻击**：其他应用试图访问存储的密钥
2. **系统级攻击**：操作系统权限提升攻击
3. **物理访问攻击**：设备被物理访问时的密钥安全
4. **备份和同步风险**：系统备份可能包含密钥信息

### 缓解措施
1. **服务名唯一性**：使用唯一的服务标识符避免冲突
2. **账户名唯一性**：使用用户邮箱等唯一标识符
3. **元数据保护**：存储的元数据不包含敏感信息
4. **定期轮换**：支持密钥定期轮换机制

这套完整的安全架构确保密码管理器的核心安全需求，基于操作系统原生密钥链提供企业级安全保护。
