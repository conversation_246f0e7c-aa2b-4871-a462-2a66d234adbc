---
description: 
globs: 
alwaysApply: false
---
# Tauri 集成规则

## Tauri 架构概述
这是一个 Tauri 应用，前端使用 React，后端使用 Rust，支持桌面端和移动端。

## 前后端通信

### Command 系统
- Rust 命令定义在各模块的 `commands.rs` 文件中
- 所有命令必须使用 `#[tauri::command]` 注解
- 参考实现：[src-tauri/src/auth/commands.rs](mdc:src-tauri/src/auth/commands.rs)

### API 集成
- 前端 API 调用统一放在 [src/api/](mdc:src/api) 目录
- 使用 `@tauri-apps/api` 调用后端命令
- 混合 API 实现：[src/api/hybrid-api.ts](mdc:src/api/hybrid-api.ts)

### 错误处理
- 后端错误定义：[src-tauri/src/errors.rs](mdc:src-tauri/src/errors.rs)
- 前端错误处理应优雅降级
- 统一错误格式和处理流程

## 移动端支持

### 移动端配置
- iOS 配置：[src-tauri/gen/apple/](mdc:src-tauri/gen/apple)
- Android 配置：[src-tauri/gen/android/](mdc:src-tauri/gen/android)
- 移动端权限配置：[src-tauri/capabilities/mobile.json](mdc:src-tauri/capabilities/mobile.json)

### 移动端特定功能
- 生物识别：[src-tauri/src/mobile/biometric.rs](mdc:src-tauri/src/mobile/biometric.rs)
- 设备信息：[src-tauri/src/mobile/device_info.rs](mdc:src-tauri/src/mobile/device_info.rs)
- 移动端命令：[src-tauri/src/mobile/commands.rs](mdc:src-tauri/src/mobile/commands.rs)

### 前端移动端适配
- 移动端组件：[src/mobile/](mdc:src/mobile)
- 响应式布局和移动端 UI 优化
- 触摸手势和移动端交互

## 跨平台开发

### 平台检测
- 平台检测工具：[src/shared/utils/platform-detector.ts](mdc:src/shared/utils/platform-detector.ts)
- 平台路由：[src/shared/components/PlatformRouter.tsx](mdc:src/shared/components/PlatformRouter.tsx)

### 平台特定代码
- Windows：[src/platform/windows/](mdc:src/platform/windows)
- macOS：[src/platform/macos/](mdc:src/platform/macos)
- Linux：[src/platform/linux/](mdc:src/platform/linux)

## 构建和部署

### 桌面端构建
```bash
# 开发模式
cargo tauri dev

# 构建桌面应用
cargo tauri build
```

### 移动端构建
```bash
# iOS
cargo tauri ios dev
cargo tauri ios build

# Android
cargo tauri android dev
cargo tauri android build
```

### 开发脚本
- iOS 开发脚本：[scripts/dev-ios.sh](mdc:scripts/dev-ios.sh)
- iOS 签名设置：[scripts/setup-ios-signing.sh](mdc:scripts/setup-ios-signing.sh)

## 安全考虑

### 权限管理
- 桌面端权限：[src-tauri/capabilities/default.json](mdc:src-tauri/capabilities/default.json)
- 移动端权限：[src-tauri/capabilities/mobile.json](mdc:src-tauri/capabilities/mobile.json)

### 安全存储
- 使用系统密钥链进行安全存储
- 密钥链实现：[src-tauri/src/crypto/keychain.rs](mdc:src-tauri/src/crypto/keychain.rs)
- 跨平台适配器：[src-tauri/src/crypto/keychain_adapter.rs](mdc:src-tauri/src/crypto/keychain_adapter.rs)
- 加密模块：[src-tauri/src/crypto/](mdc:src-tauri/src/crypto)
- 避免在前端存储敏感数据

#### 密钥链 Tauri Commands
```rust
// 密钥存储命令
#[tauri::command]
pub async fn store_master_key(
    contact: String,
    master_key: Vec<u8>,
) -> Result<String, String> {
    let keychain = KeychainManager::new("secure-password", &format!("{}-master", contact))
        .map_err(|e| e.to_string())?;
    
    let key_array: [u8; 32] = master_key.try_into()
        .map_err(|_| "Invalid key size".to_string())?;
    
    keychain.store_key(&key_array)
        .map_err(|e| e.to_string())?;
    
    Ok("密钥存储成功".to_string())
}

// 密钥获取命令
#[tauri::command]
pub async fn get_master_key(contact: String) -> Result<Vec<u8>, String> {
    let keychain = KeychainManager::new("secure-password", &format!("{}-master", contact))
        .map_err(|e| e.to_string())?;
    
    let key = keychain.get_key()
        .map_err(|e| e.to_string())?;
    
    Ok(key.to_vec())
}
```

## 系统托盘集成

### 托盘功能
- 自定义托盘：[src-tauri/src/custom_tray/](mdc:src-tauri/src/custom_tray)
- 托盘配置：[src-tauri/src/tray/config.rs](mdc:src-tauri/src/tray/config.rs)
- 托盘事件处理：[src-tauri/src/tray/events.rs](mdc:src-tauri/src/tray/events.rs)

## 原生消息传递

### 浏览器扩展集成
- 原生消息处理：[src-tauri/src/native_messaging/](mdc:src-tauri/src/native_messaging)
- 浏览器扩展：[secure-password-ext/](mdc:secure-password-ext)
- 配置文件：[secure-password-ext/public/com.secure_password.native_messaging_host_macos.json](mdc:secure-password-ext/public/com.secure_password.native_messaging_host_macos.json)

## 开发最佳实践

### 开发环境设置
1. 确保 Rust 和 Node.js 环境已安装
2. 安装 Tauri CLI：`cargo install tauri-cli`
3. 安装前端依赖：`npm install`

### 调试技巧
- 使用 `tauri::debug!` 进行 Rust 端调试
- 前端可使用浏览器开发者工具
- 移动端调试需要使用对应平台的调试工具

### 性能优化
- 避免频繁的前后端通信
- 合理使用缓存机制
- 优化资源加载和打包大小
