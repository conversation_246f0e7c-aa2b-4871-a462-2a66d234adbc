---
description: 
globs: 
alwaysApply: false
---
# 测试策略规则

## 测试架构概览

基于测试驱动开发(TDD)的完整测试策略，确保每个模块都有完整的测试覆盖，测试覆盖率要求达到95%+。

## 测试分层策略

### 🧪 单元测试 (Unit Tests)
**目标**: 测试单个函数和模块的功能正确性  
**覆盖率要求**: >95%  
**执行频率**: 每次代码提交  

#### Rust 单元测试
```rust
// 位置: src/*/tests/ 或 #[cfg(test)] 模块
#[cfg(test)]
mod crypto_tests {
    use super::*;
    
    /// 测试 AES-GCM 加密功能
    #[test]
    fn test_aes_gcm_encryption() {
        let plaintext = "sensitive_password";
        let key = "test_encryption_key_32_bytes_long";
        
        let encrypted = encrypt_aes_gcm(plaintext, key)
            .expect("加密应该成功");
        let decrypted = decrypt_aes_gcm(&encrypted, key)
            .expect("解密应该成功");
        
        assert_eq!(plaintext, decrypted);
        assert_ne!(plaintext, encrypted); // 确保数据确实被加密
    }
    
    /// 测试密钥派生功能
    #[test]
    fn test_key_derivation() {
        let password = "master_password";
        let salt = "random_salt_bytes";
        let iterations = 100_000;
        
        let key1 = derive_key(password, salt, iterations).expect("密钥派生失败");
        let key2 = derive_key(password, salt, iterations).expect("密钥派生失败");
        
        assert_eq!(key1, key2); // 相同输入应产生相同输出
        assert_eq!(key1.len(), 32); // AES-256 密钥长度
    }
    
    /// 测试错误处理
    #[test]
    fn test_invalid_key_handling() {
        let plaintext = "test_data";
        let invalid_key = "too_short";
        
        let result = encrypt_aes_gcm(plaintext, invalid_key);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), CryptoError::InvalidKeyLength(_)));
    }
    
    /// 测试异步功能
    #[tokio::test]
    async fn test_async_credential_operations() {
        let credential = Credential::new("test", "user", "pass");
        
        let saved = save_credential(&credential).await.expect("保存失败");
        let loaded = load_credential(&saved.id).await.expect("加载失败");
        
        assert_eq!(credential.title, loaded.title);
        assert_eq!(credential.username, loaded.username);
    }
}
```

#### TypeScript 单元测试
```typescript
// 位置: src/**/*.test.ts
import { generateSecurePassword, calculatePasswordStrength } from '../utils/password';

describe('密码工具函数', () => {
  describe('generateSecurePassword', () => {
    test('应该生成指定长度的密码', () => {
      const length = 16;
      const password = generateSecurePassword({ length });
      
      expect(password.value).toHaveLength(length);
      expect(password.value).toMatch(/^[A-Za-z0-9!@#$%^&*()_+\-=\[\]{}|;:,.<>?]+$/);
    });
    
    test('应该包含指定的字符类型', () => {
      const options = {
        length: 20,
        includeNumbers: true,
        includeSymbols: true,
        includeUppercase: true,
        includeLowercase: true
      };
      
      const password = generateSecurePassword(options);
      
      expect(password.value).toMatch(/[0-9]/); // 包含数字
      expect(password.value).toMatch(/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/); // 包含符号
      expect(password.value).toMatch(/[A-Z]/); // 包含大写字母
      expect(password.value).toMatch(/[a-z]/); // 包含小写字母
    });
    
    test('应该正确处理无效参数', () => {
      expect(() => generateSecurePassword({ length: 0 }))
        .toThrow('密码长度必须大于0');
      
      expect(() => generateSecurePassword({ length: -1 }))
        .toThrow('密码长度必须大于0');
    });
  });
  
  describe('calculatePasswordStrength', () => {
    test('应该正确评估弱密码', () => {
      const weakPasswords = ['password', '123456', 'qwerty'];
      
      weakPasswords.forEach(password => {
        const strength = calculatePasswordStrength(password);
        expect(strength).toBeLessThan(30);
      });
    });
    
    test('应该正确评估强密码', () => {
      const strongPassword = 'MyStr0ng!P@ssw0rd#2023';
      const strength = calculatePasswordStrength(strongPassword);
      
      expect(strength).toBeGreaterThan(80);
    });
  });
});
```

### 🔗 集成测试 (Integration Tests)
**目标**: 测试模块间交互和完整工作流程  
**覆盖率要求**: >90%  
**位置**: `tests/integration_tests.rs` 和 `src/**/*.integration.test.ts`

```rust
/// 测试完整的凭据管理工作流程
#[tokio::test]
async fn test_credential_lifecycle() {
    // 1. 创建凭据
    let mut credential = Credential::new(
        "GitHub",
        "<EMAIL>",
        "P@ssw0rd!2023"
    );
    credential.website = Some("https://github.com".to_string());
    credential.category = CredentialCategory::Development;
    
    // 2. 加密并保存
    let master_key = "master_encryption_key";
    let encrypted_credential = encrypt_credential(&credential, master_key)
        .await.expect("凭据加密失败");
    
    let saved_id = save_encrypted_credential(&encrypted_credential)
        .await.expect("凭据保存失败");
    
    // 3. 加载并解密
    let loaded_encrypted = load_encrypted_credential(&saved_id)
        .await.expect("凭据加载失败");
    
    let decrypted_credential = decrypt_credential(&loaded_encrypted, master_key)
        .await.expect("凭据解密失败");
    
    // 4. 验证数据完整性
    assert_eq!(credential.title, decrypted_credential.title);
    assert_eq!(credential.username, decrypted_credential.username);
    assert_eq!(credential.password, decrypted_credential.password);
    assert_eq!(credential.website, decrypted_credential.website);
    
    // 5. 更新凭据
    let mut updated_credential = decrypted_credential;
    updated_credential.password = "NewP@ssw0rd!2024".to_string();
    
    let updated_encrypted = encrypt_credential(&updated_credential, master_key)
        .await.expect("凭据更新加密失败");
    
    update_encrypted_credential(&saved_id, &updated_encrypted)
        .await.expect("凭据更新失败");
    
    // 6. 验证更新
    let final_loaded = load_encrypted_credential(&saved_id)
        .await.expect("更新后凭据加载失败");
    
    let final_decrypted = decrypt_credential(&final_loaded, master_key)
        .await.expect("更新后凭据解密失败");
    
    assert_eq!(final_decrypted.password, "NewP@ssw0rd!2024");
    
    // 7. 删除凭据
    delete_credential(&saved_id).await.expect("凭据删除失败");
    
    let delete_result = load_encrypted_credential(&saved_id).await;
    assert!(delete_result.is_err()); // 应该找不到已删除的凭据
}

/// 测试 Tauri 命令接口集成
#[tokio::test]
async fn test_tauri_commands_integration() {
    let app = tauri::test::mock_app();
    let window = tauri::test::mock_window();
    
    // 测试密码生成命令
    let generated = commands::generate_password(
        app.state(),
        16,
        true,  // include_numbers
        true,  // include_symbols
        true,  // include_uppercase
        true,  // include_lowercase
    ).await.expect("密码生成命令失败");
    
    assert_eq!(generated.length, 16);
    assert!(generated.strength > 80);
    
    // 测试凭据保存命令
    let credential_data = CreateCredentialRequest {
        title: "Test Service".to_string(),
        username: "testuser".to_string(),
        password: generated.password.clone(),
        website: Some("https://example.com".to_string()),
        category: "web".to_string(),
    };
    
    let saved_id = commands::create_credential(
        app.state(),
        credential_data
    ).await.expect("凭据创建命令失败");
    
    // 测试凭据检索命令
    let retrieved = commands::get_credential(
        app.state(),
        saved_id.clone()
    ).await.expect("凭据检索命令失败");
    
    assert_eq!(retrieved.title, "Test Service");
    assert_eq!(retrieved.username, "testuser");
    
    // 测试凭据列表命令
    let credentials_list = commands::list_credentials(app.state())
        .await.expect("凭据列表命令失败");
    
    assert!(credentials_list.len() > 0);
    assert!(credentials_list.iter().any(|c| c.id == saved_id));
}
```

### ⚡ 性能测试 (Performance Tests)
**目标**: 验证性能要求和基准  
**位置**: `tests/performance_tests.rs`

```rust
use std::time::Instant;

/// 测试密码生成性能
#[test]
fn test_password_generation_performance() {
    let options = PasswordOptions {
        length: 16,
        include_numbers: true,
        include_symbols: true,
        include_uppercase: true,
        include_lowercase: true,
    };
    
    let iterations = 10000;
    let start = Instant::now();
    
    for _ in 0..iterations {
        let _ = generate_secure_password(&options).expect("密码生成失败");
    }
    
    let duration = start.elapsed();
    let avg_duration = duration / iterations;
    
    // 要求：单次密码生成 < 1ms
    assert!(avg_duration.as_millis() < 1, 
        "密码生成平均耗时 {}μs 超过 1ms 要求", avg_duration.as_micros());
    
    println!("密码生成性能: {}次平均 {:.2}μs", iterations, avg_duration.as_micros());
}

/// 测试数据库操作性能
#[tokio::test]
async fn test_database_performance() {
    let db = setup_test_database().await;
    let credentials: Vec<_> = (0..1000)
        .map(|i| Credential::new(&format!("Service {}", i), "user", "pass"))
        .collect();
    
    // 测试批量插入性能
    let start = Instant::now();
    
    for credential in &credentials {
        save_credential(&db, credential).await.expect("保存失败");
    }
    
    let insert_duration = start.elapsed();
    
    // 要求：单次插入 < 10ms
    let avg_insert = insert_duration / credentials.len() as u32;
    assert!(avg_insert.as_millis() < 10,
        "数据库插入平均耗时 {}ms 超过 10ms 要求", avg_insert.as_millis());
    
    // 测试查询性能
    let start = Instant::now();
    
    let all_credentials = load_all_credentials(&db).await.expect("查询失败");
    
    let query_duration = start.elapsed();
    
    // 要求：查询1000条记录 < 50ms
    assert!(query_duration.as_millis() < 50,
        "查询1000条记录耗时 {}ms 超过 50ms 要求", query_duration.as_millis());
    
    assert_eq!(all_credentials.len(), 1000);
    
    println!("数据库性能: 插入{}次平均{:.2}ms, 查询耗时{:.2}ms", 
        credentials.len(), avg_insert.as_secs_f64() * 1000.0, 
        query_duration.as_secs_f64() * 1000.0);
}

/// 测试加密性能
#[test]
fn test_encryption_performance() {
    let plaintext = "这是一个测试密码：P@ssw0rd!2023#Secure";
    let key = "test_encryption_key_32_bytes_long";
    let iterations = 1000;
    
    let start = Instant::now();
    
    for _ in 0..iterations {
        let encrypted = encrypt_aes_gcm(plaintext, key).expect("加密失败");
        let _decrypted = decrypt_aes_gcm(&encrypted, key).expect("解密失败");
    }
    
    let duration = start.elapsed();
    let avg_duration = duration / iterations;
    
    // 要求：单次加密+解密 < 5ms
    assert!(avg_duration.as_millis() < 5,
        "加密+解密平均耗时 {}ms 超过 5ms 要求", avg_duration.as_millis());
    
    println!("加密性能: {}次加密+解密平均 {:.2}ms", 
        iterations, avg_duration.as_secs_f64() * 1000.0);
}
```

### 🔄 兼容性测试 (Compatibility Tests)
**目标**: 确保跨平台和向后兼容  
**位置**: `tests/compatibility_tests.rs`

```rust
/// 测试跨平台兼容性
#[test]
fn test_cross_platform_compatibility() {
    let credential = Credential::new("Test", "user", "pass");
    
    // 测试序列化兼容性
    let serialized = serde_json::to_string(&credential).expect("序列化失败");
    let deserialized: Credential = serde_json::from_str(&serialized).expect("反序列化失败");
    
    assert_eq!(credential.title, deserialized.title);
    assert_eq!(credential.username, deserialized.username);
    
    // 测试加密数据跨平台兼容性
    let key = "test_key_32_bytes_for_compatibility";
    let encrypted = encrypt_aes_gcm(&credential.password, key).expect("加密失败");
    let decrypted = decrypt_aes_gcm(&encrypted, key).expect("解密失败");
    
    assert_eq!(credential.password, decrypted);
}

/// 测试数据库模式兼容性
#[tokio::test]
async fn test_database_schema_compatibility() {
    // 测试旧版本数据的加载
    let legacy_data = r#"
    {
        "id": "legacy-id",
        "title": "Legacy Service",
        "username": "legacy_user",
        "password": "legacy_pass",
        "created_at": 1640995200
    }
    "#;
    
    let parsed: Credential = serde_json::from_str(legacy_data).expect("解析旧数据失败");
    
    assert_eq!(parsed.title, "Legacy Service");
    assert_eq!(parsed.username, "legacy_user");
    
    // 确保能够保存和加载升级后的数据
    let db = setup_test_database().await;
    save_credential(&db, &parsed).await.expect("保存旧数据失败");
    
    let loaded = load_credential(&db, &parsed.id).await.expect("加载旧数据失败");
    assert_eq!(parsed.title, loaded.title);
}
```

### 🛡️ 安全测试 (Security Tests)
**目标**: 验证安全性和错误处理  
**位置**: `tests/security_tests.rs`

```rust
/// 测试输入验证和安全性
#[test]
fn test_input_validation_security() {
    // 测试 SQL 注入防护
    let malicious_input = "'; DROP TABLE credentials; --";
    let result = validate_credential_title(malicious_input);
    assert!(result.is_err(), "应该拒绝恶意输入");
    
    // 测试 XSS 防护
    let xss_input = "<script>alert('xss')</script>";
    let sanitized = sanitize_user_input(xss_input);
    assert!(!sanitized.contains("<script>"), "应该清理 XSS 输入");
    
    // 测试密码强度验证
    let weak_passwords = vec!["password", "123456", "qwerty"];
    for weak_password in weak_passwords {
        let strength = calculate_password_strength(weak_password);
        assert!(strength < 50, "弱密码强度应该低于50");
    }
}

/// 测试敏感数据处理
#[test]
fn test_sensitive_data_handling() {
    let credential = Credential::new("Test", "user", "sensitive_password");
    
    // 验证 Debug 输出不包含敏感信息
    let debug_output = format!("{:?}", credential);
    assert!(!debug_output.contains("sensitive_password"), 
        "Debug 输出不应包含敏感密码");
    assert!(debug_output.contains("[REDACTED]"), 
        "Debug 输出应该隐藏敏感信息");
    
    // 测试内存清理
    let mut password = "test_password".to_string();
    zero_memory(&mut password);
    assert_ne!(password, "test_password", "密码应该被清零");
}

/// 测试时间攻击防护
#[test]
fn test_timing_attack_resistance() {
    let correct_password = "correct_master_password";
    let wrong_passwords = vec![
        "wrong_password_1",
        "wrong_password_2",
        "completely_different_length",
        "",
    ];
    
    let mut correct_times = Vec::new();
    let mut wrong_times = Vec::new();
    
    // 测试多次验证时间
    for _ in 0..100 {
        // 正确密码验证时间
        let start = Instant::now();
        let _result = verify_master_password(correct_password, "stored_hash");
        correct_times.push(start.elapsed());
        
        // 错误密码验证时间
        for wrong_password in &wrong_passwords {
            let start = Instant::now();
            let _result = verify_master_password(wrong_password, "stored_hash");
            wrong_times.push(start.elapsed());
        }
    }
    
    let avg_correct_time: Duration = correct_times.iter().sum::<Duration>() / correct_times.len() as u32;
    let avg_wrong_time: Duration = wrong_times.iter().sum::<Duration>() / wrong_times.len() as u32;
    
    // 验证时间差异不超过 10%（防时间攻击）
    let time_diff_ratio = (avg_correct_time.as_nanos() as f64 - avg_wrong_time.as_nanos() as f64).abs() 
        / avg_correct_time.as_nanos() as f64;
    
    assert!(time_diff_ratio < 0.1, 
        "密码验证时间差异过大，可能存在时间攻击风险: {:.2}%", time_diff_ratio * 100.0);
}
```

## 端到端测试 (E2E Tests)

### 前端 E2E 测试
**工具**: Playwright 或 Cypress  
**位置**: `e2e/tests/`

```typescript
// e2e/credential-management.e2e.ts
import { test, expect } from '@playwright/test';

test.describe('凭据管理流程', () => {
  test('完整的凭据生命周期', async ({ page }) => {
    // 1. 启动应用并登录
    await page.goto('http://localhost:1420');
    
    await page.fill('[data-testid="master-password"]', 'test_master_password');
    await page.click('[data-testid="login-button"]');
    
    await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
    
    // 2. 创建新凭据
    await page.click('[data-testid="add-credential-button"]');
    
    await page.fill('[data-testid="credential-title"]', 'GitHub');
    await page.fill('[data-testid="credential-username"]', '<EMAIL>');
    await page.fill('[data-testid="credential-password"]', 'P@ssw0rd!2023');
    await page.fill('[data-testid="credential-website"]', 'https://github.com');
    
    await page.click('[data-testid="save-credential-button"]');
    
    // 3. 验证凭据已保存
    await expect(page.locator('[data-testid="credential-item"]').filter({ hasText: 'GitHub' }))
      .toBeVisible();
    
    // 4. 编辑凭据
    await page.click('[data-testid="credential-item"]').filter({ hasText: 'GitHub' });
    await page.click('[data-testid="edit-credential-button"]');
    
    await page.fill('[data-testid="credential-password"]', 'NewP@ssw0rd!2024');
    await page.click('[data-testid="save-credential-button"]');
    
    // 5. 验证凭据已更新
    await page.click('[data-testid="credential-item"]').filter({ hasText: 'GitHub' });
    await page.click('[data-testid="show-password-button"]');
    
    await expect(page.locator('[data-testid="credential-password-display"]'))
      .toHaveText('NewP@ssw0rd!2024');
    
    // 6. 搜索功能测试
    await page.fill('[data-testid="search-input"]', 'GitHub');
    await expect(page.locator('[data-testid="credential-item"]')).toHaveCount(1);
    
    // 7. 删除凭据
    await page.click('[data-testid="credential-item"]').filter({ hasText: 'GitHub' });
    await page.click('[data-testid="delete-credential-button"]');
    await page.click('[data-testid="confirm-delete-button"]');
    
    await expect(page.locator('[data-testid="credential-item"]').filter({ hasText: 'GitHub' }))
      .not.toBeVisible();
  });
  
  test('密码生成功能', async ({ page }) => {
    await page.goto('http://localhost:1420');
    await page.fill('[data-testid="master-password"]', 'test_master_password');
    await page.click('[data-testid="login-button"]');
    
    // 打开密码生成器
    await page.click('[data-testid="password-generator-button"]');
    
    // 设置密码选项
    await page.selectOption('[data-testid="password-length"]', '16');
    await page.check('[data-testid="include-numbers"]');
    await page.check('[data-testid="include-symbols"]');
    
    // 生成密码
    await page.click('[data-testid="generate-password-button"]');
    
    // 验证生成的密码
    const generatedPassword = await page.textContent('[data-testid="generated-password"]');
    expect(generatedPassword).toHaveLength(16);
    expect(generatedPassword).toMatch(/[0-9]/); // 包含数字
    expect(generatedPassword).toMatch(/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/); // 包含符号
    
    // 验证密码强度
    const strengthText = await page.textContent('[data-testid="password-strength"]');
    expect(strengthText).toMatch(/强|Strong/);
  });
});
```

## 测试执行策略

### 本地开发测试
```bash
# 快速测试（开发时频繁运行）
cargo test --lib                    # 只运行单元测试
npm test -- --watch                 # 前端测试监视模式

# 完整测试（提交前运行）
cargo test                          # 所有 Rust 测试
cargo test --release performance    # 性能测试
npm test -- --coverage              # 前端测试 + 覆盖率

# 端到端测试
npm run test:e2e                    # E2E 测试
```

### CI/CD 测试流水线
```yaml
# .github/workflows/test.yml
name: 测试套件

on: [push, pull_request]

jobs:
  unit-tests:
    strategy:
      matrix:
        platform: [ubuntu-latest, windows-latest, macos-latest]
    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v4
      - name: 运行 Rust 单元测试
        run: |
          cargo test --lib --verbose
          cargo clippy -- -D warnings
          cargo fmt --check
      
      - name: 运行 TypeScript 单元测试
        run: |
          npm ci
          npm run lint
          npm test -- --coverage --watchAll=false
  
  integration-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: 运行集成测试
        run: |
          cargo test integration_tests
          
  performance-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: 运行性能测试
        run: |
          cargo test --release performance_tests
          
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: 运行 E2E 测试
        run: |
          npm run build
          npm run test:e2e
          
  coverage:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: 生成覆盖率报告
        run: |
          cargo tarpaulin --out xml --output-dir coverage
          npm test -- --coverage --coverageReporters=lcov
          bash <(curl -s https://codecov.io/bash)
```

## 测试数据管理

### 测试数据生成
```rust
// tests/fixtures/mod.rs
pub struct TestDataBuilder;

impl TestDataBuilder {
    pub fn credential() -> CredentialBuilder {
        CredentialBuilder::default()
    }
    
    pub fn user() -> UserBuilder {
        UserBuilder::default()
    }
}

pub struct CredentialBuilder {
    credential: Credential,
}

impl Default for CredentialBuilder {
    fn default() -> Self {
        Self {
            credential: Credential {
                id: uuid::Uuid::new_v4().to_string(),
                title: "Test Service".to_string(),
                username: "<EMAIL>".to_string(),
                password: "TestP@ssw0rd!2023".to_string(),
                website: Some("https://example.com".to_string()),
                category: CredentialCategory::Web,
                created_at: 1640995200,
                updated_at: 1640995200,
                ..Default::default()
            }
        }
    }
}

impl CredentialBuilder {
    pub fn title(mut self, title: &str) -> Self {
        self.credential.title = title.to_string();
        self
    }
    
    pub fn username(mut self, username: &str) -> Self {
        self.credential.username = username.to_string();
        self
    }
    
    pub fn password(mut self, password: &str) -> Self {
        self.credential.password = password.to_string();
        self
    }
    
    pub fn build(self) -> Credential {
        self.credential
    }
}
```

## 质量门控

### 测试通过标准
1. **单元测试**: 100% 通过，覆盖率 >95%
2. **集成测试**: 所有关键业务流程测试通过
3. **性能测试**: 满足所有性能基准要求
4. **安全测试**: 通过所有安全检查
5. **E2E 测试**: 主要用户流程测试通过

### 覆盖率要求
- **Rust 代码覆盖率**: >95%
- **TypeScript 代码覆盖率**: >95%
- **功能覆盖率**: >90%
- **边界条件覆盖**: >85%

### 测试报告
每次测试运行后自动生成：
- HTML 覆盖率报告
- 性能基准报告
- 安全测试摘要
- E2E 测试截图和视频

这套完整的测试策略确保代码质量和系统可靠性，支持敏捷开发和持续交付。
