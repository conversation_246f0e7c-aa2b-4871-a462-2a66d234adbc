# API 设计规范文档

## 文档概述

本文档定义了 Native Messaging 企业级独立守护进程系统的完整 API 接口规范，包括守护进程、Tauri 主应用、浏览器扩展之间的所有通信接口。

**相关文档**:
- [增量开发路线图](./NATIVE_MESSAGING_INCREMENTAL_ROADMAP.md)
- [跨平台实施计划](./NATIVE_MESSAGING_CROSS_PLATFORM_IMPLEMENTATION_PLAN.md)
- [技术栈选型指南](./TECHNOLOGY_STACK_GUIDE.md)
- [开发环境设置指南](./DEVELOPMENT_ENVIRONMENT_SETUP.md)

## 🏗️ API 架构概览

```
┌─────────────────────────────────────────────────────────────────┐
│                        API 层次架构                             │
├─────────────────────────────────────────────────────────────────┤
│  🌐 浏览器扩展 API (TypeScript)                                 │
│  ├─ Native Messaging Protocol API                              │
│  └─ Browser Extension Runtime API                              │
├─────────────────────────────────────────────────────────────────┤
│  📡 守护进程 API (Rust)                                         │
│  ├─ Native Messaging Host API                                  │
│  ├─ IPC Server API                                             │
│  ├─ System Service API                                         │
│  └─ Security Proxy API                                         │
├─────────────────────────────────────────────────────────────────┤
│  🎨 Tauri 主应用 API (Rust)                                     │
│  ├─ IPC Client API                                             │
│  ├─ Business Logic API                                         │
│  ├─ Storage Management API                                     │
│  └─ Configuration API                                          │
├─────────────────────────────────────────────────────────────────┤
│  📚 共享核心库 API (Rust)                                       │
│  ├─ Protocol Definition API                                    │
│  ├─ Type System API                                            │
│  ├─ Validation API                                             │
│  └─ Utility API                                                │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 守护进程核心 API

### 1. 守护进程服务 API

#### 1.1 服务生命周期管理

```rust
/// 守护进程服务主接口
#[async_trait]
pub trait DaemonService: Send + Sync {
    /// 启动守护进程服务
    /// 
    /// 初始化并启动所有子服务组件，包括系统服务注册、IPC服务器、
    /// Native Messaging Host 和应用管理器
    /// 
    /// # 参数
    /// - `config`: 守护进程配置
    /// 
    /// # 返回
    /// - `Ok(())`: 启动成功
    /// - `Err(DaemonError)`: 启动失败，包含详细错误信息
    /// 
    /// # 错误类型
    /// - `DaemonError::ConfigInvalid`: 配置无效
    /// - `DaemonError::ServiceRegistrationFailed`: 系统服务注册失败
    /// - `DaemonError::IpcServerStartFailed`: IPC服务器启动失败
    /// 
    /// # 示例
    /// ```rust
    /// let config = DaemonConfig::load_from_file("daemon.toml").await?;
    /// let daemon = SecurePasswordDaemon::new();
    /// daemon.start(config).await?;
    /// ```
    async fn start(&mut self, config: DaemonConfig) -> Result<(), DaemonError>;

    /// 优雅关闭守护进程
    /// 
    /// 按顺序关闭所有子服务，确保数据完整性和资源清理
    /// 
    /// # 超时控制
    /// - 默认超时: 30秒
    /// - 强制终止: 超时后强制关闭所有服务
    /// 
    /// # 关闭顺序
    /// 1. 停止接受新连接
    /// 2. 等待现有请求完成
    /// 3. 关闭 IPC 服务器
    /// 4. 关闭 Native Messaging Host
    /// 5. 停止应用管理器
    /// 6. 注销系统服务
    async fn shutdown(&mut self) -> Result<(), DaemonError>;

    /// 重新加载配置
    /// 
    /// 在不重启服务的情况下重新加载配置文件
    /// 
    /// # 支持热重载的配置项
    /// - IPC 连接参数
    /// - Native Messaging 设置
    /// - 安全策略配置
    /// - 监控告警配置
    /// 
    /// # 不支持热重载的配置项
    /// - 系统服务配置
    /// - 基础网络绑定配置
    async fn reload_config(&mut self, config: DaemonConfig) -> Result<(), DaemonError>;

    /// 获取服务健康状态
    /// 
    /// 返回详细的服务运行状态和性能指标
    /// 
    /// # 返回信息包含
    /// - 服务运行状态
    /// - 各子服务状态
    /// - 性能指标统计
    /// - 错误和警告信息
    async fn get_health_status(&self) -> DaemonHealthStatus;

    /// 获取服务统计信息
    /// 
    /// 返回详细的运行时统计数据
    async fn get_statistics(&self) -> DaemonStatistics;
}
```

#### 1.2 系统服务集成 API

```rust
/// 跨平台系统服务管理接口
#[async_trait]
pub trait SystemServiceManager: Send + Sync {
    /// 安装系统服务
    /// 
    /// 在目标平台上安装守护进程作为系统服务
    /// 
    /// # 平台支持
    /// - Windows: Windows Service
    /// - macOS: LaunchDaemon
    /// - Linux: systemd service
    /// 
    /// # 权限要求
    /// - Windows: 管理员权限
    /// - macOS: sudo 权限
    /// - Linux: root 权限或 systemd 用户权限
    async fn install_service(&self, config: &ServiceInstallConfig) -> Result<(), ServiceError>;

    /// 卸载系统服务
    /// 
    /// 从系统中完全移除守护进程服务
    async fn uninstall_service(&self) -> Result<(), ServiceError>;

    /// 启动系统服务
    async fn start_service(&self) -> Result<(), ServiceError>;

    /// 停止系统服务
    async fn stop_service(&self) -> Result<(), ServiceError>;

    /// 重启系统服务
    async fn restart_service(&self) -> Result<(), ServiceError>;

    /// 获取服务状态
    /// 
    /// # 返回状态
    /// - `ServiceStatus::Running`: 服务正在运行
    /// - `ServiceStatus::Stopped`: 服务已停止
    /// - `ServiceStatus::Starting`: 服务正在启动
    /// - `ServiceStatus::Stopping`: 服务正在停止
    /// - `ServiceStatus::Error(String)`: 服务异常
    async fn get_service_status(&self) -> Result<ServiceStatus, ServiceError>;

    /// 配置自动启动
    /// 
    /// 设置服务开机自动启动
    async fn enable_auto_start(&self) -> Result<(), ServiceError>;

    /// 禁用自动启动
    async fn disable_auto_start(&self) -> Result<(), ServiceError>;
}
```

### 2. IPC 通信引擎 API

#### 2.1 IPC 服务器 API

```rust
/// IPC 服务器接口
#[async_trait]
pub trait IpcServer: Send + Sync {
    /// 启动 IPC 服务器
    /// 
    /// 根据配置启动相应的传输层服务器
    /// 
    /// # 传输层选择
    /// - Windows: Named Pipe (优先) 或 TCP
    /// - macOS/Linux: Unix Domain Socket (优先) 或 TCP
    /// - 跨平台: TCP Socket
    async fn start(&mut self, config: IpcServerConfig) -> Result<(), IpcError>;

    /// 停止 IPC 服务器
    async fn stop(&mut self) -> Result<(), IpcError>;

    /// 发送消息到指定客户端
    /// 
    /// # 参数
    /// - `client_id`: 客户端连接标识符
    /// - `message`: 要发送的 IPC 消息
    /// 
    /// # 错误处理
    /// - 客户端不存在: 返回 `IpcError::ClientNotFound`
    /// - 连接已断开: 返回 `IpcError::ConnectionClosed`
    /// - 序列化失败: 返回 `IpcError::SerializationFailed`
    async fn send_to_client(&self, client_id: &str, message: IpcMessage) -> Result<(), IpcError>;

    /// 广播消息到所有客户端
    /// 
    /// 向所有活跃的客户端连接发送消息
    async fn broadcast(&self, message: IpcMessage) -> Result<(), IpcError>;

    /// 获取活跃连接列表
    /// 
    /// 返回当前所有活跃的客户端连接信息
    async fn get_active_connections(&self) -> Vec<IpcConnectionInfo>;

    /// 断开指定客户端连接
    async fn disconnect_client(&self, client_id: &str) -> Result<(), IpcError>;

    /// 注册消息处理器
    /// 
    /// 为特定消息类型注册处理器
    /// 
    /// # 参数
    /// - `message_type`: 消息类型标识符
    /// - `handler`: 消息处理器实现
    async fn register_handler(
        &mut self, 
        message_type: IpcMessageType, 
        handler: Box<dyn IpcMessageHandler>
    ) -> Result<(), IpcError>;

    /// 获取服务器统计信息
    async fn get_server_stats(&self) -> IpcServerStats;
}
```

#### 2.2 IPC 消息协议 API

```rust
/// IPC 消息定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcMessage {
    /// 消息唯一标识符
    pub message_id: String,
    
    /// 消息类型
    pub message_type: IpcMessageType,
    
    /// 消息负载数据
    pub payload: serde_json::Value,
    
    /// 消息时间戳 (Unix 时间戳，毫秒)
    pub timestamp: u64,
    
    /// 是否需要响应
    pub response_required: bool,
    
    /// 消息优先级
    pub priority: MessagePriority,
    
    /// 消息来源标识
    pub source: String,
    
    /// 消息目标标识 (可选)
    pub target: Option<String>,
    
    /// 消息元数据
    pub metadata: HashMap<String, String>,
}

/// IPC 消息类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum IpcMessageType {
    // 应用生命周期管理
    LaunchApp,
    ShutdownApp,
    RestartApp,
    AppStatus,
    AppHealthCheck,
    
    // Native Messaging 代理
    BrowserRequest,
    BrowserResponse,
    BrowserConnectionStatus,
    
    // 安全验证
    AuthRequest,
    AuthResponse,
    SecurityAlert,
    
    // 配置管理
    ConfigUpdate,
    ConfigQuery,
    ConfigValidation,
    
    // 系统监控
    HealthCheck,
    MetricsReport,
    PerformanceAlert,
    
    // 自定义消息类型
    Custom(String),
}

/// 消息优先级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum MessagePriority {
    Low = 1,
    Normal = 2,
    High = 3,
    Critical = 4,
}

/// IPC 响应消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcResponse {
    /// 对应的请求消息 ID
    pub request_id: String,
    
    /// 响应状态
    pub status: ResponseStatus,
    
    /// 响应数据
    pub data: serde_json::Value,
    
    /// 错误信息 (如果有)
    pub error: Option<String>,
    
    /// 响应时间戳
    pub timestamp: u64,
    
    /// 处理耗时 (毫秒)
    pub processing_time: u64,
}

/// 响应状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ResponseStatus {
    Success,
    Error,
    Timeout,
    NotFound,
    Unauthorized,
    RateLimited,
}
```

### 3. Native Messaging 代理 API

#### 3.1 Native Messaging Host API

```rust
/// Native Messaging Host 接口
#[async_trait]
pub trait NativeMessagingHost: Send + Sync {
    /// 启动 Native Messaging Host
    /// 
    /// 初始化标准输入/输出监听，注册浏览器 Host 配置
    async fn start(&mut self, config: NativeMessagingConfig) -> Result<(), NativeMessagingError>;

    /// 停止 Native Messaging Host
    async fn stop(&mut self) -> Result<(), NativeMessagingError>;

    /// 注册浏览器 Host 配置
    /// 
    /// 自动检测系统中安装的浏览器并注册相应的 Host 配置
    /// 
    /// # 支持的浏览器
    /// - Google Chrome
    /// - Mozilla Firefox
    /// - Microsoft Edge
    /// - Safari (macOS only)
    /// - Chromium
    /// - Brave Browser
    /// - Opera
    async fn register_browser_hosts(&self) -> Result<Vec<BrowserRegistration>, NativeMessagingError>;

    /// 处理浏览器扩展请求
    /// 
    /// 接收来自浏览器扩展的请求并转发给 Tauri 主应用
    /// 
    /// # 处理流程
    /// 1. 验证请求来源和格式
    /// 2. 安全检查和净化
    /// 3. 转换为 IPC 消息格式
    /// 4. 转发给主应用
    /// 5. 处理响应并返回给浏览器
    async fn handle_browser_request(&self, request: BrowserRequest) -> Result<BrowserResponse, NativeMessagingError>;

    /// 获取已注册的浏览器列表
    async fn get_registered_browsers(&self) -> Vec<BrowserInfo>;

    /// 获取活跃的浏览器连接
    async fn get_active_browser_connections(&self) -> Vec<BrowserConnectionInfo>;

    /// 获取 Native Messaging 统计信息
    async fn get_statistics(&self) -> NativeMessagingStats;
}
```

#### 3.2 浏览器请求/响应协议 API

```rust
/// 浏览器扩展请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserRequest {
    /// 请求唯一标识符
    pub request_id: String,
    
    /// 扩展 ID
    pub extension_id: String,
    
    /// 消息类型
    pub message_type: String,
    
    /// 请求负载数据
    pub payload: serde_json::Value,
    
    /// 浏览器类型
    pub browser: BrowserType,
    
    /// 请求时间戳
    pub timestamp: u64,
    
    /// 请求来源 URL (可选)
    pub origin_url: Option<String>,
    
    /// 请求优先级
    pub priority: RequestPriority,
    
    /// 超时时间 (毫秒)
    pub timeout: Option<u64>,
}

/// 浏览器响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserResponse {
    /// 对应的请求 ID
    pub request_id: String,
    
    /// 响应状态
    pub status: ResponseStatus,
    
    /// 响应数据
    pub payload: serde_json::Value,
    
    /// 错误信息 (如果有)
    pub error: Option<String>,
    
    /// 响应时间戳
    pub timestamp: u64,
    
    /// 处理耗时 (毫秒)
    pub processing_time: u64,
    
    /// 响应元数据
    pub metadata: HashMap<String, String>,
}

/// 浏览器类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum BrowserType {
    Chrome,
    Firefox,
    Edge,
    Safari,
    Chromium,
    Brave,
    Opera,
    Unknown(String),
}

/// 请求优先级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum RequestPriority {
    Background = 1,
    Normal = 2,
    UserInitiated = 3,
    Critical = 4,
}
```

## 🎨 Tauri 主应用 API

### 1. IPC 客户端 API

#### 1.1 守护进程连接 API

```rust
/// IPC 客户端接口
#[async_trait]
pub trait IpcClient: Send + Sync {
    /// 连接到守护进程
    /// 
    /// 建立与守护进程的 IPC 连接
    /// 
    /// # 连接参数
    /// - `config`: IPC 连接配置
    /// 
    /// # 连接流程
    /// 1. 检测守护进程是否运行
    /// 2. 选择最佳传输方式
    /// 3. 建立连接
    /// 4. 执行身份验证
    /// 5. 注册消息处理器
    async fn connect(&mut self, config: IpcConnectionConfig) -> Result<(), IpcClientError>;

    /// 断开连接
    async fn disconnect(&mut self) -> Result<(), IpcClientError>;

    /// 发送消息到守护进程
    /// 
    /// # 参数
    /// - `message`: 要发送的 IPC 消息
    /// 
    /// # 返回
    /// - 如果 `response_required` 为 true，等待并返回响应
    /// - 如果 `response_required` 为 false，立即返回发送结果
    async fn send_message(&self, message: IpcMessage) -> Result<Option<IpcResponse>, IpcClientError>;

    /// 发送请求并等待响应
    /// 
    /// 便捷方法，自动设置 `response_required = true` 并等待响应
    async fn send_request(&self, message: IpcMessage) -> Result<IpcResponse, IpcClientError>;

    /// 注册消息处理器
    /// 
    /// 注册处理来自守护进程的消息的处理器
    async fn register_handler(
        &mut self, 
        message_type: IpcMessageType, 
        handler: Box<dyn IpcMessageHandler>
    ) -> Result<(), IpcClientError>;

    /// 获取连接状态
    async fn get_connection_status(&self) -> ConnectionStatus;

    /// 启用自动重连
    /// 
    /// 当连接断开时自动尝试重新连接
    async fn enable_auto_reconnect(&mut self, config: ReconnectConfig) -> Result<(), IpcClientError>;
}
```
