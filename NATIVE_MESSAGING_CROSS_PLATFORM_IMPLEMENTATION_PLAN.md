# Native Messaging 企业级独立守护进程跨平台实现计划

## 项目概述

基于**独立守护进程架构**构建企业级的跨平台 Native Messaging 系统。守护进程作为系统服务独立运行，负责浏览器扩展通信代理、Tauri 应用生命周期管理和企业级安全验证。该系统提供7x24小时稳定运行能力，支持静默启动、自动故障恢复和全平台兼容。

## 🏗️ 独立守护进程架构设计

```
┌─────────────────────────────────────────────────────────────────────────┐
│                     企业级独立守护进程 (系统服务)                        │
│  ┌─────────────────────────────────────────────────────────────────────┐ │
│  │                        守护进程核心服务                             │ │
│  │                                                                     │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │ │
│  │  │  Native Messaging│  │   IPC 通信引擎   │  │  应用生命周期    │     │ │
│  │  │      代理        │  │                │  │     管理器      │     │ │
│  │  │ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │     │ │
│  │  │ │Chrome/Firefox│ │  │ │TCP/Socket  │ │  │ │启动/监控    │ │     │ │
│  │  │ │Edge/Safari  │ │  │ │Named Pipe  │ │  │ │故障恢复     │ │     │ │
│  │  │ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │     │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘     │ │
│  │                                                                     │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │ │
│  │  │  企业级安全代理  │  │   性能监控告警   │  │  系统服务集成    │     │ │
│  │  │                │  │                │  │                │     │ │
│  │  │ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │     │ │
│  │  │ │权限验证     │ │  │ │健康检查     │ │  │ │Windows Service│ │     │ │
│  │  │ │审计日志     │ │  │ │性能指标     │ │  │ │macOS LaunchD │ │     │ │
│  │  │ │加密通信     │ │  │ │告警通知     │ │  │ │Linux systemd │ │     │ │
│  │  │ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │     │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘     │ │
│  │                                                                     │ │
│  └─────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────┘
                                     ↓ 
                           标准化 IPC 通信协议
                                     ↓
┌─────────────────────────────────────────────────────────────────────────┐
│                           Tauri 主应用程序                              │
│  ┌─────────────────────────────────────────────────────────────────────┐ │
│  │                         应用核心功能                                │ │
│  │                                                                     │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │ │
│  │  │    用户界面      │  │   业务逻辑处理   │  │   数据存储管理   │     │ │
│  │  │                │  │                │  │                │     │ │
│  │  │ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │     │ │
│  │  │ │React/Vue UI │ │  │ │密码管理     │ │  │ │加密存储     │ │     │ │
│  │  │ │响应式设计   │ │  │ │凭证处理     │ │  │ │数据库ORM    │ │     │ │
│  │  │ │多主题支持   │ │  │ │同步备份     │ │  │ │备份恢复     │ │     │ │
│  │  │ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │     │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘     │ │
│  │                                                                     │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │ │
│  │  │   IPC 客户端     │  │   配置管理      │  │   插件系统      │     │ │
│  │  │                │  │                │  │                │     │ │
│  │  │ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │     │ │
│  │  │ │守护进程通信 │ │  │ │用户设置     │ │  │ │扩展支持     │ │     │ │
│  │  │ │消息处理     │ │  │ │主题配置     │ │  │ │API接口      │ │     │ │
│  │  │ │状态同步     │ │  │ │安全策略     │ │  │ │第三方集成   │ │     │ │
│  │  │ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │     │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘     │ │
│  │                                                                     │ │
│  └─────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────┘
```

## 设计原则

### 1. 独立服务架构
- **进程隔离**：守护进程和主应用独立运行，互不影响
- **系统服务级别**：支持开机自启动和系统级权限管理
- **静默运行**：无用户界面，完全后台运行
- **生命周期独立**：守护进程管理主应用的启动、监控和关闭

### 2. 企业级可靠性
- **7x24小时运行**：系统服务级别的高可用性
- **自动故障恢复**：守护进程自动重启和错误恢复
- **零停机维护**：支持热重载配置和无缝升级
- **审计合规**：完整的操作日志和审计跟踪

### 3. 跨平台兼容性
- **统一架构**：相同的核心逻辑，平台特定的实现层
- **原生集成**：深度集成各平台的系统服务机制
- **标准化通信**：使用平台最佳的 IPC 通信方式
- **一致体验**：跨平台的统一用户体验和管理接口

### 4. 测试驱动开发 (TDD)
- **测试先行**：所有功能必须先写测试，再写实现
- **覆盖率要求**：>95% 测试覆盖率
- **性能测试**：并发处理能力和响应时间基准
- **安全测试**：输入验证和权限控制验证

## 核心功能模块

### 🔧 独立守护进程项目
**位置**: `src-tauri/daemon/`  
**功能**: 系统服务、Native Messaging 代理、应用管理

#### 1. 系统服务集成
- **Windows Service**: 完整的 Windows 系统服务支持
- **macOS LaunchDaemon**: macOS 守护进程集成
- **Linux systemd**: Linux 系统服务管理
- **自动安装**: 跨平台自动安装和配置脚本

#### 2. Native Messaging 代理
- **浏览器Host注册**: 自动注册 Chrome/Firefox/Edge/Safari Host
- **消息代理**: 浏览器扩展请求代理到主应用
- **协议转换**: 标准 Native Messaging 协议到 IPC 协议转换
- **并发处理**: 支持多浏览器同时连接和请求处理

#### 3. IPC 通信引擎
- **跨平台传输**: TCP/Unix Socket/Named Pipe 自适应选择
- **消息序列化**: JSON/MessagePack/Protobuf 多格式支持
- **连接管理**: 连接池、负载均衡、故障转移
- **性能优化**: 异步非阻塞、批量处理、缓存机制

#### 4. 应用生命周期管理
- **静默启动**: 无界面的 Tauri 应用启动
- **健康监控**: 应用状态监控和自动故障恢复
- **资源管理**: CPU、内存使用监控和优化
- **版本管理**: 应用更新检查和无缝升级

#### 5. 双重安全防护体系

##### 5.1 守护进程内部安全代理
- **系统级安全**：进程隔离、资源保护、权限控制
- **IPC 通信安全**：连接认证、通道加密、会话管理
- **进程安全管理**：应用验证、内存保护、代码签名
- **内部审计系统**：事件记录、合规检查、取证收集

##### 5.2 Native Messaging 协议安全组件
- **浏览器扩展认证**：扩展ID验证、证书链验证、来源验证
- **协议安全防护**：消息净化、格式验证、版本控制
- **威胁防护系统**：注入检测、异常分析、DoS防护
- **协议合规监控**：审计跟踪、标准检查、安全报告

### 🎨 Tauri 主应用项目
**位置**: `src-tauri/src/`  
**功能**: 用户界面、业务逻辑、数据管理

#### 1. 用户界面层
- **现代化界面**: React/Vue 响应式设计
- **多主题支持**: 明暗主题、自定义主题
- **无障碍访问**: 完整的可访问性支持
- **多语言支持**: 国际化和本地化

#### 2. 业务逻辑处理
- **密码管理**: 生成、存储、检索、更新
- **凭证管理**: 登录信息、安全笔记、支付信息
- **同步备份**: 云端同步、本地备份、版本控制
- **安全策略**: 密码策略、访问控制、数据保护

#### 3. IPC 客户端
- **守护进程通信**: 连接到守护进程 IPC 服务
- **消息处理**: 处理来自浏览器扩展的请求
- **状态同步**: 与守护进程同步应用状态
- **错误处理**: 连接失败时的降级处理

### 📚 共享核心库
**位置**: `src/native_messaging/`  
**功能**: 协议定义、通用组件、工具函数

#### 1. 协议定义
- **消息格式**: 统一的消息结构和类型定义
- **版本管理**: 协议版本兼容性和升级机制
- **验证规则**: 消息验证和安全检查
- **错误码**: 标准化的错误代码和处理

## 🏗️ 文件结构设计

```
secure-password/
├── 🔧 独立守护进程项目
│   └── src-tauri/daemon/
│       ├── Cargo.toml                    # 守护进程独立项目配置
│       ├── src/
│       │   ├── main.rs                   # 守护进程入口点
│       │   ├── daemon_core.rs            # 核心守护进程逻辑
│       │   ├── config/                   # 配置管理
│       │   │   ├── mod.rs
│       │   │   ├── daemon_config.rs      # 守护进程配置
│       │   │   └── validation.rs         # 配置验证
│       │   ├── platform/                 # 平台特定实现
│       │   │   ├── mod.rs                # 平台抽象接口
│       │   │   ├── windows/              # Windows 系统服务
│       │   │   │   ├── service.rs
│       │   │   │   ├── registry.rs
│       │   │   │   └── installer.rs
│       │   │   ├── macos/                # macOS LaunchDaemon
│       │   │   │   ├── launchd.rs
│       │   │   │   ├── plist_manager.rs
│       │   │   │   └── installer.rs
│       │   │   └── linux/                # Linux systemd 服务
│       │   │       ├── systemd.rs
│       │   │       ├── service_manager.rs
│       │   │       └── installer.rs
│       │   ├── ipc/                      # IPC 通信引擎
│       │   │   ├── mod.rs
│       │   │   ├── server.rs             # IPC 服务器
│       │   │   ├── client.rs             # IPC 客户端
│       │   │   ├── protocol.rs           # IPC 协议定义
│       │   │   ├── transport/            # 传输层实现
│       │   │   │   ├── tcp.rs            # TCP 传输
│       │   │   │   ├── unix_socket.rs    # Unix Socket (macOS/Linux)
│       │   │   │   └── named_pipe.rs     # Named Pipe (Windows)
│       │   │   └── codec/                # 编解码器
│       │   │       ├── json.rs           # JSON 编解码
│       │   │       ├── msgpack.rs        # MessagePack 编解码
│       │   │       └── protobuf.rs       # Protobuf 编解码
│       │   ├── native_messaging/         # Native Messaging 代理
│       │   │   ├── mod.rs
│       │   │   ├── host.rs               # Native Messaging Host
│       │   │   ├── proxy.rs              # 请求代理和转发
│       │   │   ├── browser/              # 浏览器适配
│       │   │   │   ├── chrome.rs
│       │   │   │   ├── firefox.rs
│       │   │   │   ├── edge.rs
│       │   │   │   └── safari.rs
│       │   │   └── registry.rs           # 浏览器注册管理
│       │   ├── app_manager/              # 应用生命周期管理
│       │   │   ├── mod.rs
│       │   │   ├── launcher.rs           # 应用启动器
│       │   │   ├── monitor.rs            # 健康监控
│       │   │   ├── lifecycle.rs          # 生命周期管理
│       │   │   └── recovery.rs           # 故障恢复
│       │   ├── security/                 # 企业级安全代理
│       │   │   ├── mod.rs
│       │   │   ├── auth.rs               # 权限验证
│       │   │   ├── audit.rs              # 审计日志
│       │   │   ├── encryption.rs         # 加密通信
│       │   │   └── policy.rs             # 安全策略
│       │   ├── monitoring/               # 性能监控告警
│       │   │   ├── mod.rs
│       │   │   ├── metrics.rs            # 性能指标
│       │   │   ├── health.rs             # 健康检查
│       │   │   ├── alerts.rs             # 告警系统
│       │   │   └── dashboard.rs          # 监控面板
│       │   ├── error.rs                  # 错误处理
│       │   └── utils/                    # 工具函数
│       │       ├── logging.rs            # 日志管理
│       │       └── signal_handler.rs     # 信号处理
│       ├── resources/                    # 系统服务配置
│       │   ├── windows/
│       │   │   ├── service_config.xml    # Windows 服务配置
│       │   │   └── installer.bat         # 安装脚本
│       │   ├── macos/
│       │   │   ├── com.securepassword.daemon.plist
│       │   │   └── install_macos.sh      # 安装脚本
│       │   └── linux/
│       │       ├── secure-password-daemon.service
│       │       └── install_linux.sh      # 安装脚本
│       ├── tests/                        # 测试文件
│       │   ├── integration_tests.rs
│       │   ├── platform_tests.rs
│       │   ├── ipc_tests.rs
│       │   ├── native_messaging_tests.rs
│       │   └── performance_tests.rs
│       └── build.rs                      # 构建脚本
│
├── 🎨 Tauri 主应用项目
│   └── src-tauri/src/
│       ├── lib.rs                        # Tauri 应用库入口
│       ├── main.rs                       # 应用入口点
│       ├── ipc_client/                   # IPC 客户端
│       │   ├── mod.rs
│       │   ├── daemon_client.rs          # 守护进程通信客户端
│       │   ├── message_handler.rs        # 消息处理器
│       │   └── connection_manager.rs     # 连接管理
│       ├── business/                     # 业务逻辑
│       │   ├── mod.rs
│       │   ├── password_manager.rs       # 密码管理
│       │   ├── credential_manager.rs     # 凭证管理
│       │   ├── sync_manager.rs           # 同步管理
│       │   └── backup_manager.rs         # 备份管理
│       ├── storage/                      # 数据存储
│       │   ├── mod.rs
│       │   ├── database.rs               # 数据库管理
│       │   ├── encryption.rs             # 数据加密
│       │   └── migrations.rs             # 数据迁移
│       ├── config/                       # 应用配置
│       │   ├── mod.rs
│       │   ├── app_config.rs             # 应用配置
│       │   └── user_settings.rs          # 用户设置
│       └── utils/                        # 工具函数
│           ├── crypto.rs                 # 加密工具
│           └── validation.rs             # 验证工具
│
└── 📚 共享核心库
    └── src/native_messaging/
        ├── mod.rs                        # 模块导出
        ├── protocol/                     # 协议定义
        │   ├── mod.rs
        │   ├── message.rs                # 消息格式定义
        │   ├── validator.rs              # 协议验证
        │   └── codec.rs                  # 编解码器
        ├── types/                        # 类型定义
        │   ├── mod.rs
        │   ├── browser.rs                # 浏览器类型
        │   ├── ipc.rs                    # IPC 类型
        │   └── error.rs                  # 错误类型
        ├── config/                       # 配置定义
        │   ├── mod.rs
        │   └── shared_config.rs          # 共享配置
        └── utils/                        # 工具函数
            ├── mod.rs
            ├── crypto.rs                 # 加密工具
            ├── validation.rs             # 验证工具
            └── serialization.rs          # 序列化工具
```

## 核心API设计

### 🔧 独立守护进程 API (daemon/src/main.rs)
```rust
/// 启动企业级守护进程服务
///
/// 初始化并启动独立的系统级守护进程，提供7x24小时稳定运行能力
///
/// # 参数
/// - `config`: 守护进程配置信息
///
/// # 返回
/// Result<DaemonService> - 守护进程服务实例
///
/// # 错误
/// 当系统服务注册失败或配置无效时返回错误
pub async fn start_daemon_service(
    config: DaemonConfig
) -> Result<DaemonService>

/// 注册 Native Messaging Host
///
/// 自动检测并注册所有支持的浏览器 Native Messaging Host
///
/// # 返回
/// Result<Vec<BrowserRegistration>> - 各浏览器注册结果
///
/// # 错误
/// 当浏览器注册表访问失败时返回错误
pub async fn register_all_browser_hosts(&self) -> Result<Vec<BrowserRegistration>>

/// 启动 IPC 通信服务器
///
/// 启动跨平台 IPC 服务器，支持与 Tauri 主应用的安全通信
///
/// # 参数
/// - `bind_config`: IPC 绑定配置
///
/// # 返回
/// Result<IpcServer> - IPC 服务器实例
pub async fn start_ipc_server(
    &self,
    bind_config: IpcBindConfig
) -> Result<IpcServer>

/// 启动应用生命周期管理器
///
/// 管理 Tauri 主应用的启动、监控和故障恢复
///
/// # 参数
/// - `app_path`: Tauri 应用可执行文件路径
///
/// # 返回
/// Result<AppLifecycleManager> - 应用管理器实例
pub async fn start_app_manager(
    &self,
    app_path: PathBuf
) -> Result<AppLifecycleManager>

/// 获取守护进程健康状态
///
/// 返回守护进程的详细健康状态和性能指标
///
/// # 返回
/// DaemonHealthStatus - 健康状态信息
pub async fn get_health_status(&self) -> DaemonHealthStatus

/// 优雅关闭守护进程
///
/// 安全关闭所有服务并清理系统资源
///
/// # 返回
/// Result<()> - 关闭成功或失败
pub async fn shutdown_gracefully(&self) -> Result<()>
```

### 🎨 Tauri 主应用 API (src-tauri/src/ipc_client/)
```rust
/// 连接到守护进程 IPC 服务
///
/// 建立与独立守护进程的安全 IPC 连接
///
/// # 参数
/// - `connection_config`: IPC 连接配置
///
/// # 返回
/// Result<DaemonClient> - 守护进程客户端实例
///
/// # 错误
/// 当守护进程不可达或认证失败时返回错误
pub async fn connect_to_daemon(
    connection_config: IpcConnectionConfig
) -> Result<DaemonClient>

/// 注册浏览器消息处理器
///
/// 注册处理来自浏览器扩展请求的消息处理器
///
/// # 参数
/// - `message_type`: 要处理的消息类型
/// - `handler`: 消息处理器实例
///
/// # 返回
/// Result<()> - 注册成功或失败
pub async fn register_browser_message_handler(
    &mut self,
    message_type: BrowserMessageType,
    handler: Box<dyn BrowserMessageHandler>
) -> Result<()>

/// 向守护进程发送状态更新
///
/// 将应用状态变更通知给守护进程
///
/// # 参数
/// - `status_update`: 状态更新信息
///
/// # 返回
/// Result<()> - 发送成功或失败
pub async fn send_status_update(
    &self,
    status_update: AppStatusUpdate
) -> Result<()>

/// 处理守护进程命令
///
/// 处理从守护进程接收的管理命令
///
/// # 参数
/// - `command`: 守护进程命令
///
/// # 返回
/// Result<CommandResponse> - 命令执行结果
pub async fn handle_daemon_command(
    &self,
    command: DaemonCommand
) -> Result<CommandResponse>
```

### TypeScript API
```typescript
// 浏览器端接口
export interface NativeMessagingClient {
  // 发送消息到应用
  sendMessage(message: NativeMessage): Promise<NativeResponse>
  
  // 监听应用推送的消息
  onMessage(callback: (message: NativeMessage) => void): void
  
  // 连接状态管理
  connect(): Promise<void>
  disconnect(): void
  getConnectionStatus(): ConnectionStatus
}

// 消息类型定义
export interface NativeMessage {
  type: string
  requestId: string
  payload: any
  timestamp: number
  source: string
}
```

## 数据模型设计

### 独立守护进程数据结构
```rust
/// 守护进程主配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DaemonConfig {
    pub service_config: ServiceConfig,
    pub ipc_config: IpcConfig,
    pub native_messaging_config: NativeMessagingConfig,
    pub app_management_config: AppManagementConfig,
    pub security_config: SecurityConfig,
    pub monitoring_config: MonitoringConfig,
}

/// 系统服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    pub service_name: String,
    pub display_name: String,
    pub description: String,
    pub auto_start: bool,
    pub restart_policy: RestartPolicy,
    pub run_as_user: Option<String>,
}

/// IPC 通信配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcConfig {
    pub transport: IpcTransport,
    pub bind_address: String,
    pub max_connections: usize,
    pub connection_timeout: Duration,
    pub encryption_enabled: bool,
    pub authentication_required: bool,
}

/// Native Messaging 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NativeMessagingConfig {
    pub auto_register_browsers: bool,
    pub allowed_extensions: Vec<String>,
    pub message_size_limit: usize,
    pub concurrent_connections: usize,
    pub browser_detection_timeout: Duration,
}

/// 应用管理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppManagementConfig {
    pub app_path: PathBuf,
    pub launch_args: Vec<String>,
    pub health_check_interval: Duration,
    pub auto_restart: bool,
    pub max_restart_attempts: u32,
    pub restart_delay: Duration,
}

/// 守护进程健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DaemonHealthStatus {
    pub status: ServiceStatus,
    pub uptime: Duration,
    pub ipc_server_status: IpcServerStatus,
    pub native_messaging_status: NativeMessagingStatus,
    pub managed_app_status: AppStatus,
    pub performance_metrics: DaemonPerformanceMetrics,
    pub last_error: Option<String>,
}

/// 性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DaemonPerformanceMetrics {
    pub total_requests_processed: u64,
    pub requests_per_second: f64,
    pub average_response_time: Duration,
    pub active_ipc_connections: usize,
    pub active_browser_connections: usize,
    pub memory_usage_bytes: u64,
    pub cpu_usage_percent: f64,
    pub disk_io_bytes: u64,
    pub network_io_bytes: u64,
}
```

### Tauri 主应用数据结构
```rust
/// IPC 连接配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcConnectionConfig {
    pub daemon_address: String,
    pub connection_timeout: Duration,
    pub reconnect_attempts: u32,
    pub reconnect_delay: Duration,
    pub authentication_token: Option<String>,
}

/// 浏览器消息类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BrowserMessageType {
    GetCredentials,
    SaveCredential,
    UpdateCredential,
    DeleteCredential,
    GeneratePassword,
    CheckPasswordStrength,
    GetSecureNote,
    SaveSecureNote,
    Custom(String),
}

/// 应用状态更新
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppStatusUpdate {
    pub timestamp: SystemTime,
    pub status: AppStatus,
    pub active_users: u32,
    pub encrypted_data_size: u64,
    pub last_sync: Option<SystemTime>,
    pub performance_metrics: AppPerformanceMetrics,
}

/// 守护进程命令
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DaemonCommand {
    Shutdown,
    Restart,
    ReloadConfig,
    GetStatus,
    GetMetrics,
    EnableMaintenance,
    DisableMaintenance,
    Custom { command: String, args: Vec<String> },
}
```

### 共享类型定义
```rust
/// 跨平台 IPC 传输类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IpcTransport {
    Auto,                    // 自动选择最佳传输方式
    Tcp { port: u16 },      // TCP Socket (跨平台)
    UnixSocket { path: PathBuf }, // Unix Domain Socket (Unix系统)
    NamedPipe { name: String },   // Named Pipe (Windows)
}

/// 浏览器类型
#[derive(Debug, Clone, Serialize, Deserialize, Hash, Eq, PartialEq)]
pub enum BrowserType {
    Chrome,
    Firefox,
    Edge,
    Safari,
    Chromium,
    Brave,
    Opera,
    Unknown(String),
}

/// 服务状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServiceStatus {
    Starting,
    Running,
    Stopping,
    Stopped,
    Error(String),
    Maintenance,
}

/// 重启策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RestartPolicy {
    Never,
    OnFailure,
    Always,
    UnlessStopped,
}
```

## 📅 模块化开发里程碑 (5周计划)

### 🔧 第1周：守护进程基础框架
**模块**: Module 1-2 (守护进程基础 + 系统服务集成)
- [ ] **Module 1**：守护进程基础框架 (4天)
  - 独立守护进程项目结构
  - 跨平台构建配置  
  - 基础框架和错误处理
  - 配置管理系统
- [ ] **Module 2**：系统服务集成 (3天)
  - Windows 系统服务集成
  - macOS LaunchDaemon 集成
  - Linux systemd 服务集成
  - 自动安装和卸载脚本

### 🔗 第2周：通信与代理引擎
**模块**: Module 3-4 (IPC通信 + Native Messaging代理)
- [ ] **Module 3**：IPC 通信引擎 (4天)
  - 跨平台 IPC 通信协议
  - TCP/Unix Socket/Named Pipe 实现
  - 消息序列化和反序列化
  - 连接池和负载均衡
- [ ] **Module 4**：Native Messaging 代理 (3天)
  - 浏览器Host自动注册
  - 消息代理引擎
  - 协议转换器
  - 并发处理能力

### 🚀 第3周：应用管理与协议层
**模块**: Module 5 + Module 9-10 (应用管理 + 协议层 + 浏览器适配)
- [ ] **Module 5**：Tauri 应用管理器 (4天)
  - 应用启动器 (静默启动)
  - 健康监控和性能追踪
  - 故障恢复和自动重启
  - 生命周期管理
- [ ] **Module 9-10**：协议层和浏览器适配 (3天)
  - Native Messaging 协议实现
  - 浏览器适配层
  - 跨平台兼容性

### 🔐 第4周：安全与消息处理
**模块**: Module 6 + Module 11 (安全代理 + 消息处理框架)
- [ ] **Module 6**：守护进程内部安全代理 (4天)
  - 系统级安全防护
  - IPC 通信安全管理
  - 进程间权限控制
  - 系统资源保护
- [ ] **Module 11**：消息处理框架 (3天)
  - 消息路由和分发系统
  - 处理器注册框架
  - 中间件支持
  - 错误处理和恢复机制

### 📊 第5周：监控、安全验证与部署
**模块**: Module 7-8 + Module 12 (监控告警 + 部署工具 + 协议安全)
- [ ] **Module 7**：性能监控和告警 (2天)
  - 实时性能指标收集
  - 健康检查和状态监控
  - 告警规则和通知机制
  - 监控面板可视化
- [ ] **Module 12**：Native Messaging 协议安全组件 (2天)
  - 扩展身份验证
  - 消息完整性验证
  - 频率限制和防护
  - 审计日志记录
- [ ] **Module 8**：部署和运维工具 (1天)
  - 自动化部署脚本
  - 系统服务安装器
  - 配置管理工具
  - 运维监控集成

## 技术依赖

### 🔧 独立守护进程依赖 (daemon/Cargo.toml)
```toml
[dependencies]
# 核心异步运行时
tokio = { version = "1.0", features = ["full", "tracing"] }
async-trait = "0.1"
futures = "0.3"

# 系统服务支持
[target.'cfg(windows)'.dependencies]
windows-service = "0.6"
winapi = { version = "0.3", features = ["winsvc", "winreg"] }

[target.'cfg(target_os = "macos")'.dependencies]
libc = "0.2"
core-foundation = "0.9"

[target.'cfg(target_os = "linux")'.dependencies]
systemd = "0.10"
nix = "0.27"

# 序列化和配置
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"
config = "0.13"

# IPC 通信
tokio-util = { version = "0.7", features = ["codec"] }
bincode = "1.3"
rmp-serde = "1.1"  # MessagePack
prost = "0.12"     # Protocol Buffers

# 网络和传输
tokio-stream = "0.1"
hyper = { version = "0.14", features = ["server", "tcp"] }

# 安全和加密
ring = "0.16"
aes-gcm = "0.10"
x25519-dalek = "2.0"
rand = "0.8"
base64 = "0.21"

# 错误处理和日志
thiserror = "1.0"
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-appender = "0.2"

# 性能监控
prometheus = "0.13"
sysinfo = "0.29"
procfs = "0.16"

# 进程管理
signal-hook = "0.3"
signal-hook-tokio = { version = "0.3", features = ["futures-v0_3"] }

# 跨平台支持
dirs = "5.0"
uuid = { version = "1.0", features = ["v4", "serde"] }

# 测试依赖
[dev-dependencies]
tokio-test = "0.4"
mockall = "0.11"
criterion = { version = "0.4", features = ["html_reports"] }
tempfile = "3.0"
```

### 🎨 Tauri 主应用依赖 (src-tauri/Cargo.toml)
```toml
[dependencies]
# Tauri 核心
tauri = { version = "2.0", features = ["api-all", "system-tray", "updater"] }
tauri-build = { version = "2.0", features = [] }

# IPC 客户端
tokio = { version = "1.0", features = ["full"] }
async-trait = "0.1"
futures = "0.3"

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 守护进程通信
bincode = "1.3"      # 与守护进程保持一致的序列化格式
rmp-serde = "1.1"    # MessagePack 支持

# 业务逻辑依赖
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# 数据库和存储
sea-orm = { version = "0.12", features = ["sqlx-sqlite", "runtime-tokio-rustls", "macros"] }
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }

# 加密和安全
ring = "0.16"
argon2 = "0.5"
base64 = "0.21"

# 错误处理和日志
thiserror = "1.0"
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"

# 测试依赖
[dev-dependencies]
tokio-test = "0.4"
mockall = "0.11"
sea-orm = { version = "0.12", features = ["mock"] }
```

### 前端依赖
```json
{
  "devDependencies": {
    "@types/chrome": "^0.0.246",
    "@types/webextension-polyfill": "^0.10.0",
    "webextension-polyfill": "^0.10.0"
  }
}
```

## 质量保证标准

### 性能要求
- **并发处理**: >1000 并发连接
- **响应时间**: <10ms 平均响应时间
- **吞吐量**: >10000 消息/秒
- **内存使用**: <50MB 基础内存占用
- **CPU 使用**: <5% 空闲时 CPU 占用

### 可靠性要求
- **可用性**: 99.9% 服务可用性
- **错误恢复**: <1s 自动恢复时间
- **连接稳定性**: 支持长连接和自动重连
- **数据完整性**: 100% 消息传递保证

### 安全要求
- **身份验证**: 强制扩展身份验证
- **数据加密**: 敏感数据端到端加密
- **访问控制**: 基于权限的 API 访问
- **审计跟踪**: 完整的操作审计日志

## 集成示例

### 🔧 独立守护进程启动
```rust
use daemon::{
    DaemonConfig, DaemonService, 
    AppLifecycleManager, IpcServer
};

/// 守护进程启动示例
///
/// 演示如何创建和启动企业级独立守护进程
///
/// # 示例
/// ```rust
/// // 创建守护进程配置
/// let daemon_config = DaemonConfig::builder()
///     .service_name("secure-password-daemon")
///     .auto_start(true)
///     .max_ipc_connections(1000)
///     .enable_encryption(true)
///     .app_path("/path/to/secure-password-app")
///     .build();
/// 
/// // 启动守护进程服务
/// let mut daemon = DaemonService::start(daemon_config).await?;
/// 
/// // 注册所有浏览器 Native Messaging Host
/// daemon.register_all_browser_hosts().await?;
/// 
/// // 启动 IPC 服务器
/// let ipc_config = IpcBindConfig::auto_detect();
/// daemon.start_ipc_server(ipc_config).await?;
/// 
/// // 启动应用生命周期管理器
/// daemon.start_app_manager().await?;
/// 
/// // 开始监听所有服务
/// daemon.run().await?;
/// ```
```

### 🎨 Tauri 主应用 IPC 客户端
```rust
use tauri_app::{
    DaemonClient, BrowserMessageHandler, 
    IpcConnectionConfig, AppStatusUpdate
};

/// Tauri 主应用连接守护进程示例
///
/// 演示如何从 Tauri 主应用连接到独立守护进程
///
/// # 示例
/// ```rust
/// // 创建 IPC 连接配置
/// let connection_config = IpcConnectionConfig::builder()
///     .daemon_address("127.0.0.1:9090")
///     .connection_timeout(Duration::from_secs(10))
///     .auto_reconnect(true)
///     .enable_encryption(true)
///     .build();
/// 
/// // 连接到守护进程
/// let mut daemon_client = DaemonClient::connect(connection_config).await?;
/// 
/// // 注册浏览器消息处理器
/// daemon_client.register_browser_message_handler(
///     BrowserMessageType::GetCredentials,
///     Box::new(CredentialsHandler::new())
/// ).await?;
/// 
/// // 定期发送状态更新给守护进程
/// let status_update = AppStatusUpdate {
///     timestamp: SystemTime::now(),
///     status: AppStatus::Running,
///     active_users: 1,
///     encrypted_data_size: calculate_data_size(),
///     last_sync: get_last_sync_time(),
///     performance_metrics: collect_metrics(),
/// };
/// daemon_client.send_status_update(status_update).await?;
/// 
/// // 开始监听守护进程命令
/// daemon_client.start_command_listener().await?;
/// ```
```

### 🌐 浏览器消息处理器
```rust
/// 浏览器消息处理器实现
///
/// 演示如何在 Tauri 主应用中处理来自浏览器扩展的消息
#[async_trait]
impl BrowserMessageHandler for CredentialsHandler {
    /// 处理来自浏览器扩展的消息
    ///
    /// 通过守护进程代理接收并处理浏览器扩展请求
    ///
    /// # 参数
    /// - `message`: 从浏览器扩展接收到的消息
    ///
    /// # 返回
    /// Result<BrowserResponse> - 处理结果消息
    ///
    /// # 错误
    /// 当处理过程中出现错误时返回相应错误信息
    async fn handle(&self, message: BrowserMessage) -> Result<BrowserResponse> {
        match message.message_type {
            BrowserMessageType::GetCredentials => {
                let domain = message.payload.get("domain")
                    .ok_or("Missing domain parameter")?
                    .as_str()
                    .unwrap();
                
                // 从加密存储中获取凭证
                let credentials = self.credential_manager
                    .get_credentials_for_domain(domain)
                    .await?;
                
                Ok(BrowserResponse::success(
                    message.request_id,
                    json!({ "credentials": credentials })
                ))
            },
            BrowserMessageType::SaveCredential => {
                let credential_data = message.payload.get("credential")
                    .ok_or("Missing credential data")?;
                
                // 保存凭证到加密存储
                let saved_credential = self.credential_manager
                    .save_credential(credential_data)
                    .await?;
                
                Ok(BrowserResponse::success(
                    message.request_id,
                    json!({ "saved": true, "id": saved_credential.id })
                ))
            },
            _ => Err(anyhow!("Unsupported message type"))
        }
    }
    
    /// 获取支持的消息类型列表
    fn supported_message_types(&self) -> Vec<BrowserMessageType> {
        vec![
            BrowserMessageType::GetCredentials,
            BrowserMessageType::SaveCredential,
            BrowserMessageType::UpdateCredential,
            BrowserMessageType::DeleteCredential,
        ]
    }
}
```

### 🌐 浏览器扩展集成
```typescript
// Chrome 扩展 content script - 通过守护进程代理连接
import { NativeMessagingClient } from './native-messaging-client';

/// 浏览器扩展与守护进程交互示例
///
/// 浏览器扩展通过 Native Messaging 连接到守护进程，
/// 守护进程再将请求转发给 Tauri 主应用处理
const client = new NativeMessagingClient({
    hostName: 'com.secure_password.daemon',  // 连接到守护进程
    timeout: 10000,
    autoReconnect: true
});

// 连接到守护进程
await client.connect();

// 发送凭证获取请求
const response = await client.sendMessage({
    type: 'getCredentials',
    requestId: generateId(),
    payload: { 
        domain: window.location.hostname,
        url: window.location.href,
        formData: extractFormFields()
    },
    timestamp: Date.now(),
    source: 'browser-extension',
    extensionId: chrome.runtime.id
});

// 处理守护进程/主应用的响应
if (response.success) {
    const credentials = response.data.credentials;
    autoFillCredentials(credentials);
}

// 监听守护进程推送的实时更新
client.onMessage((message) => {
    switch (message.type) {
        case 'credentialsUpdated':
            refreshCredentialFields(message.payload);
            break;
        case 'securityAlert':
            showSecurityWarning(message.payload);
            break;
        case 'syncComplete':
            updateSyncStatus(message.payload);
            break;
    }
});

// 发送密码保存请求
const saveResponse = await client.sendMessage({
    type: 'saveCredential',
    requestId: generateId(),
    payload: {
        domain: window.location.hostname,
        username: document.getElementById('username').value,
        password: document.getElementById('password').value,
        metadata: {
            title: document.title,
            url: window.location.href,
            formSelector: getFormSelector()
        }
    },
    timestamp: Date.now(),
    source: 'browser-extension'
});
```

## 监控和运维

### 🔧 守护进程性能监控
```rust
/// 获取并记录守护进程实时性能指标
///
/// 从守护进程获取详细的性能统计数据并记录到日志
async fn monitor_daemon_performance(daemon: &DaemonService) {
    let health_status = daemon.get_health_status().await;
    let metrics = &health_status.performance_metrics;
    
    log::info!("守护进程运行时间: {:?}", health_status.uptime);
    log::info!("处理请求数: {}", metrics.total_requests_processed);
    log::info!("每秒请求数: {:.2}", metrics.requests_per_second);
    log::info!("平均响应时间: {:?}", metrics.average_response_time);
    log::info!("活跃 IPC 连接: {}", metrics.active_ipc_connections);
    log::info!("活跃浏览器连接: {}", metrics.active_browser_connections);
    log::info!("内存使用: {} MB", metrics.memory_usage_bytes / 1024 / 1024);
    log::info!("CPU 使用率: {:.1}%", metrics.cpu_usage_percent);
    
    // 性能告警检查
    if metrics.memory_usage_bytes > 100 * 1024 * 1024 {  // >100MB
        log::warn!("守护进程内存使用过高: {} MB", metrics.memory_usage_bytes / 1024 / 1024);
    }
    
    if metrics.cpu_usage_percent > 10.0 {  // >10%
        log::warn!("守护进程 CPU 使用率过高: {:.1}%", metrics.cpu_usage_percent);
    }
}
```

### 🏥 健康检查系统
```rust
/// 执行全面的健康状态检查
///
/// 检查守护进程、IPC服务、应用管理器等所有组件的健康状态
async fn comprehensive_health_check(daemon: &DaemonService) -> Result<HealthReport> {
    let health_status = daemon.get_health_status().await;
    
    let mut issues = Vec::new();
    let mut warnings = Vec::new();
    
    // 检查守护进程主服务状态
    match health_status.status {
        ServiceStatus::Running => log::info!("守护进程运行正常"),
        ServiceStatus::Starting => warnings.push("守护进程正在启动中".to_string()),
        ServiceStatus::Error(ref error) => {
            issues.push(format!("守护进程服务异常: {}", error));
        },
        _ => issues.push("守护进程状态异常".to_string()),
    }
    
    // 检查 IPC 服务器状态
    match health_status.ipc_server_status {
        IpcServerStatus::Listening => log::info!("IPC 服务器运行正常"),
        IpcServerStatus::Error(ref error) => {
            issues.push(format!("IPC 服务器异常: {}", error));
        },
        _ => warnings.push("IPC 服务器状态不稳定".to_string()),
    }
    
    // 检查 Native Messaging 状态
    match health_status.native_messaging_status {
        NativeMessagingStatus::Active => log::info!("Native Messaging 服务正常"),
        NativeMessagingStatus::PartialFailure => {
            warnings.push("部分浏览器 Native Messaging 连接失败".to_string());
        },
        NativeMessagingStatus::Failed => {
            issues.push("Native Messaging 服务完全失败".to_string());
        },
    }
    
    // 检查管理的 Tauri 应用状态
    match health_status.managed_app_status {
        AppStatus::Running => log::info!("Tauri 主应用运行正常"),
        AppStatus::NotResponding => {
            warnings.push("Tauri 主应用响应缓慢".to_string());
        },
        AppStatus::Crashed => {
            issues.push("Tauri 主应用已崩溃，正在尝试重启".to_string());
        },
        AppStatus::Stopped => {
            issues.push("Tauri 主应用已停止运行".to_string());
        },
    }
    
    Ok(HealthReport {
        overall_status: if issues.is_empty() { 
            if warnings.is_empty() { HealthLevel::Healthy } 
            else { HealthLevel::Warning } 
        } else { 
            HealthLevel::Critical 
        },
        issues,
        warnings,
        metrics: health_status.performance_metrics,
        timestamp: SystemTime::now(),
    })
}
```

### ⚙️ 配置热重载和管理
```rust
/// 更新守护进程运行时配置
///
/// 在不重启守护进程的情况下更新配置参数
///
/// # 参数
/// - `daemon`: 守护进程实例
/// - `new_config`: 新的配置参数
///
/// # 返回
/// Result<ConfigUpdateReport> - 更新结果报告
async fn reload_daemon_config(
    daemon: &mut DaemonService, 
    new_config: DaemonConfig
) -> Result<ConfigUpdateReport> {
    let mut update_report = ConfigUpdateReport::new();
    
    // 验证新配置的有效性
    new_config.validate()?;
    
    // 逐步更新各个组件的配置
    if daemon.get_config().ipc_config != new_config.ipc_config {
        daemon.update_ipc_config(new_config.ipc_config.clone()).await?;
        update_report.updated_components.push("IPC配置".to_string());
    }
    
    if daemon.get_config().native_messaging_config != new_config.native_messaging_config {
        daemon.update_native_messaging_config(new_config.native_messaging_config.clone()).await?;
        update_report.updated_components.push("Native Messaging配置".to_string());
    }
    
    if daemon.get_config().security_config != new_config.security_config {
        daemon.update_security_config(new_config.security_config.clone()).await?;
        update_report.updated_components.push("安全配置".to_string());
    }
    
    if daemon.get_config().monitoring_config != new_config.monitoring_config {
        daemon.update_monitoring_config(new_config.monitoring_config.clone()).await?;
        update_report.updated_components.push("监控配置".to_string());
    }
    
    // 保存新配置到文件
    new_config.save_to_file(&daemon.config_file_path()).await?;
    
    log::info!("守护进程配置已更新，影响组件: {:?}", update_report.updated_components);
    update_report.success = true;
    update_report.timestamp = SystemTime::now();
    
    Ok(update_report)
}

/// 守护进程管理命令处理器
///
/// 处理运维管理命令，支持远程管理和自动化运维
async fn handle_management_command(
    daemon: &mut DaemonService,
    command: ManagementCommand
) -> Result<CommandResponse> {
    match command {
        ManagementCommand::GetStatus => {
            let status = daemon.get_health_status().await;
            Ok(CommandResponse::success(json!(status)))
        },
        ManagementCommand::GetMetrics => {
            let metrics = daemon.get_performance_metrics().await;
            Ok(CommandResponse::success(json!(metrics)))
        },
        ManagementCommand::ReloadConfig { config_path } => {
            let new_config = DaemonConfig::load_from_file(&config_path).await?;
            let update_report = reload_daemon_config(daemon, new_config).await?;
            Ok(CommandResponse::success(json!(update_report)))
        },
        ManagementCommand::RestartComponent { component } => {
            match component.as_str() {
                "ipc" => daemon.restart_ipc_server().await?,
                "native_messaging" => daemon.restart_native_messaging().await?,
                "app_manager" => daemon.restart_app_manager().await?,
                _ => return Err(anyhow!("未知组件: {}", component)),
            }
            Ok(CommandResponse::success(json!({"restarted": component})))
        },
        ManagementCommand::EnableMaintenance => {
            daemon.enable_maintenance_mode().await?;
            Ok(CommandResponse::success(json!({"maintenance_enabled": true})))
        },
        ManagementCommand::DisableMaintenance => {
            daemon.disable_maintenance_mode().await?;
            Ok(CommandResponse::success(json!({"maintenance_disabled": true})))
        },
    }
}
```

## 扩展性设计

### 🔌 守护进程插件架构
```rust
/// 守护进程插件接口定义
///
/// 支持在守护进程中动态加载和管理功能插件
#[async_trait]
pub trait DaemonPlugin: Send + Sync {
    /// 插件初始化
    async fn initialize(&self, context: &DaemonPluginContext) -> Result<()>;
    
    /// 处理浏览器消息（在转发给主应用之前）
    async fn before_browser_message(&self, message: &mut BrowserMessage) -> Result<()>;
    
    /// 处理应用响应（在返回给浏览器之前）
    async fn after_app_response(&self, response: &mut BrowserResponse) -> Result<()>;
    
    /// 处理 IPC 消息（守护进程与主应用之间）
    async fn handle_ipc_message(&self, message: &IpcMessage) -> Option<IpcResponse>;
    
    /// 定期维护任务
    async fn periodic_maintenance(&self, context: &MaintenanceContext) -> Result<()>;
    
    /// 插件信息
    fn plugin_info(&self) -> DaemonPluginInfo;
    
    /// 插件配置
    fn default_config(&self) -> PluginConfig;
}

/// 守护进程插件管理器
pub struct DaemonPluginManager {
    plugins: Vec<Box<dyn DaemonPlugin>>,
    config: PluginManagerConfig,
}

impl DaemonPluginManager {
    /// 注册插件到守护进程
    pub async fn register_plugin(&mut self, plugin: Box<dyn DaemonPlugin>) -> Result<()> {
        let plugin_info = plugin.plugin_info();
        log::info!("注册守护进程插件: {}", plugin_info.name);
        
        // 初始化插件
        let context = DaemonPluginContext::new(&self.config);
        plugin.initialize(&context).await?;
        
        self.plugins.push(plugin);
        Ok(())
    }
    
    /// 卸载插件
    pub async fn unregister_plugin(&mut self, plugin_name: &str) -> Result<()> {
        self.plugins.retain(|p| p.plugin_info().name != plugin_name);
        log::info!("卸载守护进程插件: {}", plugin_name);
        Ok(())
    }
}

// 插件注册示例
daemon.plugin_manager()
    .register_plugin(Box::new(SecurityAuditPlugin::new()))
    .await?;
daemon.plugin_manager()
    .register_plugin(Box::new(PerformanceMonitorPlugin::new()))
    .await?;
```

### 🔄 Tauri 主应用中间件系统
```rust
/// Tauri 主应用消息中间件接口
///
/// 在主应用中处理来自守护进程的消息请求
#[async_trait]
pub trait AppMiddleware: Send + Sync {
    /// 消息预处理
    async fn before_handle(&self, message: &mut BrowserMessage) -> Result<()>;
    
    /// 消息后处理
    async fn after_handle(&self, response: &mut BrowserResponse) -> Result<()>;
    
    /// 错误处理
    async fn on_error(&self, error: &AppError, message: &BrowserMessage) -> Result<Option<BrowserResponse>>;
    
    /// 中间件信息
    fn middleware_info(&self) -> MiddlewareInfo;
}

/// 中间件管理器
pub struct MiddlewareManager {
    middlewares: Vec<Box<dyn AppMiddleware>>,
}

impl MiddlewareManager {
    /// 注册中间件到主应用
    pub async fn use_middleware(&mut self, middleware: Box<dyn AppMiddleware>) -> Result<()> {
        let info = middleware.middleware_info();
        log::info!("注册主应用中间件: {}", info.name);
        self.middlewares.push(middleware);
        Ok(())
    }
    
    /// 执行中间件链
    pub async fn execute_chain(
        &self, 
        message: &mut BrowserMessage
    ) -> Result<Option<BrowserResponse>> {
        // 按注册顺序执行 before_handle
        for middleware in &self.middlewares {
            middleware.before_handle(message).await?;
        }
        
        // 这里会执行实际的业务逻辑处理
        // let response = handle_business_logic(message).await?;
        
        Ok(None) // 实际实现中会返回处理结果
    }
}

// 中间件注册示例
app.middleware_manager()
    .use_middleware(Box::new(AuthenticationMiddleware::new()))
    .await?;
app.middleware_manager()
    .use_middleware(Box::new(RateLimitingMiddleware::new()))
    .await?;
app.middleware_manager()
    .use_middleware(Box::new(AuditLoggingMiddleware::new()))
    .await?;
```

### 🎨 浏览器扩展功能扩展
```rust
/// 浏览器扩展功能模块接口
///
/// 支持在不同浏览器中扩展特定功能
pub trait BrowserExtensionModule: Send + Sync {
    /// 模块支持的浏览器类型
    fn supported_browsers(&self) -> Vec<BrowserType>;
    
    /// 处理特定浏览器的消息
    async fn handle_browser_specific_message(
        &self, 
        browser: BrowserType,
        message: BrowserMessage
    ) -> Result<BrowserResponse>;
    
    /// 注入到浏览器的脚本内容
    fn injection_scripts(&self, browser: BrowserType) -> Vec<String>;
    
    /// 模块配置
    fn module_config(&self) -> ExtensionModuleConfig;
}

/// 扩展模块管理器
pub struct ExtensionModuleManager {
    modules: HashMap<String, Box<dyn BrowserExtensionModule>>,
}

impl ExtensionModuleManager {
    /// 注册浏览器扩展模块
    pub fn register_module(&mut self, name: String, module: Box<dyn BrowserExtensionModule>) {
        log::info!("注册浏览器扩展模块: {}", name);
        self.modules.insert(name, module);
    }
    
    /// 获取支持特定浏览器的模块
    pub fn get_modules_for_browser(&self, browser: BrowserType) -> Vec<&dyn BrowserExtensionModule> {
        self.modules.values()
            .filter(|module| module.supported_browsers().contains(&browser))
            .map(|module| module.as_ref())
            .collect()
    }
}

// 扩展模块注册示例
extension_manager.register_module(
    "password_generator".to_string(),
    Box::new(PasswordGeneratorModule::new())
);
extension_manager.register_module(
    "form_detector".to_string(),
    Box::new(FormDetectorModule::new())
);
extension_manager.register_module(
    "security_scanner".to_string(),
    Box::new(SecurityScannerModule::new())
);
```

## 🧪 集成测试计划

### 测试金字塔架构
```
                    🔺 E2E 集成测试 (5%)
                   📐 跨模块集成测试 (15%)  
                  📊 模块内集成测试 (25%)
                 🧪 单元测试基础层 (55%)
```

### 三级集成测试体系

#### L1 系统级集成测试 (End-to-End)
**测试范围**: 浏览器扩展 → 守护进程 → 主应用完整链路  
**测试频率**: 每个主要里程碑完成后  
**自动化程度**: 100% 自动化

```yaml
E2E测试场景:
  浏览器扩展通信测试:
    - Chrome扩展 → 守护进程 → Tauri应用 → 数据库
    - Firefox扩展 → 守护进程 → Tauri应用 → 数据库  
    - Edge扩展 → 守护进程 → Tauri应用 → 数据库
    - Safari扩展 → 守护进程 → Tauri应用 → 数据库

  故障恢复测试:
    - 守护进程重启后自动恢复连接
    - 主应用崩溃后守护进程自动重启应用
    - IPC连接断开后自动重连机制
    - 网络中断后同步数据恢复

  性能压力测试:
    - 1000并发连接处理能力
    - 10000消息/秒吞吐量测试
    - 长时间运行稳定性测试 (24小时)
    - 内存泄漏检测测试
```

#### L2 跨模块集成测试 (Cross-Module)
**测试范围**: 2-3个相关模块的协作功能  
**测试频率**: 每个模块完成后立即执行  
**自动化程度**: 95% 自动化

```rust
// 跨模块集成测试示例框架
#[cfg(test)]
mod cross_module_integration_tests {
    use super::*;
    
    /// 测试 IPC通信 + Native Messaging 集成
    #[tokio::test]
    async fn test_ipc_native_messaging_integration() {
        // 1. 启动IPC服务器
        let ipc_config = IpcConfig::test_config();
        let mut ipc_server = IpcServer::start(ipc_config).await?;
        
        // 2. 启动Native Messaging Host
        let nm_config = NativeMessagingConfig::test_config();
        let mut nm_host = NativeMessagingHost::start(nm_config).await?;
        
        // 3. 模拟浏览器扩展请求
        let browser_request = BrowserRequest {
            message_type: "get_credentials".to_string(),
            payload: json!({ "domain": "example.com" }),
            extension_id: "test-extension-id".to_string(),
        };
        
        // 4. 测试请求代理流程
        let response = nm_host.handle_browser_request(browser_request).await?;
        
        // 5. 验证响应格式和内容
        assert_eq!(response.status, "success");
        assert!(response.payload.get("credentials").is_some());
    }
    
    /// 测试 守护进程安全 + 协议安全 集成
    #[tokio::test]
    async fn test_security_layers_integration() {
        // 1. 初始化守护进程安全代理
        let daemon_security = DaemonSecurityManager::new().await?;
        
        // 2. 初始化协议安全组件
        let protocol_security = ProtocolSecurityManager::new().await?;
        
        // 3. 测试恶意请求检测链路
        let malicious_request = create_malicious_request();
        
        // 4. 协议层安全验证
        let protocol_result = protocol_security
            .validate_browser_request(&malicious_request).await;
        assert!(protocol_result.is_err());
        
        // 5. 守护进程层安全验证
        let daemon_result = daemon_security
            .validate_ipc_message(&malicious_request).await;
        assert!(daemon_result.is_err());
    }
}
```

#### L3 模块内组件集成测试 (Intra-Module)
**测试范围**: 单个模块内部组件的协作  
**测试频率**: 每日开发完成后  
**自动化程度**: 100% 自动化

### 集成测试自动化框架

#### CI/CD 集成测试流水线
```yaml
# .github/workflows/integration-tests.yml
name: 模块集成测试

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  integration-tests:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: Run Module Integration Tests
      run: |
        # 按模块顺序执行集成测试
        cargo test integration::daemon_ipc --release
        cargo test integration::ipc_native_messaging --release
        cargo test integration::security_layers --release
        cargo test integration::app_lifecycle --release
    
    - name: Run Cross-Platform Tests
      run: |
        cargo test integration::cross_platform --release
    
    - name: Run E2E Tests
      run: |
        ./scripts/run_e2e_tests.sh
```

### 单元测试和性能测试
```rust
// 基准测试示例
#[cfg(test)]
mod benchmarks {
    use criterion::{criterion_group, criterion_main, Criterion};
    
    fn bench_message_processing(c: &mut Criterion) {
        c.bench_function("process_1000_messages", |b| {
            b.iter(|| {
                // 性能测试逻辑
            })
        });
    }
    
    criterion_group!(benches, bench_message_processing);
    criterion_main!(benches);
}
```

## 🛡️ 企业级安全架构总览

### 🔒 双向全程安全防护体系

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            🌐 Chrome 浏览器扩展                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                           🔐 扩展安全层                                     │ │
│  │                                                                             │ │
│  │  ✅ 扩展ID验证         ✅ 来源域名验证      ✅ 证书链验证                    │ │
│  │  ✅ 权限检查           ✅ 内容脚本隔离      ✅ CSP策略                      │ │
│  │  ✅ 消息签名           ✅ 时间戳验证        ✅ 重放攻击防护                 │ │
│  │                                                                             │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
                                     ↓ 加密传输
┌─────────────────────────────────────────────────────────────────────────────────┐
│                          📡 协议安全层 (Module 12)                               │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                         Native Messaging 协议安全                           │ │
│  │                                                                             │ │
│  │  🛡️ 消息净化处理      🔍 威胁检测分析      🚨 异常行为监控                  │ │
│  │  📝 协议格式验证      ⏱️ 频率限制控制      📋 审计跟踪记录                  │ │
│  │  🔐 加密协议转换      🛠️ 版本兼容管理      📊 合规性检查                    │ │
│  │                                                                             │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
                                     ↓ IPC 安全通道
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        🔧 守护进程安全层 (Module 6)                              │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                          守护进程内部安全代理                               │ │
│  │                                                                             │ │
│  │  🏠 进程隔离保护      🔐 IPC通道加密       🛡️ 系统资源保护                  │ │
│  │  🔑 会话密钥管理      👤 连接身份验证      📋 权限级别控制                   │ │
│  │  🗃️ 内存安全清理      🔍 完整性校验        📊 内部审计系统                  │ │
│  │                                                                             │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
                                     ↓ 安全 IPC 通信
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           🎨 Tauri 应用安全层                                   │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                            应用业务安全                                     │ │
│  │                                                                             │ │
│  │  🔒 业务数据加密      🔐 用户身份验证      🛡️ 中间件安全                    │ │
│  │  📝 业务逻辑验证      🗄️ 数据库安全       🔍 操作审计日志                   │ │
│  │  🚀 安全备份同步      ⚡ 性能安全平衡      📊 合规性报告                    │ │
│  │                                                                             │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 🚨 企业级威胁防护矩阵

| 攻击类型 | Module 12 (协议安全) | Module 6 (守护进程安全) | 防护效果 |
|---------|-------------------|---------------------|---------|
| **注入攻击** | 消息净化、格式验证 | IPC完整性校验 | ✅ 双重防护 |
| **中间人攻击** | 协议加密、证书验证 | IPC通道加密 | ✅ 端到端加密 |
| **重放攻击** | 时间戳验证、nonce | 会话管理 | ✅ 时效性控制 |
| **DoS攻击** | 频率限制、连接控制 | 资源保护、负载均衡 | ✅ 系统稳定 |
| **提权攻击** | 扩展权限检查 | 进程隔离、最小权限 | ✅ 权限隔离 |
| **数据泄露** | 协议数据脱敏 | 内存安全清理 | ✅ 敏感数据保护 |

### 🔐 安全合规标准

#### 数据保护合规
- **GDPR 合规**: 用户数据隐私保护，数据最小化原则
- **SOC 2 Type II**: 安全控制和审计跟踪
- **ISO 27001**: 信息安全管理体系
- **零知识架构**: 服务端无法访问用户明文数据

#### 企业安全要求
- **端到端加密**: X25519 + ChaCha20-Poly1305 加密算法
- **身份验证**: 多因素认证 + 数字证书验证
- **访问控制**: RBAC + 最小权限原则
- **审计合规**: 完整的操作日志 + 取证数据收集

## 💡 独立守护进程架构优势

### 🚀 企业级可靠性
- **进程隔离**：守护进程独立运行，主应用崩溃不影响浏览器扩展通信
- **自动恢复**：守护进程监控主应用状态，故障时自动重启和恢复
- **系统服务级别**：7x24小时稳定运行，支持开机自启动
- **静默运行**：无用户界面干扰，完全后台透明运行

### 🔒 安全性增强
- **权限隔离**：守护进程以系统服务权限运行，提供更高安全级别
- **审计跟踪**：完整的操作日志和审计记录，满足企业合规要求
- **加密通信**：所有 IPC 通信使用加密协议，防止中间人攻击
- **权限验证**：多层权限验证机制，确保只有授权请求被处理

### ⚡ 性能优化
- **资源独立**：守护进程和主应用独立分配资源，避免相互干扰
- **并发处理**：支持多浏览器同时连接，单个守护进程处理所有请求
- **连接复用**：IPC 连接池和缓存机制，减少连接开销
- **异步非阻塞**：全异步架构，高并发处理能力

### 🔧 运维便利性
- **统一管理**：单一守护进程管理所有浏览器通信和应用启动
- **热重载配置**：支持运行时配置更新，无需重启服务
- **版本管理**：独立的守护进程版本管理和升级机制
- **监控告警**：完整的性能监控和告警系统

## 🎯 核心技术特性

### 📡 IPC 通信引擎
```rust
// 跨平台 IPC 通信示例
#[cfg(windows)]
let transport = NamedPipeTransport::new("secure-password-daemon");

#[cfg(unix)]
let transport = UnixSocketTransport::new("/tmp/secure-password-daemon.sock");

let ipc_server = IpcServer::new(transport)
    .with_encryption(true)
    .with_connection_pool(100)
    .with_timeout(Duration::from_secs(30));

ipc_server.start().await?;
```

### 🌐 Native Messaging 代理
```rust
// Native Messaging Host 自动注册
let host_registry = BrowserRegistry::new();

// 自动检测并注册所有浏览器
host_registry.auto_register_all_browsers().await?;

// 启动消息代理
let nm_host = NativeMessagingHost::new()
    .with_ipc_client(ipc_client)
    .with_request_timeout(Duration::from_secs(10));

nm_host.start_listening().await?;
```

### 🚀 应用生命周期管理
```rust
// 应用启动和监控
let app_manager = AppLifecycleManager::new()
    .with_app_path("/path/to/tauri-app")
    .with_health_check_interval(Duration::from_secs(30))
    .with_auto_restart(true);

// 静默启动主应用
app_manager.launch_app_silent().await?;

// 持续健康监控
app_manager.start_health_monitoring().await?;
```

## 📊 部署和配置

### 🔧 自动化安装
```bash
# 跨平台自动安装
curl -sSL https://install.secure-password.com/daemon | bash

# 或者使用平台特定的安装器
# Windows: installer.msi
# macOS: installer.pkg  
# Linux: installer.deb/rpm
```

### ⚙️ 配置管理
```toml
# daemon_config.toml
[daemon]
name = "secure-password-daemon"
version = "1.0.0"
auto_start = true
log_level = "info"

[ipc]
transport = "auto"  # auto/tcp/unix_socket/named_pipe
bind_address = "auto"
max_connections = 100
timeout_seconds = 30

[native_messaging]
auto_register_browsers = true
allowed_extensions = [
    "chrome-extension://your-extension-id",
    "moz-extension://your-extension-id"
]

[app_management]
app_path = "/path/to/tauri-app"
health_check_interval = 30
auto_restart = true
max_restart_attempts = 3

[security]
enable_encryption = true
audit_logging = true
permission_validation = true

[monitoring]
enable_metrics = true
health_check_endpoint = "http://127.0.0.1:9090/health"
alert_webhook_url = "https://your-monitoring.com/webhook"
```

## 🚀 总结

这个**企业级独立守护进程架构**的 Native Messaging 实现提供：

### ✨ 核心价值
1. **🏗️ 企业级架构**：系统服务级别的高可用性和可靠性
2. **🔒 增强安全性**：进程隔离、权限验证、审计跟踪
3. **⚡ 高性能**：异步非阻塞、连接池、资源优化
4. **🌐 跨平台兼容**：Windows/macOS/Linux 原生集成
5. **🔧 运维友好**：自动化部署、监控告警、热重载配置
6. **🧪 测试驱动**：>95% 测试覆盖率，完整的质量保证体系

### 🎯 适用场景
- **企业级密码管理**：满足企业安全和合规要求
- **7x24小时服务**：关键业务系统的高可用性需求
- **多用户环境**：共享计算机和企业网络环境
- **安全审计**：需要完整操作日志和审计跟踪的场景

### 🚀 技术优势
- **独立进程架构**：故障隔离、资源独立、安全增强
- **标准化 IPC 通信**：跨平台兼容、高性能、可扩展
- **智能应用管理**：自动启动、健康监控、故障恢复
- **企业级运维**：监控告警、配置管理、版本控制

## 🎯 质量门控与验收标准

### 模块级质量要求
1. **代码质量**
   - ✅ 通过 `cargo clippy`（零警告）
   - ✅ 通过 `cargo fmt --check`
   - ✅ 通过 Rust 静态分析工具检查
   - ✅ 函数式编程和不可变数据结构优先

2. **测试覆盖**
   - ✅ 单元测试覆盖 >95%
   - ✅ 集成测试通过率 100%
   - ✅ 性能测试达标
   - ✅ 安全测试通过
   - ✅ 端到端测试覆盖主要用例

3. **性能基准**
   - ✅ 并发处理 >1000 连接
   - ✅ 响应时间 <10ms (P95)
   - ✅ 内存使用 <100MB (守护进程)
   - ✅ CPU 使用 <5% (空闲时)
   - ✅ 消息吞吐量 >10,000 msg/s

4. **安全要求**
   - ✅ 零知识架构设计
   - ✅ 端到端加密通信
   - ✅ 进程隔离和权限最小化
   - ✅ 审计日志完整性保护
   - ✅ 抗攻击和故障恢复能力

### 集成验收标准

#### 1. 系统稳定性
- ✅ **7x24小时稳定运行**: 连续运行测试通过
- ✅ **故障自动恢复**: 模拟故障场景恢复测试
- ✅ **数据完整性保证**: 并发访问和异常情况数据一致性
- ✅ **内存泄漏检测**: 长期运行内存使用稳定
- ✅ **优雅关闭**: 信号处理和资源清理正确

#### 2. 企业级特性
- ✅ **多用户支持**: 支持多用户并发使用
- ✅ **权限管理**: 基于角色的访问控制
- ✅ **审计合规**: 完整的操作审计跟踪
- ✅ **监控集成**: 与企业监控系统集成
- ✅ **备份恢复**: 配置和数据备份恢复机制

#### 3. 兼容性验证
- ✅ **多浏览器兼容**: Chrome/Firefox/Edge/Safari 全面支持
- ✅ **跨平台支持**: Windows/macOS/Linux 完整兼容
- ✅ **版本兼容性**: 向后兼容旧版本协议
- ✅ **移动端支持**: iOS/Android 平台适配

## 🚀 项目总结

该**企业级独立守护进程架构**的 Native Messaging 实现提供：

### ✨ 核心价值
1. **🏗️ 企业级架构**：系统服务级别的高可用性和可靠性
2. **🔒 增强安全性**：进程隔离、权限验证、审计跟踪
3. **⚡ 高性能**：异步非阻塞、连接池、资源优化
4. **🌐 跨平台兼容**：Windows/macOS/Linux 原生集成
5. **🔧 运维友好**：自动化部署、监控告警、热重载配置
6. **🧪 测试驱动**：>95% 测试覆盖率，完整的质量保证体系

### 🎯 适用场景
- **企业级密码管理**：满足企业安全和合规要求
- **7x24小时服务**：关键业务系统的高可用性需求
- **多用户环境**：共享计算机和企业网络环境
- **安全审计**：需要完整操作日志和审计跟踪的场景

### 🚀 技术优势
- **独立进程架构**：故障隔离、资源独立、安全增强
- **标准化 IPC 通信**：跨平台兼容、高性能、可扩展
- **智能应用管理**：自动启动、健康监控、故障恢复
- **企业级运维**：监控告警、配置管理、版本控制

该架构将成为下一代浏览器扩展与 Tauri 应用通信的**企业级标准解决方案**，为用户提供**安全、稳定、高性能**的跨平台密码管理体验。 