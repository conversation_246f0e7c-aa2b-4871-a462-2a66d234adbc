# Stronghold 安全存储模块：跨平台实现计划与增量路线图

## 1. 核心目标与愿景

本项目旨在实现一个基于 `tauri-plugin-stronghold` 的安全存储模块，作为应用核心凭证和敏感数据的加密后端。它必须是：
- **跨平台 (Cross-Platform)**: 在 Windows, macOS, Linux, iOS, 和 Android 上提供统一、可靠的功能。
- **安全 (Secure)**: 采用零知识架构，主密钥永远不以明文形式存储在磁盘或服务器上。
- **用户友好 (User-Friendly)**: 实现无缝的自动解锁机制，避免在应用常规启动时打扰用户输入密码。
- **健壮 (Robust)**: 具备完善的设备管理和灾难恢复机制。

---

## 2. 最终架构：混合令牌与轻量级设备绑定

我们将采用"混合令牌与轻量级设备绑定"方案，它在满足所有约束条件（特别是"无特殊权限要求"）的同时，提供了最佳的平衡。

### 2.1. 架构流程图

#### 阶段零：新用户首次注册
```mermaid
sequenceDiagram
    participant Client as 客户端 (<PERSON><PERSON>pp)
    participant Server as 后端服务器

    Note over Client,Server: 新用户首次注册
    Client->>Client: 用户输入主密码进行注册
    Client->>Client: **(本地)** 生成一个全新的、高熵的 StrongholdMasterKey
    Client->>Client: **(本地)** 从主密码派生出 UserPasswordKey (Argon2)
    Client->>Client: **(本地)** 使用 UserPasswordKey 加密 StrongholdMasterKey 得到 EncryptedMasterKeyBlob
    Client->>Server: 发送注册信息 (username) 和 EncryptedMasterKeyBlob
    Server-->>Client: 确认注册成功, 并返回 DeviceRefreshToken
    
    Note over Client: 注册成功后，直接进行首次设备设置
    Client->>Client: (此时内存中已有 MasterKey)
    Client->>Client: 获取无权限设备标识 DeviceIdentifier
    Client->>Client: 派生出 HybridDeviceKey (RefreshToken + DeviceIdentifier)
    Client->>Client: 使用 HybridDeviceKey 加密 StrongholdMasterKey
    Client->>LocalStorage: 将 (EncryptedMasterKey, RefreshToken) 存入本地 session.json
```

#### 阶段一：已注册用户首次登录或添加新设备
```mermaid
sequenceDiagram
    participant Client as 客户端 (Tauri App)
    participant Server as 后端服务器
    participant Stronghold as Stronghold 实例
    participant LocalStorage as 本地加密文件 (session.json)

    Note over Client,Server: 阶段一：首次登录或添加新设备
    Client->>Client: 用户输入主密码
    Client->>Client:派生 UserPasswordKey
    Client->>Server: 发送登录凭据
    Server-->>Client: 返回 DeviceRefreshToken
    Client->>Server: 请求下载 EncryptedMasterKeyBlob
    Server-->>Client: 返回 EncryptedMasterKeyBlob
    Client->>Client: 使用 UserPasswordKey 解密，在内存获得 StrongholdMasterKey
    Client->>Client: 获取无权限设备标识 DeviceIdentifier
    Client->>Client: 派生出 HybridDeviceKey (RefreshToken + DeviceIdentifier)
    Client->>Client: 使用 HybridDeviceKey 加密 StrongholdMasterKey
    Client->>LocalStorage: 将 (EncryptedMasterKey, RefreshToken) 存入本地

    Note over Client,LocalStorage: 阶段二：日常无缝解锁
    Client->>LocalStorage: 读取 EncryptedMasterKey 和 RefreshToken
    Client->>Client: 获取当前 DeviceIdentifier
    Client->>Client: 重新派生 HybridDeviceKey
    Client->>Client: 使用 HybridDeviceKey 解密 EncryptedMasterKey
    alt 解密成功
        Client->>Client: 在内存中获得 StrongholdMasterKey
        Client->>Stronghold: 使用 MasterKey 初始化并解锁实例
    else 解密失败
        Client->>Client: 清理本地会话，引导用户重新登录
    end
```

### 2.2. 核心组件

- **`StrongholdManager` (Rust)**: 核心业务逻辑封装，负责整个生命周期的管理。
- **`StrongholdSessionManager` (Rust State)**: **(新增)** 用于在内存中安全缓存 `StrongholdMasterKey` 的Tauri托管状态，解决性能瓶颈的关键。
- **`DeviceIdentifierProvider` (Rust)**: 平台差异化模块，负责获取无权限的设备ID。
- **`ApiClient` (Rust/TS)**: 负责与后端服务器通信，获取/刷新令牌，同步数据。
- **`session.json` (本地文件)**: 存储加密后的主密钥和刷新令牌。

### 2.3. 性能优化：一次性解锁与会话密钥

**问题根源**: 直接使用用户密码初始化 Stronghold 会导致每次读/写/保存操作都触发一次极其耗时（可能长达数分钟）的 Argon2id 密钥派生计算，严重影响用户体验。

**解决方案**:
1.  **一次性昂贵操作**: 仅在用户登录或应用首次解锁时，执行一次从用户主密码到 `StrongholdMasterKey` 的派生。这个过程慢是可以接受的。
2.  **内存缓存密钥**: 将派生出的高熵 `StrongholdMasterKey` 缓存在一个安全的、由 Tauri 托管的 Rust 状态 (`StrongholdSessionManager`) 中。
3.  **瞬时快速操作**: 后续所有的 Stronghold 操作（读、写、保存）都通过一个特殊的 `KeyProvider` 直接从内存中获取 `StrongholdMasterKey`。这个过程是毫秒级的，完全消除了性能瓶颈。
4.  **登出即销毁**: 用户登出或锁定应用时，立即从内存中清除 `StrongholdMasterKey`，确保安全。

#### 2.3.1. 关键代码实现示例

以下代码片段展示了如何实现此优化方案的核心逻辑。

**1. 会话状态管理器 (`src-tauri/src/stronghold_manager/session.rs`)**
```rust
use std::sync::{Arc, Mutex};

// 这个结构将作为 Tauri 的托管状态存在，确保在应用的整个生命周期中只有一个实例。
#[derive(Default)]
pub struct StrongholdSessionManager {
    // 使用 Arc<Mutex<...>> 来确保在多线程环境中（Tauri命令是异步的）安全地访问密钥。
    // Option<Vec<u8>> 表示密钥可能存在（已解锁）或不存在（未解锁）。
    master_key: Arc<Mutex<Option<Vec<u8>>>>,
}

impl StrongholdSessionManager {
    // 登录成功后，调用此方法将主密钥存入内存。
    pub fn set_key(&self, key: Vec<u8>) {
        let mut guard = self.master_key.lock().unwrap();
        *guard = Some(key);
    }

    // Builder 闭包和后续操作会调用此方法来获取密钥。
    pub fn get_key(&self) -> Option<Vec<u8>> {
        let guard = self.master_key.lock().unwrap();
        guard.clone()
    }

    // 登出时调用，立即清除内存中的密钥。
    pub fn clear_key(&self) {
        let mut guard = self.master_key.lock().unwrap();
        *guard = None;
    }
}
```

**2. 在 `main.rs` 中初始化插件和状态**
```rust
// 引入我们的新模块和状态
use stronghold_manager::session::StrongholdSessionManager;
use tauri_plugin_stronghold::{TauriStronghold, Error as StrongholdError};

fn main() {
    tauri::Builder::default()
        // 1. 将我们的会话管理器注册为 Tauri 的托管状态
        .manage(StrongholdSessionManager::default())

        // 2. 改造 Stronghold 插件的初始化闭包
        .plugin(tauri_plugin_stronghold::Builder::new(|app, _password| {
            // 从 Tauri 状态中获取我们的会话管理器实例
            let session_manager = app.state::<StrongholdSessionManager>();
            
            // 尝试从内存中获取密钥
            if let Some(key) = session_manager.get_key() {
                // 如果密钥存在，直接返回。这是毫秒级的快速路径！
                Ok(key)
            } else {
                // 如果密钥不存在（例如应用刚启动，用户还未登录），则返回错误。
                // 这会阻止 Stronghold 在未授权的情况下被访问。
                Err(StrongholdError::Key("Master key not found in session. Please login.".to_string()))
            }
        }).build())
        .invoke_handler(tauri::generate_handler![
            login_and_unlock,
            logout_and_lock,
            // ... 其他命令
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

**3. 实现登录/登出命令**
```rust
// 这是一个伪代码函数，代表了您从用户密码派生出最终主密钥的完整逻辑
fn derive_master_key_from_password(password: &str) -> Vec<u8> {
    println!("Performing expensive, one-time key derivation...");
    // 在这里实现:
    // 1. 调用 Argon2 从 password 派生 UserPasswordKey
    // 2. 从服务器或本地文件获取 EncryptedMasterKeyBlob
    // 3. 解密得到 StrongholdMasterKey
    // 4. 返回这个 Vec<u8>
    // 此处使用一个模拟的高熵密钥：
    vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
}

#[tauri::command]
async fn login_and_unlock(
    password: String, // 从前端传来
    session_manager: tauri::State<'_, StrongholdSessionManager>,
    stronghold: tauri::State<'_, TauriStronghold> // 注入 stronghold 实例来预热
) -> Result<(), String> {
    let master_key = derive_master_key_from_password(&password);
    
    // 将派生出的主密钥存入会话管理器
    session_manager.set_key(master_key);

    // (推荐) 立即尝试加载一次 client，以确保密码正确并预热 Stronghold
    // 这会触发上面 Builder 的闭包，并使用刚存入的密钥
    stronghold.get_or_create_client("default_client").map_err(|e| e.to_string())?;

    println!("Stronghold unlocked and session key is cached in memory.");
    Ok(())
}

#[tauri::command]
async fn logout_and_lock(
    session_manager: tauri::State<'_, StrongholdSessionManager>,
    stronghold: tauri::State<'_, TauriStronghold>
) -> Result<(), String> {
    // 在锁定前，确保所有内存中的更改都已写入快照文件
    stronghold.save(None).map_err(|e| e.to_string())?;
    
    // 从内存中清除主密钥，这是最关键的安全步骤
    session_manager.clear_key();
    
    println!("Stronghold locked and session key cleared from memory.");
    Ok(())
}
```

---

## 3. 模块化实现计划

我们将项目分解为多个独立的 Rust 模块，以实现高内聚、低耦合。所有模块将位于 `src-tauri/src/stronghold_manager/` 目录下。

### 3.1. 文件结构

```
src-tauri/src/
├── stronghold_manager/
│   ├── mod.rs                # 公开模块 API
│   ├── manager.rs            # 核心管理器，状态机与业务流程
│   ├── config.rs             # 配置信息，如服务器地址
│   ├── device.rs             # 跨平台设备标识符提供者
│   ├── crypto.rs             # 加密/解密/密钥派生逻辑
│   ├── storage_adapter.rs    # 对 tauri-plugin-stronghold 的封装
│   ├── error.rs              # 统一的错误类型
│   └── types.rs              # 模块特定的数据结构
└── ...
```

### 3.2. 模块详解

#### a. `storage_adapter.rs`
- **职责**: 直接与 `tauri-plugin-stronghold` 交互的底层适配器。
- **核心 API**:
  ```rust
  // new(password: &str) -> Self
  // insert(key: &[u8], value: &[u8]) -> Result<(), Error>
  // get(key: &[u8]) -> Result<Option<Vec<u8>>, Error>
  // remove(key: &[u8]) -> Result<(), Error>
  // save_and_lock() -> Result<(), Error>
  ```
- **实现细节**: 内部将不再直接使用 `.password(password)`。取而代之，`TauriStronghold::builder()` 将使用一个闭包。该闭包会从 Tauri 的托管状态 `StrongholdSessionManager` 中尝试获取内存里的主密钥。如果获取不到，则操作失败，以此保证只有在解锁状态下才能访问 Stronghold。

#### b. `device.rs`
- **职责**: 提供一个统一的、无权限的设备ID。
- **核心 API**:
  ```rust
  // get_device_identifier() -> Result<String, Error>
  ```
- **实现细节**:
  - **桌面 (Win, Mac, Linux)**: 使用 `machine-uid` crate。
  - **iOS**: 使用 `UIDevice.current.identifierForVendor`。需要通过 Swift/Objective-C 编写插件或通过 `invoke` 调用原生代码。
  - **Android**: 使用 `Settings.Secure.ANDROID_ID`。需要通过 Kotlin/Java 编写插件或通过 `invoke` 调用原生代码。

#### c. `crypto.rs`
- **职责**: 封装所有加密操作和密钥派生。
- **核心 API**:
  ```rust
  // derive_key_from_password(password: &str, salt: &[u8]) -> Vec<u8> (Argon2)
  // derive_hybrid_key(token: &str, device_id: &str) -> Vec<u8> (HKDF)
  // encrypt(data: &[u8], key: &[u8]) -> Result<Vec<u8>, Error> (AES-256-GCM)
  // decrypt(encrypted_data: &[u8], key: &[u8]) -> Result<Vec<u8>, Error>
  ```

#### d. `manager.rs`
- **职责**: 整个模块的大脑，协调其他模块完成核心流程。
- **核心 API (Tauri Commands)**:
  ```rust
  // #[tauri::command]
  // async fn register_and_initialize(password: &str) -> Result<(), Error>
  // #[tauri::command]
  // async fn login_and_unlock(password: &str) -> Result<(), Error>
  // #[tauri::command]
  // async fn unlock_from_session() -> Result<bool, Error> // 返回是否解锁成功
  // #[tauri::command]
  // async fn store_secret(key: String, value: String) -> Result<(), Error>
  // #[tauri::command]
  // async fn retrieve_secret(key: String) -> Result<Option<String>, Error>
  // #[tauri::command]
  // async fn logout_and_lock() -> Result<(), Error>
  ```

---

## 4. 增量开发实施路线图

我们将开发过程分为四个里程碑，每个里程碑都有明确的目标、任务和验收标准。

### 📍 里程碑 M1: 核心桌面端实现 (预计时间: 1周)

**目标**: 在桌面端 (Windows/macOS/Linux) 实现完整的、基于内存会话密钥的高性能无缝解锁流程。

**任务清单**:
- [ ] 1. 创建 `src-tauri/src/stronghold_manager/` 目录和所有模块文件，包括用于状态管理的 `session.rs`。
- [ ] 2. 在 `Cargo.toml` 中添加依赖: `tauri-plugin-stronghold`, `machine-uid`, `argon2`, `hkdf`, `aes-gcm`, `serde`, `rand`。
- [ ] 3. 创建 `StrongholdSessionManager` 结构体，并将其注册为 Tauri 的托管状态。
- [ ] 4. 修改 `lib.rs` 中 `tauri-plugin-stronghold` 的初始化逻辑，使其 `Builder` 闭包从 `StrongholdSessionManager` 中获取密钥。
- [ ] 5. 实现 `login_and_unlock` 命令：执行昂贵的密钥派生，并将最终的 `StrongholdMasterKey` 存入 `StrongholdSessionManager`。
- [ ] 6. 实现 `store_secret` 和 `retrieve_secret` 命令，它们将受益于快速的内存密钥访问。
- [ ] 7. 实现 `logout_and_lock` 命令，负责清理 `StrongholdSessionManager` 中的密钥和保存快照。
- [ ] 8. 编写单元测试和集成测试，重点验证状态管理、登录/登出逻辑和性能。

**验收标准**:
- ✅ 所有测试通过。
- ✅ **性能验收**: 登录操作可能耗时几秒，但后续的 `store` 和 `retrieve` 操作必须是毫秒级的。
- ✅ 应用首次启动时调用 `login_and_unlock`，可以成功创建并解锁 Stronghold。
- ✅ 调用 `logout_and_lock` 后，任何 `store/retrieve` 操作都会失败。
- ✅ 在未登出的情况下关闭并重新打开应用后（自动解锁流程），无需输入密码即可成功解锁并读取数据。
- ✅ 删除本地 `session.json` 文件后，自动解锁失败，需要用户重新登录。

### 📍 里程碑 M2: 移动端适配与验证 (预计时间: 1-2周)

**目标**: 将 M1 的功能无缝扩展到 iOS 和 Android 平台。

**任务清单**:
- [ ] 1. **(关键任务)** 实现 `device.rs` 的移动端部分。这可能需要：
    - 创建一个小的 Tauri 插件或使用 `execute_native_async` (如果适用)。
    - 在 `src-tauri/gen/apple/Sources/` 和 `src-tauri/gen/android/app/src/main/java/` 中添加原生代码来获取 `identifierForVendor` 和 `ANDROID_ID`。
    - 将原生代码获取的值通过事件或回调传递给 Rust。
- [ ] 2. 调整文件路径处理，确保在移动端沙盒文件系统中正确读写 `session.json` 和 Stronghold 快照。
- [ ] 3. 在 iOS 模拟器和 Android 模拟器上编译并运行应用。
- [ ] 4. 在移动端上完整测试 M1 的所有功能。

**验收标准**:
- ✅ M1 的所有验收标准在 iOS 和 Android 上都得到满足。
- ✅ 能够在真实设备上成功获取设备ID并完成自动解锁流程。

### 📍 里程碑 M3: 后端集成与端到端测试 (预计时间: 1周)

**目标**: 替换模拟的令牌，与真实的后端服务器对接，实现完整的设备认证和管理流程。

**任务清单**:
- [ ] 1. 搭建一个简单的后端服务，提供以下接口：
    - `POST /auth/register`
    - `POST /auth/login`
    - `POST /auth/refresh_token`
    - `GET /storage/master_key`
    - `POST /devices/revoke`
- [ ] 2. 在 `manager.rs` 中集成 `reqwest` 或其他 HTTP 客户端。
- [ ] 3. 修改 `initialize_with_password` 流程，使其在登录成功后从服务器获取真实的 `DeviceRefreshToken` 和 `EncryptedMasterKeyBlob`。
- [ ] 4. 实现令牌刷新逻辑，在应用启动时静默刷新令牌。
- [ ] 5. 在前端设置页面实现"设备管理"功能，允许用户查看已登录设备并"吊销"它们。

**验收标准**:
- ✅ 用户可以在桌面端登录，然后在新安装的移动端应用上登录，并访问到相同的数据。
- ✅ 从一台设备上吊销另一台设备后，被吊销的设备在下次启动时会自动退出登录。
- ✅ 端到端集成测试通过。

### 📍 里程碑 M4: 健壮性与生产准备 (预计时间: 1周)

**目标**: 对模块进行加固，优化性能和错误处理，为生产环境发布做准备。

**任务清单**:
- [ ] 1. **错误处理**: 对所有网络请求、文件IO和加密操作增加详尽的错误处理和用户提示。
- [ ] 2. **性能分析**: 分析启动解锁过程的耗时，确保在低端设备上也能快速完成。
- [ ] 3. **安全审计**:
    - 代码审查，确保没有逻辑漏洞。
    - 确保所有敏感数据（如内存中的主密钥）在不再需要时被立即清理。
    - 检查依赖项是否存在已知的安全漏洞。
- [ ] 4. **文档完善**: 为模块的公开 API 编写完整的文档注释。
- [ ] 5. **代码清理**: 移除所有测试用的硬编码和日志。

**验收标准**:
- ✅ 测试覆盖率达到 95% 以上。
- ✅ 模块在所有目标平台上稳定运行，无明显性能问题或崩溃。
- ✅ 安全审计通过，无已知高风险漏洞。
- ✅ 文档完整清晰。 