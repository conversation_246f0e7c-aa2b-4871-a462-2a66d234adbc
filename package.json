{"name": "secure-password", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "tauri": "cross-env RUST_BACKTRACE=1 tauri", "dev:android": "cross-env RUST_BACKTRACE=1 tauri android dev", "dev:ios": "echo 8 | cross-env RUST_BACKTRACE=1 tauri ios dev", "dev:ios:clean": "./scripts/dev-ios.sh", "setup:ios": "./scripts/setup-ios-signing.sh", "dev:fast": "cross-env RUST_BACKTRACE=1 RUST_LOG=info tauri dev -- --profile fast-dev", "build:fast": "cross-env NODE_OPTIONS='--max-old-space-size=4096' tauri build -- --profile fast-release"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@radix-ui/react-slot": "^1.2.3", "@skipperndt/plugin-machine-uid": "^0.1.0", "@tailwindcss/vite": "^4.1.7", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-biometric": "^2.2.1", "@tauri-apps/plugin-clipboard-manager": "^2.2.2", "@tauri-apps/plugin-http": "^2.4.4", "@tauri-apps/plugin-notification": "^2.2.2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-os": "^2.2.1", "@tauri-apps/plugin-store": "^2.2.0", "@tauri-apps/plugin-stronghold": "^2.2.0", "antd": "^5.25.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "pinyin": "3.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-use": "^17.6.0", "shadcn": "^2.5.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@tauri-apps/cli": "^2.5.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.15.21", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "jsdom": "^26.1.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "~5.6.2", "vite": "^6.0.3", "vitest": "^3.2.3"}}