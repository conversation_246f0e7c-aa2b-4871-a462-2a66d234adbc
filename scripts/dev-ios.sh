#!/bin/bash

# iOS 开发脚本 - 简化开发流程并过滤重复符号警告

echo "🚀 启动 iOS 开发环境..."

# 设置环境变量
export RUST_BACKTRACE=1
export RUST_LOG=info

# 检查 iOS 模拟器是否运行
if ! xcrun simctl list devices | grep -q "Booted"; then
    echo "📱 启动 iOS 模拟器..."
    # 启动 iPhone 16 Pro 模拟器
    xcrun simctl boot C1F5A910-CCF5-429D-90AC-9A456B45BA82 2>/dev/null || true
    sleep 5
fi

echo "🔨 开始构建和部署..."

# 运行 Tauri iOS 开发命令，过滤重复符号警告
echo 8 | npm run dev:ios 2>&1 | grep -v "ld: warning: duplicate symbol" | grep -v "note: Run script build phase"

echo "✅ iOS 开发环境已启动" 