#!/bin/bash

# iOS 代码签名配置脚本

echo "🔐 配置 iOS 代码签名..."

# 设置变量
TEAM_ID="WS299J65H4"
SIGNING_IDENTITY="Developer ID Application: Beijing Qihoo Technology Co Ltd (WS299J65H4)"
BUNDLE_ID="com.secure-password.app"
PROJECT_DIR="src-tauri/gen/apple"
ENTITLEMENTS_FILE="$PROJECT_DIR/secure-password_iOS/secure-password_iOS.entitlements"

# 检查证书是否存在
echo "📋 检查可用的代码签名证书..."
security find-identity -v -p codesigning

# 检查开发者团队
echo "👥 使用开发者团队: $TEAM_ID"
echo "🔑 使用签名身份: $SIGNING_IDENTITY"

# 验证 entitlements 文件
if [ -f "$ENTITLEMENTS_FILE" ]; then
    echo "✅ Entitlements 文件存在: $ENTITLEMENTS_FILE"
    echo "📄 Entitlements 内容:"
    cat "$ENTITLEMENTS_FILE"
else
    echo "❌ Entitlements 文件不存在: $ENTITLEMENTS_FILE"
    exit 1
fi

# 检查 Xcode 项目配置
PBXPROJ_FILE="$PROJECT_DIR/secure-password.xcodeproj/project.pbxproj"
if [ -f "$PBXPROJ_FILE" ]; then
    echo "✅ Xcode 项目文件存在"
    
    # 检查签名配置
    if grep -q "$TEAM_ID" "$PBXPROJ_FILE"; then
        echo "✅ 开发者团队已配置"
    else
        echo "⚠️  开发者团队未配置"
    fi
    
    if grep -q "Apple Development" "$PBXPROJ_FILE"; then
        echo "✅ 签名身份已配置"
    else
        echo "⚠️  签名身份未配置"
    fi
else
    echo "❌ Xcode 项目文件不存在: $PBXPROJ_FILE"
    exit 1
fi

# 检查模拟器状态
echo "📱 检查 iOS 模拟器状态..."
xcrun simctl list devices | grep "Booted" || echo "⚠️  没有运行中的模拟器"

echo "🎉 iOS 代码签名配置检查完成！"
echo ""
echo "💡 使用提示:"
echo "   - 运行 'npm run dev:ios' 启动 iOS 开发"
echo "   - 如果遇到签名问题，请检查 Xcode 中的签名设置"
echo "   - 确保在 Xcode 中选择了正确的开发者团队" 