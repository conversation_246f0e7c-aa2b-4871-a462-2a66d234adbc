# Cargo 配置文件 - 优化构建过程

[build]
# 针对不同平台的优化配置
target-dir = "target"

# iOS 特定配置
[target.aarch64-apple-ios]
# 减少重复符号警告
rustflags = [
    "-C", "link-arg=-Wl,-dead_strip",
    "-C", "link-arg=-Wl,-no_duplicate_dylibs",
]

[target.aarch64-apple-ios-sim]
# iOS 模拟器配置
rustflags = [
    "-C", "link-arg=-Wl,-dead_strip",
    "-C", "link-arg=-Wl,-no_duplicate_dylibs",
]

# macOS 配置
[target.aarch64-apple-darwin]
rustflags = [
    "-C", "link-arg=-Wl,-dead_strip",
]

[target.x86_64-apple-darwin]
rustflags = [
    "-C", "link-arg=-Wl,-dead_strip",
]

# 通用配置
[env]
# 设置环境变量以减少警告
RUST_LOG = "info"
# 针对 iOS 开发的特定配置
IPHONEOS_DEPLOYMENT_TARGET = "13.0" 