/// 集成示例：在现有项目中使用 AsyncHandler
///
/// 这个文件展示了如何将 AsyncHandler 集成到现有的 Tauri 项目中，
/// 替换直接使用 tauri::async_runtime 的地方。
use crate::async_handler::simple_api::AsyncHandler;
use crate::async_handler::AsyncTaskManager;
use std::time::Duration;
use tauri::{A<PERSON><PERSON><PERSON><PERSON>, Emitter, Manager};
// 添加 FutureExt trait

/// 示例：在数据库初始化中使用 AsyncHandler
pub fn setup_database_with_async_handler(app_handle: AppHandle) {
    // 使用简化 API
    AsyncHandler::spawn(|| async move {
        match initialize_database().await {
            Ok(_) => {
                log::info!("数据库初始化成功");
                // 通知前端数据库已准备就绪
                if let Some(window) = app_handle.get_webview_window("main") {
                    let _ = window.emit("db_ready", ());
                }
            }
            Err(e) => {
                log::error!("数据库初始化失败: {}", e);
                if let Some(window) = app_handle.get_webview_window("main") {
                    let _ = window.eval(format!("alert('数据库初始化失败: {}')", e));
                }
            }
        }
    });
}

/// 示例：在 Native Messaging 中使用 AsyncHandler
pub fn setup_native_messaging_with_async_handler(app_handle: AppHandle) {
    // 使用高级 API 的重试机制
    AsyncTaskManager::spawn_task_with_retry(
        move || {
            let handle = app_handle.clone();
            async move {
                match start_native_messaging_listener(handle).await {
                    Ok(_) => {
                        log::info!("Native Messaging 启动成功");
                        Ok(())
                    }
                    Err(e) => {
                        log::warn!("Native Messaging 启动失败，将重试: {}", e);
                        Err(e)
                    }
                }
            }
        },
        3,                      // 重试 3 次
        Duration::from_secs(2), // 每次重试间隔 2 秒
    );
}

/// 示例：在 Tauri 命令中使用阻塞任务
#[tauri::command]
pub async fn secure_file_operation(file_path: String) -> Result<String, String> {
    // 使用阻塞任务处理文件 I/O，避免阻塞异步运行时
    let handle = AsyncHandler::spawn_blocking(move || {
        // 模拟安全的文件操作
        match std::fs::read_to_string(&file_path) {
            Ok(content) => {
                // 模拟一些 CPU 密集型的安全处理
                let processed = content
                    .lines()
                    .map(|line| format!("安全处理: {}", line))
                    .collect::<Vec<_>>()
                    .join("\n");
                Ok(processed)
            }
            Err(e) => Err(format!("文件读取失败: {}", e)),
        }
    });

    handle.await.map_err(|e| format!("任务执行失败: {:?}", e))?
}

/// 示例：批量加密操作
#[tauri::command]
pub async fn batch_encrypt_passwords(passwords: Vec<String>) -> Result<Vec<String>, String> {
    // 使用并发任务管理器处理多个加密操作
    let encryption_tasks: Vec<
        std::pin::Pin<Box<dyn std::future::Future<Output = Result<String, String>> + Send>>,
    > = passwords
        .into_iter()
        .map(|password| {
            Box::pin(async move {
                let result = AsyncHandler::spawn_blocking(move || {
                    // 模拟 CPU 密集型的加密操作
                    std::thread::sleep(Duration::from_millis(50)); // 模拟加密时间
                    format!("encrypted_{}", password)
                })
                .await;

                result.map_err(|e| format!("加密任务失败: {:?}", e))
            })
                as std::pin::Pin<
                    Box<dyn std::future::Future<Output = Result<String, String>> + Send>,
                >
        })
        .collect();

    let handle = AsyncTaskManager::join_all_tasks(encryption_tasks);

    match handle.await {
        Ok(results) => {
            // 收集所有成功的结果，如果有任何失败则返回错误
            let mut encrypted_passwords = Vec::new();
            for result in results {
                match result {
                    Ok(encrypted) => encrypted_passwords.push(encrypted),
                    Err(e) => return Err(e),
                }
            }
            Ok(encrypted_passwords)
        }
        Err(e) => Err(format!("批量加密失败: {:?}", e)),
    }
}

/// 示例：带超时的网络请求
#[tauri::command]
pub async fn fetch_with_timeout(url: String, timeout_secs: u64) -> Result<String, String> {
    let handle = AsyncTaskManager::spawn_task_with_timeout(
        async move {
            // 模拟网络请求
            reqwest::get(&url)
                .await
                .map_err(|e| format!("请求失败: {}", e))?
                .text()
                .await
                .map_err(|e| format!("读取响应失败: {}", e))
        },
        Duration::from_secs(timeout_secs),
    );

    match handle.await {
        Ok(Ok(Ok(content))) => Ok(content),
        Ok(Ok(Err(e))) => Err(e),
        Ok(Err(_)) => Err("请求超时".to_string()),
        Err(e) => Err(format!("任务执行失败: {:?}", e)),
    }
}

/// 示例：如何替换现有的 tauri::async_runtime 调用
pub mod migration_examples {
    use super::*;

    /// 旧的方式（直接使用 tauri::async_runtime）
    #[allow(dead_code)]
    pub fn old_way(_app_handle: AppHandle) {
        tauri::async_runtime::spawn(async move {
            // 异步操作
            tokio::time::sleep(Duration::from_millis(100)).await;
            log::info!("旧方式完成");
        });
    }

    /// 新的方式（使用 AsyncHandler）
    pub fn new_way(_app_handle: AppHandle) {
        AsyncHandler::spawn(|| async move {
            // 异步操作
            tokio::time::sleep(Duration::from_millis(100)).await;
            log::info!("新方式完成");
        });
    }

    /// 旧的方式（阻塞任务）
    #[allow(dead_code)]
    pub fn old_blocking_way() {
        tauri::async_runtime::spawn_blocking(|| {
            // 阻塞操作
            std::thread::sleep(Duration::from_millis(100));
            "阻塞操作完成"
        });
    }

    /// 新的方式（阻塞任务）
    pub fn new_blocking_way() {
        AsyncHandler::spawn_blocking(|| {
            // 阻塞操作
            std::thread::sleep(Duration::from_millis(100));
            "阻塞操作完成"
        });
    }

    /// 旧的方式（同步等待）
    #[allow(dead_code)]
    pub fn old_block_on_way() -> String {
        tauri::async_runtime::block_on(async { "同步等待结果".to_string() })
    }

    /// 新的方式（同步等待）
    pub fn new_block_on_way() -> String {
        AsyncHandler::block_on(|| async { "同步等待结果".to_string() })
    }
}

// 模拟函数（在实际项目中这些应该是真实的实现）
async fn initialize_database() -> Result<(), String> {
    tokio::time::sleep(Duration::from_millis(100)).await;
    Ok(())
}

async fn start_native_messaging_listener(_app_handle: AppHandle) -> Result<(), String> {
    tokio::time::sleep(Duration::from_millis(50)).await;
    Ok(())
}
