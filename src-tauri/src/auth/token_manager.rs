use crate::auth::models::RemoteTokenInfo;
use anyhow::Result;
use chrono::{DateTime, Duration, Utc};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tauri::AppHandle;
use tauri_plugin_store::StoreExt;
use tokio::sync::RwLock;

/// Token 管理器
pub struct TokenManager {
    token_info: Arc<RwLock<Option<StoredTokenInfo>>>,
    app_handle: Option<AppHandle>,
}

/// 存储的 Token 信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StoredTokenInfo {
    /// 访问令牌
    pub access_token: String,
    /// 刷新令牌
    pub refresh_token: String,
    /// 令牌类型
    pub token_type: String,
    /// 过期时间
    pub expires_at: DateTime<Utc>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

impl Default for TokenManager {
    fn default() -> Self {
        Self::new()
    }
}

impl TokenManager {
    /// 创建新的 Token 管理器
    pub fn new() -> Self {
        Self {
            token_info: Arc::new(RwLock::new(None)),
            app_handle: None,
        }
    }

    /// 初始化 Token 管理器，设置 AppHandle 并从持久化存储加载 Token
    pub async fn initialize(&mut self, app_handle: AppHandle) -> Result<()> {
        self.app_handle = Some(app_handle);
        self.load_from_store().await?;
        Ok(())
    }

    /// 从 Store 加载 Token 信息
    async fn load_from_store(&self) -> Result<()> {
        if let Some(ref app_handle) = self.app_handle {
            let store = app_handle.store("tokens.json")?;

            if let Some(token_data) = store.get("token_info") {
                if let Ok(stored_token) = serde_json::from_value::<StoredTokenInfo>(token_data) {
                    let mut token_store = self.token_info.write().await;
                    *token_store = Some(stored_token);
                    log::info!("Token 已从持久化存储加载");
                }
            }
        }
        Ok(())
    }

    /// 保存 Token 信息到 Store
    async fn save_to_store(&self, token_info: &StoredTokenInfo) -> Result<()> {
        if let Some(ref app_handle) = self.app_handle {
            let store = app_handle.store("tokens.json")?;
            let token_value = serde_json::to_value(token_info)?;
            store.set("token_info", token_value);
            store.save()?;
            log::info!("Token 已保存到持久化存储");
        }
        Ok(())
    }

    /// 从 Store 删除 Token 信息
    async fn remove_from_store(&self) -> Result<()> {
        if let Some(ref app_handle) = self.app_handle {
            let store = app_handle.store("tokens.json")?;
            store.delete("token_info");
            store.save()?;
            log::info!("Token 已从持久化存储删除");
        }
        Ok(())
    }

    /// 存储 Token 信息
    pub async fn store_token(&self, token_info: RemoteTokenInfo) -> Result<()> {
        let stored_token = StoredTokenInfo {
            access_token: token_info.access_token,
            refresh_token: token_info.refresh_token,
            token_type: token_info.token_type,
            expires_at: Utc::now() + Duration::seconds(token_info.expires_in),
            created_at: Utc::now(),
        };

        let expires_at = stored_token.expires_at;

        // 保存到内存
        let mut token_store = self.token_info.write().await;
        *token_store = Some(stored_token.clone());

        // 保存到持久化存储
        self.save_to_store(&stored_token).await?;

        log::info!("Token 已存储，过期时间: {}", expires_at);
        Ok(())
    }

    /// 获取当前 Token
    pub async fn get_token(&self) -> Option<String> {
        let token_store = self.token_info.read().await;

        if let Some(ref token_info) = *token_store {
            // 检查 Token 是否过期
            if Utc::now() < token_info.expires_at {
                Some(token_info.access_token.clone())
            } else {
                log::warn!("Token 已过期");
                None
            }
        } else {
            None
        }
    }

    /// 获取刷新令牌
    pub async fn get_refresh_token(&self) -> Option<String> {
        let token_store = self.token_info.read().await;

        (*token_store).as_ref().map(|token_info| token_info.refresh_token.clone())
    }

    /// 检查 Token 是否即将过期（5分钟内）
    pub async fn is_token_expiring_soon(&self) -> bool {
        let token_store = self.token_info.read().await;

        if let Some(ref token_info) = *token_store {
            let expiry_threshold = Utc::now() + Duration::minutes(5);
            token_info.expires_at <= expiry_threshold
        } else {
            true // 没有 Token 视为需要刷新
        }
    }

    /// 清除 Token
    pub async fn clear_token(&self) -> Result<()> {
        // 从内存清除
        let mut token_store = self.token_info.write().await;
        *token_store = None;

        // 从持久化存储清除
        self.remove_from_store().await?;

        log::info!("Token 已清除");
        Ok(())
    }

    /// 获取 Token 信息（用于调试）
    pub async fn get_token_info(&self) -> Option<StoredTokenInfo> {
        let token_store = self.token_info.read().await;
        token_store.clone()
    }

    /// 刷新 Token（从持久化存储重新加载）
    pub async fn refresh_from_store(&self) -> Result<()> {
        self.load_from_store().await
    }
}

lazy_static::lazy_static! {
    /// 全局 Token 管理器实例
    pub static ref GLOBAL_TOKEN_MANAGER: Arc<RwLock<TokenManager>> = Arc::new(RwLock::new(TokenManager::new()));
}

/// 初始化全局 Token 管理器
pub async fn initialize_global_token_manager(app_handle: AppHandle) -> Result<()> {
    let mut manager = GLOBAL_TOKEN_MANAGER.write().await;
    manager.initialize(app_handle).await?;
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::auth::models::RemoteTokenInfo;

    #[tokio::test]
    async fn test_token_storage_and_retrieval() {
        let manager = TokenManager::new();

        // 创建测试 Token 信息
        let _test_token = RemoteTokenInfo {
            access_token: "test_access_token".to_string(),
            refresh_token: "test_refresh_token".to_string(),
            token_type: "Bearer".to_string(),
            expires_in: 3600, // 1小时
        };

        // 注意：在实际测试中，需要 AppHandle 来测试持久化功能
        // 这里只测试内存存储功能

        // 测试 Token 是否为空
        assert!(manager.get_token().await.is_none());
        assert!(manager.get_refresh_token().await.is_none());
        assert!(manager.is_token_expiring_soon().await);

        // 由于没有 AppHandle，我们无法测试完整的持久化功能
        // 但可以测试基本的内存操作
        log::info!("Token 管理器基础功能测试完成");
    }

    #[test]
    fn test_stored_token_info_serialization() {
        let stored_token = StoredTokenInfo {
            access_token: "test_access".to_string(),
            refresh_token: "test_refresh".to_string(),
            token_type: "Bearer".to_string(),
            expires_at: Utc::now() + Duration::hours(1),
            created_at: Utc::now(),
        };

        // 测试序列化和反序列化
        let serialized = serde_json::to_string(&stored_token).unwrap();
        let deserialized: StoredTokenInfo = serde_json::from_str(&serialized).unwrap();

        assert_eq!(stored_token.access_token, deserialized.access_token);
        assert_eq!(stored_token.refresh_token, deserialized.refresh_token);
        assert_eq!(stored_token.token_type, deserialized.token_type);

        log::info!("Token 序列化测试完成");
    }
}
