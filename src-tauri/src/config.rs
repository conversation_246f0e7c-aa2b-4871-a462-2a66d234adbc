use once_cell::sync::Lazy;
use std::env;

/// 应用配置结构
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct AppConfig {
    /// 服务端基础 URL
    pub server_base_url: String,
    /// 日志级别
    pub log_level: String,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            server_base_url: "http://39.107.78.133".to_string(),
            log_level: "info".to_string(),
        }
    }
}

impl AppConfig {
    /// 从环境变量加载配置
    pub fn from_env() -> Self {
        Self {
            server_base_url: env::var("VITE_SERVER_BASE_URL")
                .or_else(|_| env::var("SERVER_BASE_URL"))
                .unwrap_or_else(|_| "http://39.107.78.133".to_string()),
            log_level: env::var("RUST_LOG").unwrap_or_else(|_| "info".to_string()),
        }
    }

    /// 初始化配置（加载环境变量文件）
    pub fn init() -> Self {
        // 根据构建模式加载不同的环境变量文件
        #[cfg(debug_assertions)]
        {
            // 开发模式：优先加载 .env.development
            if dotenv::from_filename(".env.development").is_err() {
                dotenv::dotenv().ok();
            }
        }

        #[cfg(not(debug_assertions))]
        {
            // 生产模式：优先加载 .env.production
            if dotenv::from_filename(".env.production").is_err() {
                dotenv::dotenv().ok();
            }
        }

        Self::from_env()
    }
}

/// 全局配置实例
pub static CONFIG: Lazy<AppConfig> = Lazy::new(AppConfig::init);
