// 系统密钥链集成模块
// 实现跨平台的系统密钥链访问功能

use crate::errors::{VaultError, VaultResult};
use base64::{engine::general_purpose::STANDARD as BASE64_STANDARD, Engine as _};
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};
use thiserror::Error;
use zeroize::Zeroize;

// 移动平台特定导入
#[cfg(any(target_os = "android", target_os = "ios"))]
use crate::mobile::{
    KeychainSecureStorage, KeychainSecureStorageFactory, MobileError, SecureStorageProvider,
};
#[cfg(any(target_os = "android", target_os = "ios"))]
use once_cell::sync::Lazy;
#[cfg(any(target_os = "android", target_os = "ios"))]
use std::sync::Arc;
#[cfg(any(target_os = "android", target_os = "ios"))]
use tokio::sync::RwLock;

use super::KEY_SIZE;

/// 密钥链错误类型
#[derive(Error, Debug)]
pub enum KeychainError {
    #[error("密钥链服务不可用")]
    ServiceUnavailable,
    #[error("访问被拒绝")]
    AccessDenied,
    #[error("密钥未找到")]
    KeyNotFound,
    #[error("密钥已存在")]
    KeyExists,
    #[error("无效的服务名称或用户名")]
    InvalidCredentials,
    #[error("系统错误: {0}")]
    SystemError(String),
    #[error("编码错误: {0}")]
    EncodingError(String),
}

impl From<KeychainError> for VaultError {
    fn from(err: KeychainError) -> Self {
        VaultError::InternalError(format!("密钥链错误: {}", err))
    }
}

// 移动平台错误转换
#[cfg(any(target_os = "android", target_os = "ios"))]
impl From<MobileError> for KeychainError {
    fn from(err: MobileError) -> Self {
        match err {
            MobileError::UnsupportedPlatform { .. } => KeychainError::ServiceUnavailable,
            MobileError::SecureStorageError { reason } => KeychainError::SystemError(reason),
            MobileError::PermissionError { permission: _ } => KeychainError::AccessDenied,
            MobileError::InitializationError { reason } => KeychainError::SystemError(reason),
            MobileError::ConfigurationError { reason } => KeychainError::SystemError(reason),
            MobileError::InternalError { reason } => KeychainError::SystemError(reason),
            _ => KeychainError::SystemError(err.to_string()),
        }
    }
}

/// 密钥链条目元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeychainEntry {
    /// 服务名称
    pub service: String,
    /// 用户名/账户名
    pub account: String,
    /// 创建时间戳
    pub created_at: i64,
    /// 最后访问时间戳
    pub last_accessed: i64,
    /// 密钥类型
    pub key_type: KeyType,
}

/// 密钥类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum KeyType {
    /// 主密钥
    MasterKey,
    /// 派生密钥
    DerivedKey,
    /// 加密密钥
    EncryptionKey,
    /// 用户定义
    Custom(String),
}

impl KeyType {
    pub fn description(&self) -> &str {
        match self {
            Self::MasterKey => "主密钥",
            Self::DerivedKey => "派生密钥",
            Self::EncryptionKey => "加密密钥",
            Self::Custom(desc) => desc,
        }
    }
}

// 移动平台全局安全存储实例
#[cfg(any(target_os = "android", target_os = "ios"))]
static MOBILE_SECURE_STORAGE: Lazy<Arc<RwLock<KeychainSecureStorage>>> = Lazy::new(|| {
    log::info!("初始化移动平台全局安全存储实例");
    let storage = KeychainSecureStorageFactory::create_default();
    Arc::new(RwLock::new(storage))
});

/// 跨平台密钥链管理器
pub struct KeychainManager {
    service: String,
    account: String,
}

impl KeychainManager {
    /// 创建新的密钥链管理器
    pub fn new(service: &str, account: &str) -> VaultResult<Self> {
        if service.is_empty() || account.is_empty() {
            return Err(VaultError::InvalidInput(
                "服务名称和账户名不能为空".to_string(),
            ));
        }

        Ok(Self {
            service: service.to_string(),
            account: account.to_string(),
        })
    }

    /// 存储密钥到系统密钥链
    pub fn store_key(&self, key: &[u8; KEY_SIZE]) -> VaultResult<()> {
        self.store_key_with_type(key, KeyType::MasterKey)
    }

    /// 存储指定类型的密钥到系统密钥链
    pub fn store_key_with_type(&self, key: &[u8; KEY_SIZE], key_type: KeyType) -> VaultResult<()> {
        let encoded_key = BASE64_STANDARD.encode(key);

        // 创建元数据
        let metadata = KeychainEntry {
            service: self.service.clone(),
            account: self.account.clone(),
            created_at: current_timestamp(),
            last_accessed: current_timestamp(),
            key_type,
        };

        // 将元数据和密钥一起存储
        let entry_data = KeychainEntryData {
            metadata,
            key: encoded_key,
        };

        let serialized = serde_json::to_string(&entry_data)
            .map_err(|e| VaultError::InternalError(format!("序列化失败: {}", e)))?;

        self.store_password(&serialized)
    }

    /// 从系统密钥链获取密钥
    pub fn get_key(&self) -> VaultResult<[u8; KEY_SIZE]> {
        let serialized = self.get_password()?;

        let entry_data: KeychainEntryData = serde_json::from_str(&serialized)
            .map_err(|e| VaultError::InternalError(format!("反序列化失败: {}", e)))?;

        let key_bytes = BASE64_STANDARD
            .decode(&entry_data.key)
            .map_err(VaultError::Base64Decode)?;

        if key_bytes.len() != KEY_SIZE {
            return Err(VaultError::InternalError(format!(
                "无效的密钥长度: 期望{}字节，实际{}字节",
                KEY_SIZE,
                key_bytes.len()
            )));
        }

        let mut key = [0u8; KEY_SIZE];
        key.copy_from_slice(&key_bytes);

        // 更新最后访问时间（可选）
        // self.update_last_accessed()?;

        Ok(key)
    }

    /// 删除密钥
    pub fn delete_key(&self) -> VaultResult<()> {
        self.delete_password()
    }

    /// 检查密钥是否存在
    pub fn key_exists(&self) -> bool {
        self.get_password().is_ok()
    }

    /// 获取密钥元数据
    pub fn get_metadata(&self) -> VaultResult<KeychainEntry> {
        let serialized = self.get_password()?;

        let entry_data: KeychainEntryData = serde_json::from_str(&serialized)
            .map_err(|e| VaultError::InternalError(format!("反序列化失败: {}", e)))?;

        Ok(entry_data.metadata)
    }

    /// 更新密钥类型
    pub fn update_key_type(&self, new_type: KeyType) -> VaultResult<()> {
        let serialized = self.get_password()?;

        let mut entry_data: KeychainEntryData = serde_json::from_str(&serialized)
            .map_err(|e| VaultError::InternalError(format!("反序列化失败: {}", e)))?;

        entry_data.metadata.key_type = new_type;
        entry_data.metadata.last_accessed = current_timestamp();

        let updated_serialized = serde_json::to_string(&entry_data)
            .map_err(|e| VaultError::InternalError(format!("序列化失败: {}", e)))?;

        self.store_password(&updated_serialized)
    }

    /// 生成存储键名
    ///
    /// 为移动平台生成唯一的存储键名
    #[cfg(any(target_os = "android", target_os = "ios"))]
    fn generate_storage_key(&self) -> String {
        format!("{}:{}", self.service, self.account)
    }

    /// 确保移动安全存储已初始化
    ///
    /// 这是一个异步函数的同步包装器
    #[cfg(any(target_os = "android", target_os = "ios"))]
    fn ensure_mobile_storage_initialized(&self) -> VaultResult<()> {
        // 使用 tokio 的阻塞运行时来执行异步代码
        let rt = tokio::runtime::Runtime::new()
            .map_err(|e| KeychainError::SystemError(format!("创建运行时失败: {}", e)))?;

        rt.block_on(async {
            let storage = MOBILE_SECURE_STORAGE.clone();
            let mut storage_guard = storage.write().await;

            if !storage_guard.is_initialized().await {
                log::info!("初始化移动平台安全存储");
                storage_guard.initialize().await.map_err(|e| {
                    KeychainError::SystemError(format!("初始化安全存储失败: {}", e))
                })?;
            }

            Ok::<(), KeychainError>(())
        })?;

        Ok(())
    }

    // 平台特定的实现

    #[cfg(target_os = "macos")]
    fn store_password(&self, password: &str) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry
            .set_password(password)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        Ok(())
    }

    #[cfg(target_os = "macos")]
    fn get_password(&self) -> VaultResult<String> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry.get_password().map_err(|e| match e {
            keyring::Error::NoEntry => KeychainError::KeyNotFound.into(),
            _ => KeychainError::SystemError(e.to_string()).into(),
        })
    }

    #[cfg(target_os = "macos")]
    fn delete_password(&self) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry
            .delete_password()
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        Ok(())
    }

    #[cfg(target_os = "windows")]
    fn store_password(&self, password: &str) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry
            .set_password(password)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        Ok(())
    }

    #[cfg(target_os = "windows")]
    fn get_password(&self) -> VaultResult<String> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry.get_password().map_err(|e| match e {
            keyring::Error::NoEntry => KeychainError::KeyNotFound.into(),
            _ => KeychainError::SystemError(e.to_string()).into(),
        })
    }

    #[cfg(target_os = "windows")]
    fn delete_password(&self) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry
            .delete_password()
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        Ok(())
    }

    #[cfg(target_os = "linux")]
    fn store_password(&self, password: &str) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry
            .set_password(password)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        Ok(())
    }

    #[cfg(target_os = "linux")]
    fn get_password(&self) -> VaultResult<String> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry.get_password().map_err(|e| match e {
            keyring::Error::NoEntry => KeychainError::KeyNotFound.into(),
            _ => KeychainError::SystemError(e.to_string()).into(),
        })
    }

    #[cfg(target_os = "linux")]
    fn delete_password(&self) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry
            .delete_password()
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        Ok(())
    }

    // 移动平台实现 - 使用 mobile 模块的 KeychainSecureStorage
    #[cfg(any(target_os = "android", target_os = "ios"))]
    fn store_password(&self, password: &str) -> VaultResult<()> {
        log::info!("在移动平台存储密码到安全存储");

        // 确保安全存储已初始化
        self.ensure_mobile_storage_initialized()?;

        let storage_key = self.generate_storage_key();

        // 使用 tokio 运行时执行异步操作
        let rt = tokio::runtime::Runtime::new()
            .map_err(|e| KeychainError::SystemError(format!("创建运行时失败: {}", e)))?;

        rt.block_on(async {
            let storage = MOBILE_SECURE_STORAGE.clone();
            let storage_guard = storage.read().await;

            storage_guard
                .store(&storage_key, password)
                .await
                .map_err(|e| KeychainError::SystemError(format!("存储密码失败: {}", e)))?;

            log::info!("移动平台密码存储成功: {}", storage_key);
            Ok::<(), KeychainError>(())
        })?;

        Ok(())
    }

    #[cfg(any(target_os = "android", target_os = "ios"))]
    fn get_password(&self) -> VaultResult<String> {
        log::info!("从移动平台安全存储获取密码");

        // 确保安全存储已初始化
        self.ensure_mobile_storage_initialized()?;

        let storage_key = self.generate_storage_key();

        // 使用 tokio 运行时执行异步操作
        let rt = tokio::runtime::Runtime::new()
            .map_err(|e| KeychainError::SystemError(format!("创建运行时失败: {}", e)))?;

        let password = rt.block_on(async {
            let storage = MOBILE_SECURE_STORAGE.clone();
            let storage_guard = storage.read().await;

            let result = storage_guard
                .retrieve(&storage_key)
                .await
                .map_err(|e| KeychainError::SystemError(format!("获取密码失败: {}", e)))?;

            match result {
                Some(password) => {
                    log::info!("移动平台密码获取成功: {}", storage_key);
                    Ok(password)
                }
                None => {
                    log::warn!("移动平台密码未找到: {}", storage_key);
                    Err(KeychainError::KeyNotFound)
                }
            }
        })?;

        Ok(password)
    }

    #[cfg(any(target_os = "android", target_os = "ios"))]
    fn delete_password(&self) -> VaultResult<()> {
        log::info!("从移动平台安全存储删除密码");

        // 确保安全存储已初始化
        self.ensure_mobile_storage_initialized()?;

        let storage_key = self.generate_storage_key();

        // 使用 tokio 运行时执行异步操作
        let rt = tokio::runtime::Runtime::new()
            .map_err(|e| KeychainError::SystemError(format!("创建运行时失败: {}", e)))?;

        rt.block_on(async {
            let storage = MOBILE_SECURE_STORAGE.clone();
            let storage_guard = storage.read().await;

            let deleted = storage_guard
                .remove(&storage_key)
                .await
                .map_err(|e| KeychainError::SystemError(format!("删除密码失败: {}", e)))?;

            if deleted {
                log::info!("移动平台密码删除成功: {}", storage_key);
            } else {
                log::warn!("移动平台密码删除失败，可能不存在: {}", storage_key);
            }

            Ok::<(), KeychainError>(())
        })?;

        Ok(())
    }
}

/// 内部数据结构，用于存储密钥和元数据
#[derive(Debug, Serialize, Deserialize)]
struct KeychainEntryData {
    metadata: KeychainEntry,
    key: String, // Base64编码的密钥
}

/// 获取当前时间戳
fn current_timestamp() -> i64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs() as i64
}

/// 安全的密钥链密钥容器
pub struct SecureKeychainKey {
    key: [u8; KEY_SIZE],
    metadata: KeychainEntry,
}

impl SecureKeychainKey {
    /// 从密钥链加载密钥
    pub fn load_from_keychain(manager: &KeychainManager) -> VaultResult<Self> {
        let key = manager.get_key()?;
        let metadata = manager.get_metadata()?;

        Ok(Self { key, metadata })
    }

    /// 获取密钥字节
    pub fn key(&self) -> &[u8; KEY_SIZE] {
        &self.key
    }

    /// 获取元数据
    pub fn metadata(&self) -> &KeychainEntry {
        &self.metadata
    }

    /// 验证密钥是否有效
    pub fn is_valid(&self) -> bool {
        !self.key.iter().all(|&b| b == 0)
    }

    /// 获取密钥年龄（秒）
    pub fn age_seconds(&self) -> i64 {
        current_timestamp() - self.metadata.created_at
    }
}

impl Zeroize for SecureKeychainKey {
    fn zeroize(&mut self) {
        self.key.zeroize();
        // 元数据不需要清理，因为它不包含敏感信息
    }
}

impl Drop for SecureKeychainKey {
    fn drop(&mut self) {
        self.zeroize();
    }
}

/// 批量密钥链操作
pub struct BatchKeychainManager {
    base_service: String,
}

impl BatchKeychainManager {
    /// 创建批量管理器
    pub fn new(base_service: &str) -> Self {
        Self {
            base_service: base_service.to_string(),
        }
    }

    /// 存储多个密钥
    pub fn store_keys(&self, keys: &[(&str, &[u8; KEY_SIZE], KeyType)]) -> VaultResult<()> {
        for (account, key, key_type) in keys {
            let manager = KeychainManager::new(&self.base_service, account)?;
            manager.store_key_with_type(key, key_type.clone())?;
        }
        Ok(())
    }

    /// 加载多个密钥
    pub fn load_keys(&self, accounts: &[&str]) -> VaultResult<Vec<(String, SecureKeychainKey)>> {
        let mut results = Vec::new();

        for account in accounts {
            let manager = KeychainManager::new(&self.base_service, account)?;
            match SecureKeychainKey::load_from_keychain(&manager) {
                Ok(key) => results.push((account.to_string(), key)),
                Err(e) => {
                    log::warn!("无法加载账户 {} 的密钥: {}", account, e);
                    // 继续处理其他密钥，不中断整个操作
                }
            }
        }

        Ok(results)
    }

    /// 删除多个密钥
    pub fn delete_keys(&self, accounts: &[&str]) -> VaultResult<()> {
        for account in accounts {
            let manager = KeychainManager::new(&self.base_service, account)?;
            if let Err(e) = manager.delete_key() {
                log::warn!("无法删除账户 {} 的密钥: {}", account, e);
                // 继续处理其他密钥
            }
        }
        Ok(())
    }
}

/// 注册时的密钥管理器，用于管理主密钥、对称密钥和密钥对
pub struct RegistrationKeychainManager {
    base_service: String,
    contact: String,
}

impl RegistrationKeychainManager {
    /// 创建新的注册密钥管理器
    pub fn new(contact: &str) -> Self {
        Self {
            base_service: "secure-password".to_string(),
            contact: contact.to_string(),
        }
    }

    /// 存储主密钥
    pub fn store_master_key(&self, master_key: &[u8; KEY_SIZE]) -> VaultResult<()> {
        let manager =
            KeychainManager::new(&self.base_service, &format!("{}-master", self.contact))?;
        manager.store_key_with_type(master_key, KeyType::MasterKey)
    }

    /// 存储对称密钥
    pub fn store_symmetric_key(&self, symmetric_key: &[u8; KEY_SIZE]) -> VaultResult<()> {
        let manager =
            KeychainManager::new(&self.base_service, &format!("{}-symmetric", self.contact))?;
        manager.store_key_with_type(symmetric_key, KeyType::EncryptionKey)
    }

    /// 存储私钥（Base64编码的字符串）
    pub fn store_private_key(&self, private_key: &str) -> VaultResult<()> {
        let manager =
            KeychainManager::new(&self.base_service, &format!("{}-private", self.contact))?;

        // 将私钥作为字符串存储
        let entry_data = KeychainEntryData {
            metadata: KeychainEntry {
                service: self.base_service.clone(),
                account: format!("{}-private", self.contact),
                created_at: current_timestamp(),
                last_accessed: current_timestamp(),
                key_type: KeyType::Custom("PrivateKey".to_string()),
            },
            key: private_key.to_string(),
        };

        let serialized = serde_json::to_string(&entry_data)
            .map_err(|e| VaultError::InternalError(format!("序列化失败: {}", e)))?;

        manager.store_password(&serialized)
    }

    /// 存储公钥（Base64编码的字符串）
    pub fn store_public_key(&self, public_key: &str) -> VaultResult<()> {
        let manager =
            KeychainManager::new(&self.base_service, &format!("{}-public", self.contact))?;

        // 将公钥作为字符串存储
        let entry_data = KeychainEntryData {
            metadata: KeychainEntry {
                service: self.base_service.clone(),
                account: format!("{}-public", self.contact),
                created_at: current_timestamp(),
                last_accessed: current_timestamp(),
                key_type: KeyType::Custom("PublicKey".to_string()),
            },
            key: public_key.to_string(),
        };

        let serialized = serde_json::to_string(&entry_data)
            .map_err(|e| VaultError::InternalError(format!("序列化失败: {}", e)))?;

        manager.store_password(&serialized)
    }

    /// 获取主密钥
    pub fn get_master_key(&self) -> VaultResult<[u8; KEY_SIZE]> {
        let manager =
            KeychainManager::new(&self.base_service, &format!("{}-master", self.contact))?;
        manager.get_key()
    }

    /// 获取对称密钥
    pub fn get_symmetric_key(&self) -> VaultResult<[u8; KEY_SIZE]> {
        let manager =
            KeychainManager::new(&self.base_service, &format!("{}-symmetric", self.contact))?;
        manager.get_key()
    }

    /// 获取私钥
    pub fn get_private_key(&self) -> VaultResult<String> {
        let manager =
            KeychainManager::new(&self.base_service, &format!("{}-private", self.contact))?;

        let serialized = manager.get_password()?;
        let entry_data: KeychainEntryData = serde_json::from_str(&serialized)
            .map_err(|e| VaultError::InternalError(format!("反序列化失败: {}", e)))?;

        Ok(entry_data.key)
    }

    /// 获取公钥
    pub fn get_public_key(&self) -> VaultResult<String> {
        let manager =
            KeychainManager::new(&self.base_service, &format!("{}-public", self.contact))?;

        let serialized = manager.get_password()?;
        let entry_data: KeychainEntryData = serde_json::from_str(&serialized)
            .map_err(|e| VaultError::InternalError(format!("反序列化失败: {}", e)))?;

        Ok(entry_data.key)
    }

    /// 检查所有密钥是否存在
    pub fn all_keys_exist(&self) -> bool {
        let master_exists =
            KeychainManager::new(&self.base_service, &format!("{}-master", self.contact))
                .map(|m| m.key_exists())
                .unwrap_or(false);

        let symmetric_exists =
            KeychainManager::new(&self.base_service, &format!("{}-symmetric", self.contact))
                .map(|m| m.key_exists())
                .unwrap_or(false);

        let private_exists =
            KeychainManager::new(&self.base_service, &format!("{}-private", self.contact))
                .map(|m| m.key_exists())
                .unwrap_or(false);

        let public_exists =
            KeychainManager::new(&self.base_service, &format!("{}-public", self.contact))
                .map(|m| m.key_exists())
                .unwrap_or(false);

        master_exists && symmetric_exists && private_exists && public_exists
    }

    /// 删除所有密钥
    pub fn delete_all_keys(&self) -> VaultResult<()> {
        let accounts = vec![
            format!("{}-master", self.contact),
            format!("{}-symmetric", self.contact),
            format!("{}-private", self.contact),
            format!("{}-public", self.contact),
        ];

        for account in accounts {
            if let Ok(manager) = KeychainManager::new(&self.base_service, &account) {
                let _ = manager.delete_key(); // 忽略错误，继续删除其他密钥
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_keychain_manager_creation() {
        let manager = KeychainManager::new("test-service", "test-user").unwrap();
        assert_eq!(manager.service, "test-service");
        assert_eq!(manager.account, "test-user");

        // 测试无效输入
        assert!(KeychainManager::new("", "user").is_err());
        assert!(KeychainManager::new("service", "").is_err());
    }

    #[test]
    fn test_key_type() {
        let master_key = KeyType::MasterKey;
        assert_eq!(master_key.description(), "主密钥");

        let custom_key = KeyType::Custom("自定义密钥".to_string());
        assert_eq!(custom_key.description(), "自定义密钥");
    }

    #[test]
    fn test_keychain_entry_data() {
        let entry = KeychainEntry {
            service: "test".to_string(),
            account: "user".to_string(),
            created_at: current_timestamp(),
            last_accessed: current_timestamp(),
            key_type: KeyType::MasterKey,
        };

        let entry_data = KeychainEntryData {
            metadata: entry,
            key: "dGVzdGtleQ==".to_string(), // "testkey" in base64
        };

        let serialized = serde_json::to_string(&entry_data).unwrap();
        let deserialized: KeychainEntryData = serde_json::from_str(&serialized).unwrap();

        assert_eq!(deserialized.key, "dGVzdGtleQ==");
        assert_eq!(deserialized.metadata.service, "test");
    }

    // 移动平台特定测试
    #[cfg(any(target_os = "android", target_os = "ios"))]
    #[test]
    fn test_mobile_keychain_manager_creation() {
        let manager = KeychainManager::new("mobile-test-service", "mobile-test-user").unwrap();
        assert_eq!(manager.service, "mobile-test-service");
        assert_eq!(manager.account, "mobile-test-user");

        // 测试存储键名生成
        let storage_key = manager.generate_storage_key();
        assert_eq!(storage_key, "mobile-test-service:mobile-test-user");
    }

    #[cfg(any(target_os = "android", target_os = "ios"))]
    #[test]
    fn test_mobile_error_conversion() {
        use crate::mobile::MobileError;

        // 测试各种错误类型的转换
        let unsupported_error = MobileError::unsupported_platform("test feature");
        let keychain_error: KeychainError = unsupported_error.into();
        assert!(matches!(keychain_error, KeychainError::ServiceUnavailable));

        let storage_error = MobileError::secure_storage_error("test reason");
        let keychain_error: KeychainError = storage_error.into();
        assert!(matches!(keychain_error, KeychainError::SystemError(_)));

        let permission_error = MobileError::permission_error("test permission");
        let keychain_error: KeychainError = permission_error.into();
        assert!(matches!(keychain_error, KeychainError::AccessDenied));
    }

    #[cfg(any(target_os = "android", target_os = "ios"))]
    #[test]
    fn test_mobile_keychain_basic_operations() {
        // 测试基本的密钥链操作（不涉及实际存储）
        let manager = KeychainManager::new("test-mobile-service", "test-mobile-user").unwrap();

        // 测试密钥生成和序列化
        let test_key = [1u8; KEY_SIZE];
        let encoded_key = BASE64_STANDARD.encode(&test_key);

        let metadata = KeychainEntry {
            service: manager.service.clone(),
            account: manager.account.clone(),
            created_at: current_timestamp(),
            last_accessed: current_timestamp(),
            key_type: KeyType::MasterKey,
        };

        let entry_data = KeychainEntryData {
            metadata,
            key: encoded_key,
        };

        // 测试序列化和反序列化
        let serialized = serde_json::to_string(&entry_data).unwrap();
        let deserialized: KeychainEntryData = serde_json::from_str(&serialized).unwrap();

        assert_eq!(deserialized.metadata.service, "test-mobile-service");
        assert_eq!(deserialized.metadata.account, "test-mobile-user");
        assert!(matches!(deserialized.metadata.key_type, KeyType::MasterKey));

        // 测试密钥解码
        let decoded_key = BASE64_STANDARD.decode(&deserialized.key).unwrap();
        assert_eq!(decoded_key.len(), KEY_SIZE);
        assert_eq!(decoded_key, test_key);

        println!("✅ 移动平台密钥链基本操作测试通过");
    }

    // 注意：实际的密钥链测试需要在支持的平台上运行，并且可能需要用户交互
    // 这里只测试不需要实际密钥链访问的功能

    #[test]
    fn test_batch_keychain_manager() {
        let batch_manager = BatchKeychainManager::new("test-batch-service");
        assert_eq!(batch_manager.base_service, "test-batch-service");
    }

    #[test]
    fn test_registration_keychain_manager() {
        let reg_manager = RegistrationKeychainManager::new("<EMAIL>");
        assert_eq!(reg_manager.base_service, "secure-password");
        assert_eq!(reg_manager.contact, "<EMAIL>");
    }
}
