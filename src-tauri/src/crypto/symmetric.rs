// 对称加密模块
// 实现基于 AES-256-GCM 的安全对称加密/解密功能

use crate::errors::{VaultError, VaultResult};
use aes_gcm::{
    aead::{<PERSON><PERSON>, AeadCore, KeyInit, OsRng},
    Aes256Gcm, Key, Nonce,
};
use base64::{engine::general_purpose::STANDARD as BASE64_STANDARD, Engine as _};
use serde::{Deserialize, Serialize};
use zeroize::Zeroize;

use super::{KEY_SIZE, NONCE_SIZE};

/// 加密数据容器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptedData {
    /// Base64编码的密文
    pub ciphertext: String,
    /// Base64编码的nonce
    pub nonce: String,
    /// 加密算法标识符
    pub algorithm: String,
    /// 创建时间戳（可选，用于审计）
    pub timestamp: Option<i64>,
}

impl EncryptedData {
    /// 创建新的加密数据容器
    pub fn new(ciphertext: Vec<u8>, nonce: Vec<u8>) -> Self {
        Self {
            ciphertext: BASE64_STANDARD.encode(&ciphertext),
            nonce: BASE64_STANDARD.encode(&nonce),
            algorithm: "AES-256-GCM".to_string(),
            timestamp: Some(chrono::Utc::now().timestamp()),
        }
    }

    /// 从密文和nonce字节创建
    pub fn from_bytes(ciphertext: &[u8], nonce: &[u8]) -> Self {
        Self::new(ciphertext.to_vec(), nonce.to_vec())
    }

    /// 获取解码后的密文
    pub fn ciphertext_bytes(&self) -> VaultResult<Vec<u8>> {
        BASE64_STANDARD
            .decode(&self.ciphertext)
            .map_err(VaultError::Base64Decode)
    }

    /// 获取解码后的nonce
    pub fn nonce_bytes(&self) -> VaultResult<Vec<u8>> {
        BASE64_STANDARD
            .decode(&self.nonce)
            .map_err(VaultError::Base64Decode)
    }

    /// 转换为Base64编码的字符串（用于存储）
    pub fn to_base64(&self) -> String {
        let json = serde_json::to_string(self).unwrap_or_default();
        BASE64_STANDARD.encode(json.as_bytes())
    }

    /// 从Base64编码的字符串解析
    pub fn from_base64(encoded: &str) -> VaultResult<Self> {
        let json_bytes = BASE64_STANDARD
            .decode(encoded)
            .map_err(VaultError::Base64Decode)?;

        let json_str = String::from_utf8(json_bytes)
            .map_err(|e| VaultError::Decryption(format!("无效的UTF-8数据: {}", e)))?;

        serde_json::from_str(&json_str)
            .map_err(|e| VaultError::Decryption(format!("无效的加密数据格式: {}", e)))
    }

    /// 验证数据完整性
    pub fn validate(&self) -> VaultResult<()> {
        if self.algorithm != "AES-256-GCM" {
            return Err(VaultError::Decryption(format!(
                "不支持的加密算法: {}",
                self.algorithm
            )));
        }

        let ciphertext = self.ciphertext_bytes()?;
        let nonce = self.nonce_bytes()?;

        if ciphertext.is_empty() {
            return Err(VaultError::Decryption("密文不能为空".to_string()));
        }

        if nonce.len() != NONCE_SIZE {
            return Err(VaultError::Decryption(format!(
                "无效的nonce长度: 期望{}字节，实际{}字节",
                NONCE_SIZE,
                nonce.len()
            )));
        }

        Ok(())
    }
}

/// 安全的对称加密器
pub struct SymmetricCipher {
    cipher: Aes256Gcm,
}

impl SymmetricCipher {
    /// 从密钥创建加密器
    pub fn new(key: &[u8; KEY_SIZE]) -> Self {
        let key = Key::<Aes256Gcm>::from_slice(key);
        let cipher = Aes256Gcm::new(key);
        Self { cipher }
    }

    /// 加密数据
    pub fn encrypt(&self, plaintext: &[u8]) -> VaultResult<EncryptedData> {
        if plaintext.is_empty() {
            return Err(VaultError::InvalidInput("明文不能为空".to_string()));
        }

        // 生成随机nonce
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);

        // 执行加密
        let ciphertext = self
            .cipher
            .encrypt(&nonce, plaintext)
            .map_err(|e| VaultError::Encryption(format!("加密失败: {}", e)))?;

        Ok(EncryptedData::from_bytes(&ciphertext, &nonce))
    }

    /// 解密数据
    pub fn decrypt(&self, encrypted_data: &EncryptedData) -> VaultResult<Vec<u8>> {
        encrypted_data.validate()?;

        let ciphertext = encrypted_data.ciphertext_bytes()?;
        let nonce_bytes = encrypted_data.nonce_bytes()?;

        if nonce_bytes.len() != NONCE_SIZE {
            return Err(VaultError::Decryption("无效的nonce长度".to_string()));
        }

        let nonce = Nonce::from_slice(&nonce_bytes);

        let plaintext = self
            .cipher
            .decrypt(nonce, ciphertext.as_ref())
            .map_err(|e| VaultError::Decryption(format!("解密失败: {}", e)))?;

        Ok(plaintext)
    }

    /// 加密文本
    pub fn encrypt_text(&self, text: &str) -> VaultResult<EncryptedData> {
        self.encrypt(text.as_bytes())
    }

    /// 解密文本
    pub fn decrypt_text(&self, encrypted_data: &EncryptedData) -> VaultResult<String> {
        let plaintext = self.decrypt(encrypted_data)?;
        String::from_utf8(plaintext)
            .map_err(|e| VaultError::Decryption(format!("解密结果不是有效的UTF-8: {}", e)))
    }
}

/// 便利函数：使用给定密钥加密数据
pub fn encrypt_data(plaintext: &[u8], key: &[u8; KEY_SIZE]) -> VaultResult<EncryptedData> {
    let cipher = SymmetricCipher::new(key);
    cipher.encrypt(plaintext)
}

/// 便利函数：使用给定密钥解密数据
pub fn decrypt_data(encrypted_data: &EncryptedData, key: &[u8; KEY_SIZE]) -> VaultResult<Vec<u8>> {
    let cipher = SymmetricCipher::new(key);
    cipher.decrypt(encrypted_data)
}

/// 便利函数：加密文本
pub fn encrypt_text(text: &str, key: &[u8; KEY_SIZE]) -> VaultResult<EncryptedData> {
    let cipher = SymmetricCipher::new(key);
    cipher.encrypt_text(text)
}

/// 便利函数：解密文本
pub fn decrypt_text(encrypted_data: &EncryptedData, key: &[u8; KEY_SIZE]) -> VaultResult<String> {
    let cipher = SymmetricCipher::new(key);
    cipher.decrypt_text(encrypted_data)
}

/// 生成随机的对称加密密钥
///
/// # 返回
/// - 成功时返回32字节的随机密钥
/// - 失败时返回VaultError
pub fn generate_symmetric_key() -> VaultResult<[u8; KEY_SIZE]> {
    use rand::RngCore;

    let mut key = [0u8; KEY_SIZE];
    rand::rngs::OsRng.fill_bytes(&mut key);

    Ok(key)
}

/// 使用主密钥加密对称密钥
///
/// # 参数
/// - `symmetric_key`: 要加密的对称密钥
/// - `master_key`: 用于加密的主密钥
///
/// # 返回
/// - 成功时返回加密后的对称密钥（Base64编码）
/// - 失败时返回VaultError
pub fn encrypt_symmetric_key(
    symmetric_key: &[u8; KEY_SIZE],
    master_key: &[u8; KEY_SIZE],
) -> VaultResult<String> {
    let encrypted = encrypt_data(symmetric_key, master_key)?;
    Ok(encrypted.to_base64())
}

/// 使用主密钥解密对称密钥
///
/// # 参数
/// - `encrypted_symmetric_key`: 加密的对称密钥（Base64编码）
/// - `master_key`: 用于解密的主密钥
///
/// # 返回
/// - 成功时返回解密后的对称密钥
/// - 失败时返回VaultError
pub fn decrypt_symmetric_key(
    encrypted_symmetric_key: &str,
    master_key: &[u8; KEY_SIZE],
) -> VaultResult<[u8; KEY_SIZE]> {
    let encrypted_data = EncryptedData::from_base64(encrypted_symmetric_key)?;
    let decrypted_bytes = decrypt_data(&encrypted_data, master_key)?;

    if decrypted_bytes.len() != KEY_SIZE {
        return Err(VaultError::InvalidKeySize(format!(
            "Expected {} bytes, got {}",
            KEY_SIZE,
            decrypted_bytes.len()
        )));
    }

    let mut key = [0u8; KEY_SIZE];
    key.copy_from_slice(&decrypted_bytes);
    Ok(key)
}

/// 批量加密器，用于高效处理多个数据项
pub struct BatchCipher {
    cipher: SymmetricCipher,
}

impl BatchCipher {
    /// 创建批量加密器
    pub fn new(key: &[u8; KEY_SIZE]) -> Self {
        Self {
            cipher: SymmetricCipher::new(key),
        }
    }

    /// 批量加密多个文本
    pub fn encrypt_batch(&self, texts: &[&str]) -> VaultResult<Vec<EncryptedData>> {
        texts
            .iter()
            .map(|text| self.cipher.encrypt_text(text))
            .collect()
    }

    /// 批量解密多个数据
    pub fn decrypt_batch(&self, encrypted_data: &[&EncryptedData]) -> VaultResult<Vec<String>> {
        encrypted_data
            .iter()
            .map(|data| self.cipher.decrypt_text(data))
            .collect()
    }
}

/// 流式加密器，用于处理大文件
pub struct StreamCipher {
    cipher: SymmetricCipher,
    chunk_size: usize,
}

impl StreamCipher {
    /// 创建流式加密器
    pub fn new(key: &[u8; KEY_SIZE], chunk_size: Option<usize>) -> Self {
        Self {
            cipher: SymmetricCipher::new(key),
            chunk_size: chunk_size.unwrap_or(64 * 1024), // 默认64KB块
        }
    }

    /// 流式加密数据
    pub fn encrypt_stream(&self, data: &[u8]) -> VaultResult<Vec<EncryptedData>> {
        if data.is_empty() {
            return Ok(vec![]);
        }

        let mut encrypted_chunks = Vec::new();

        for chunk in data.chunks(self.chunk_size) {
            let encrypted = self.cipher.encrypt(chunk)?;
            encrypted_chunks.push(encrypted);
        }

        Ok(encrypted_chunks)
    }

    /// 流式解密数据
    pub fn decrypt_stream(&self, encrypted_chunks: &[EncryptedData]) -> VaultResult<Vec<u8>> {
        let mut decrypted_data = Vec::new();

        for chunk in encrypted_chunks {
            let mut decrypted_chunk = self.cipher.decrypt(chunk)?;
            decrypted_data.append(&mut decrypted_chunk);
        }

        Ok(decrypted_data)
    }
}

/// 内存安全的加密上下文，自动清理密钥
pub struct SecureCipherContext {
    key: [u8; KEY_SIZE],
}

impl SecureCipherContext {
    /// 创建安全的加密上下文
    pub fn new(key: [u8; KEY_SIZE]) -> Self {
        Self { key }
    }

    /// 获取加密器（临时借用）
    pub fn cipher(&self) -> SymmetricCipher {
        SymmetricCipher::new(&self.key)
    }

    /// 加密数据
    pub fn encrypt(&self, plaintext: &[u8]) -> VaultResult<EncryptedData> {
        self.cipher().encrypt(plaintext)
    }

    /// 解密数据
    pub fn decrypt(&self, encrypted_data: &EncryptedData) -> VaultResult<Vec<u8>> {
        self.cipher().decrypt(encrypted_data)
    }
}

impl Zeroize for SecureCipherContext {
    fn zeroize(&mut self) {
        self.key.zeroize();
    }
}

impl Drop for SecureCipherContext {
    fn drop(&mut self) {
        self.zeroize();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn test_key() -> [u8; KEY_SIZE] {
        [0x42; KEY_SIZE] // 简单的测试密钥
    }

    #[test]
    fn test_symmetric_encryption() {
        let key = test_key();
        let plaintext = "Hello, World!";

        let encrypted = encrypt_text(plaintext, &key).unwrap();
        let decrypted = decrypt_text(&encrypted, &key).unwrap();

        assert_eq!(plaintext, decrypted);
    }

    #[test]
    fn test_encrypted_data_serialization() {
        let key = test_key();
        let plaintext = "Test data for serialization";

        let encrypted = encrypt_text(plaintext, &key).unwrap();
        let serialized = encrypted.to_base64();
        let deserialized = EncryptedData::from_base64(&serialized).unwrap();

        let decrypted = decrypt_text(&deserialized, &key).unwrap();
        assert_eq!(plaintext, decrypted);
    }

    #[test]
    fn test_batch_encryption() {
        let key = test_key();
        let batch_cipher = BatchCipher::new(&key);

        let texts = vec!["Text 1", "Text 2", "Text 3"];
        let encrypted_batch = batch_cipher.encrypt_batch(&texts).unwrap();
        let decrypted_batch = batch_cipher
            .decrypt_batch(&encrypted_batch.iter().collect::<Vec<_>>())
            .unwrap();

        assert_eq!(texts, decrypted_batch);
    }

    #[test]
    fn test_stream_encryption() {
        let key = test_key();
        let stream_cipher = StreamCipher::new(&key, Some(10)); // 小块大小用于测试

        let data = b"This is a long piece of data that will be split into chunks";
        let encrypted_chunks = stream_cipher.encrypt_stream(data).unwrap();
        let decrypted_data = stream_cipher.decrypt_stream(&encrypted_chunks).unwrap();

        assert_eq!(data.to_vec(), decrypted_data);
    }

    #[test]
    fn test_secure_cipher_context() {
        let key = test_key();
        let context = SecureCipherContext::new(key);

        let plaintext = b"Secure context test";
        let encrypted = context.encrypt(plaintext).unwrap();
        let decrypted = context.decrypt(&encrypted).unwrap();

        assert_eq!(plaintext.to_vec(), decrypted);
    }

    #[test]
    fn test_encryption_uniqueness() {
        let key = test_key();
        let plaintext = "Same plaintext";

        let encrypted1 = encrypt_text(plaintext, &key).unwrap();
        let encrypted2 = encrypt_text(plaintext, &key).unwrap();

        // 由于使用随机nonce，即使明文相同，密文也应该不同
        assert_ne!(encrypted1.ciphertext, encrypted2.ciphertext);
        assert_ne!(encrypted1.nonce, encrypted2.nonce);

        // 但解密结果应该相同
        let decrypted1 = decrypt_text(&encrypted1, &key).unwrap();
        let decrypted2 = decrypt_text(&encrypted2, &key).unwrap();
        assert_eq!(decrypted1, decrypted2);
        assert_eq!(decrypted1, plaintext);
    }

    #[test]
    fn test_validation() {
        let mut encrypted = EncryptedData::new(vec![1, 2, 3], vec![4; NONCE_SIZE]);
        assert!(encrypted.validate().is_ok());

        // 修改算法
        encrypted.algorithm = "AES-128-GCM".to_string();
        assert!(encrypted.validate().is_err());

        // 恢复算法，修改nonce长度
        encrypted.algorithm = "AES-256-GCM".to_string();
        encrypted.nonce = BASE64_STANDARD.encode(&[4; 8]); // 错误的nonce长度
        assert!(encrypted.validate().is_err());
    }

    #[test]
    fn test_symmetric_key_generation() {
        let key1 = generate_symmetric_key().unwrap();
        let key2 = generate_symmetric_key().unwrap();

        // 验证密钥长度正确
        assert_eq!(key1.len(), KEY_SIZE);
        assert_eq!(key2.len(), KEY_SIZE);

        // 验证每次生成的密钥都不同
        assert_ne!(key1, key2);
    }

    #[test]
    fn test_symmetric_key_encryption() {
        let symmetric_key = generate_symmetric_key().unwrap();
        let master_key = generate_symmetric_key().unwrap(); // 使用另一个随机密钥作为主密钥

        let encrypted = encrypt_symmetric_key(&symmetric_key, &master_key).unwrap();

        // 验证加密结果是有效的Base64
        assert!(base64::engine::general_purpose::STANDARD
            .decode(&encrypted)
            .is_ok());

        // 验证加密结果不为空
        assert!(!encrypted.is_empty());
    }
}
