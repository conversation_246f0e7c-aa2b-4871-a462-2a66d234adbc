// 加密工具模块
// 提供各种加密相关的实用工具函数

use crate::errors::{VaultError, VaultResult};
use base64::Engine;

/// 生成随机密码
///
/// # 参数
/// - `length`: 密码长度
/// - `include_special`: 是否包含特殊字符
///
/// # 返回
/// - 生成的随机密码字符串
pub fn generate_random_password(length: usize, include_special: bool) -> String {
    use rand::{rngs::OsRng, Rng};

    let chars = if include_special {
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?"
    } else {
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    };

    let chars: Vec<char> = chars.chars().collect();
    let mut rng = OsRng;

    (0..length)
        .map(|_| chars[rng.gen_range(0..chars.len())])
        .collect()
}

/// Base64 编码函数
///
/// # 参数
/// - `data`: 要编码的字节数据
///
/// # 返回
/// - Base64编码的字符串
pub fn encode_base64(data: &[u8]) -> String {
    base64::engine::general_purpose::STANDARD.encode(data)
}

/// Base64 解码函数
///
/// # 参数
/// - `data`: 要解码的Base64字符串
///
/// # 返回
/// - 成功时返回解码后的字节数据
/// - 失败时返回VaultError
pub fn decode_base64(data: &str) -> VaultResult<Vec<u8>> {
    base64::engine::general_purpose::STANDARD
        .decode(data)
        .map_err(VaultError::Base64Decode)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_password_generation() {
        let password = generate_random_password(12, true);
        assert_eq!(password.len(), 12);

        let password_no_special = generate_random_password(8, false);
        assert_eq!(password_no_special.len(), 8);

        // 验证每次生成的密码都不同
        let password2 = generate_random_password(12, true);
        assert_ne!(password, password2);
    }

    #[test]
    fn test_base64_encoding() {
        let data = b"Hello, World!";
        let encoded = encode_base64(data);
        let decoded = decode_base64(&encoded).unwrap();

        assert_eq!(data.to_vec(), decoded);
    }

    #[test]
    fn test_base64_invalid_input() {
        let invalid_base64 = "invalid base64!@#";
        assert!(decode_base64(invalid_base64).is_err());
    }
}
