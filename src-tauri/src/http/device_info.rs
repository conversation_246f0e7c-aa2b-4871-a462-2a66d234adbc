#[cfg(not(any(target_os = "android", target_os = "ios")))]
use machine_uid;
use std::sync::OnceLock;

/// 设备信息管理器
/// 负责获取和管理设备相关信息，包括设备ID、设备类型和应用版本
pub struct DeviceInfoManager {
    device_id: String,
    device_type: String,
    app_version: String,
}

impl Default for DeviceInfoManager {
    fn default() -> Self {
        Self::new()
    }
}

impl DeviceInfoManager {
    /// 创建新的设备信息管理器实例
    pub fn new() -> Self {
        Self {
            device_id: Self::get_machine_uid(),
            device_type: Self::detect_device_type(),
            app_version: Self::detect_app_version(),
        }
    }

    /// 获取设备唯一标识
    /// 返回基于机器硬件的唯一标识符
    pub fn get_device_id(&self) -> &str {
        &self.device_id
    }

    /// 获取设备类型
    /// 根据当前运行平台返回对应的设备类型
    pub fn get_device_type(&self) -> &str {
        &self.device_type
    }

    /// 获取应用版本
    /// 从编译时环境变量获取应用版本信息
    pub fn get_app_version(&self) -> &str {
        &self.app_version
    }

    /// 获取机器唯一标识
    /// 使用 machine-uid 库获取基于硬件的唯一标识符（仅桌面平台）
    fn get_machine_uid() -> String {
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            match machine_uid::get() {
                Ok(uid) => {
                    log::info!("成功获取机器唯一标识: {}", uid);
                    uid
                }
                Err(e) => {
                    log::warn!("获取机器唯一标识失败: {}, 使用备用方案", e);
                    // 备用方案：使用 UUID v4
                    use uuid::Uuid;
                    let fallback_id = Uuid::new_v4().to_string();
                    log::info!("使用备用设备ID: {}", fallback_id);
                    fallback_id
                }
            }
        }

        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            // 移动端平台使用 UUID v4 作为设备标识
            use uuid::Uuid;
            let mobile_id = Uuid::new_v4().to_string();
            log::info!("移动端设备ID: {}", mobile_id);
            mobile_id
        }
    }

    /// 检测当前平台的设备类型
    /// 使用 tauri-plugin-os 提供的平台检测功能
    fn detect_device_type() -> String {
        // 使用编译时条件检测平台类型
        // 这与 tauri-plugin-os 的检测逻辑保持一致
        #[cfg(target_os = "windows")]
        return "windows".to_string();

        #[cfg(target_os = "macos")]
        return "macos".to_string();

        #[cfg(target_os = "linux")]
        return "linux".to_string();

        #[cfg(target_os = "android")]
        return "android".to_string();

        #[cfg(target_os = "ios")]
        return "ios".to_string();

        #[cfg(target_arch = "wasm32")]
        return "web".to_string();

        // 默认情况
        #[cfg(not(any(
            target_os = "windows",
            target_os = "macos",
            target_os = "linux",
            target_os = "android",
            target_os = "ios",
            target_arch = "wasm32"
        )))]
        return "unknown".to_string();
    }

    /// 检测应用版本
    /// 从编译时环境变量 CARGO_PKG_VERSION 获取版本信息
    fn detect_app_version() -> String {
        env!("CARGO_PKG_VERSION").to_string()
    }
}

/// 全局设备信息管理器实例
/// 使用 OnceLock 确保线程安全的单例模式
static GLOBAL_DEVICE_INFO: OnceLock<DeviceInfoManager> = OnceLock::new();

/// 获取全局设备信息管理器实例
///
/// 返回全局唯一的设备信息管理器实例，确保整个应用中设备信息的一致性
pub fn get_global_device_info() -> &'static DeviceInfoManager {
    GLOBAL_DEVICE_INFO.get_or_init(|| {
        log::info!("初始化全局设备信息管理器");
        DeviceInfoManager::new()
    })
}

/// 异步获取设备信息（使用 tauri-plugin-os）
///
/// 这个函数提供了使用 tauri-plugin-os 的异步接口来获取设备信息的能力
/// 主要用于需要更详细平台信息的场景
#[cfg(feature = "tauri-os")]
pub async fn get_device_info_async() -> Result<DeviceInfo, String> {
    use tauri_plugin_os::{arch, family, platform};

    let device_info = DeviceInfo {
        platform: platform().to_string(),
        os_type: DeviceInfoManager::detect_device_type(), // 使用我们自己的检测方法
        version: "unknown".to_string(),                   // 简化版本信息
        arch: arch().to_string(),
        family: family().to_string(),
    };

    Ok(device_info)
}

/// 详细设备信息结构
/// 包含通过 tauri-plugin-os 获取的详细平台信息
#[cfg(feature = "tauri-os")]
#[derive(Debug, Clone)]
pub struct DeviceInfo {
    pub platform: String,
    pub os_type: String,
    pub version: String,
    pub arch: String,
    pub family: String,
}

/// HTTP 头部常量定义
pub mod headers {
    /// 设备唯一标识头部
    pub const DEVICE_ID: &str = "X-Device-ID";

    /// 设备类型头部
    pub const DEVICE_TYPE: &str = "X-Device-Type";

    /// 应用版本头部
    pub const APP_VERSION: &str = "X-App-Version";
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_device_info_manager_creation() {
        let device_info = DeviceInfoManager::new();

        // 验证设备ID不为空
        assert!(!device_info.get_device_id().is_empty());

        // 验证设备类型不为空
        assert!(!device_info.get_device_type().is_empty());

        // 验证应用版本不为空
        assert!(!device_info.get_app_version().is_empty());
        assert_eq!(device_info.get_app_version(), "0.1.0");

        println!("设备ID: {}", device_info.get_device_id());
        println!("设备类型: {}", device_info.get_device_type());
        println!("应用版本: {}", device_info.get_app_version());
    }

    #[test]
    fn test_global_device_info() {
        let device_info1 = get_global_device_info();
        let device_info2 = get_global_device_info();

        // 验证全局实例的一致性
        assert_eq!(device_info1.get_device_id(), device_info2.get_device_id());
        assert_eq!(
            device_info1.get_device_type(),
            device_info2.get_device_type()
        );
        assert_eq!(
            device_info1.get_app_version(),
            device_info2.get_app_version()
        );
    }

    #[test]
    fn test_device_type_detection() {
        let device_type = DeviceInfoManager::detect_device_type();

        // 验证设备类型是预期的值之一
        let valid_types = [
            "windows", "macos", "linux", "android", "ios", "web", "unknown",
        ];
        assert!(valid_types.contains(&device_type.as_str()));

        // 在 macOS 上运行时应该返回 "macos"
        #[cfg(target_os = "macos")]
        assert_eq!(device_type, "macos");
    }

    #[test]
    fn test_machine_uid_generation() {
        let uid1 = DeviceInfoManager::get_machine_uid();
        let uid2 = DeviceInfoManager::get_machine_uid();

        // 验证机器UID不为空
        assert!(!uid1.is_empty());
        assert!(!uid2.is_empty());

        // 在同一台机器上，机器UID应该是相同的
        assert_eq!(uid1, uid2);

        println!("机器唯一标识: {}", uid1);
    }

    #[test]
    fn test_headers_constants() {
        assert_eq!(headers::DEVICE_ID, "X-Device-ID");
        assert_eq!(headers::DEVICE_TYPE, "X-Device-Type");
        assert_eq!(headers::APP_VERSION, "X-App-Version");
    }

    #[test]
    fn test_device_info_persistence() {
        // 测试设备信息在多次创建时的一致性
        let manager1 = DeviceInfoManager::new();
        let manager2 = DeviceInfoManager::new();

        // 设备ID应该在同一台机器上保持一致
        assert_eq!(manager1.get_device_id(), manager2.get_device_id());

        // 设备类型应该一致
        assert_eq!(manager1.get_device_type(), manager2.get_device_type());

        // 应用版本应该一致
        assert_eq!(manager1.get_app_version(), manager2.get_app_version());
    }
}
