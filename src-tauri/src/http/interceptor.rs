use crate::http::types::{HttpError, HttpRequest, HttpResponse};
use async_trait::async_trait;
use std::sync::Arc;

// 添加设备信息相关导入
use crate::http::device_info::{get_global_device_info, headers};

/// 请求拦截器 trait
#[async_trait]
pub trait RequestInterceptor: Send + Sync {
    /// 处理请求
    async fn on_request(&self, request: HttpRequest) -> Result<HttpRequest, HttpError>;
}

/// 响应拦截器 trait  
#[async_trait]
pub trait ResponseInterceptor: Send + Sync {
    /// 处理响应
    async fn on_response(
        &self,
        response: HttpResponse<serde_json::Value>,
    ) -> Result<HttpResponse<serde_json::Value>, HttpError>;
}

/// 设备信息拦截器
/// 为所有HTTP请求自动添加设备相关的头部信息
pub struct DeviceInfoInterceptor;

impl Default for DeviceInfoInterceptor {
    fn default() -> Self {
        Self::new()
    }
}

impl DeviceInfoInterceptor {
    /// 创建新的设备信息拦截器
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl RequestInterceptor for DeviceInfoInterceptor {
    async fn on_request(&self, mut request: HttpRequest) -> Result<HttpRequest, HttpError> {
        let device_info = get_global_device_info();

        // 添加设备ID头部
        request.config.headers.insert(
            headers::DEVICE_ID.to_string(),
            device_info.get_device_id().to_string(),
        );

        // 添加设备类型头部
        request.config.headers.insert(
            headers::DEVICE_TYPE.to_string(),
            device_info.get_device_type().to_string(),
        );

        // 添加应用版本头部
        request.config.headers.insert(
            headers::APP_VERSION.to_string(),
            device_info.get_app_version().to_string(),
        );

        log::debug!(
            "添加设备信息头部 - ID: {}, Type: {}, Version: {}",
            device_info.get_device_id(),
            device_info.get_device_type(),
            device_info.get_app_version()
        );

        Ok(request)
    }
}

/// 认证拦截器
#[derive(Clone)]
pub struct AuthInterceptor {
    token_store: Arc<tokio::sync::RwLock<Option<String>>>,
}

impl Default for AuthInterceptor {
    fn default() -> Self {
        Self::new()
    }
}

impl AuthInterceptor {
    /// 创建新的认证拦截器
    pub fn new() -> Self {
        Self {
            token_store: Arc::new(tokio::sync::RwLock::new(None)),
        }
    }

    /// 设置访问令牌
    pub async fn set_token(&self, token: Option<String>) {
        let mut token_store = self.token_store.write().await;
        *token_store = token;
    }

    /// 获取访问令牌
    pub async fn get_token(&self) -> Option<String> {
        let token_store = self.token_store.read().await;
        token_store.clone()
    }
}

#[async_trait]
impl RequestInterceptor for AuthInterceptor {
    async fn on_request(&self, mut request: HttpRequest) -> Result<HttpRequest, HttpError> {
        // 添加认证 header
        if let Some(token) = self.get_token().await {
            request
                .config
                .headers
                .insert("Authorization".to_string(), format!("Bearer {}", token));
        }

        Ok(request)
    }
}

/// 日志拦截器
pub struct LoggingInterceptor;

impl Default for LoggingInterceptor {
    fn default() -> Self {
        Self::new()
    }
}

impl LoggingInterceptor {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl RequestInterceptor for LoggingInterceptor {
    async fn on_request(&self, request: HttpRequest) -> Result<HttpRequest, HttpError> {
        log::info!("HTTP Request: {} {}", request.method, request.url);
        log::debug!("Request headers: {:?}", request.config.headers);
        Ok(request)
    }
}

#[async_trait]
impl ResponseInterceptor for LoggingInterceptor {
    async fn on_response(
        &self,
        response: HttpResponse<serde_json::Value>,
    ) -> Result<HttpResponse<serde_json::Value>, HttpError> {
        log::info!("HTTP Response: Status {}", response.status);
        log::debug!("Response headers: {:?}", response.headers);
        Ok(response)
    }
}

/// 组合拦截器
pub struct CompositeInterceptor {
    request_interceptors: Vec<Arc<dyn RequestInterceptor>>,
    response_interceptors: Vec<Arc<dyn ResponseInterceptor>>,
}

impl Default for CompositeInterceptor {
    fn default() -> Self {
        Self::new()
    }
}

impl CompositeInterceptor {
    /// 创建新的组合拦截器
    pub fn new() -> Self {
        Self {
            request_interceptors: Vec::new(),
            response_interceptors: Vec::new(),
        }
    }

    /// 添加请求拦截器
    pub fn add_request_interceptor<I: RequestInterceptor + 'static>(&mut self, interceptor: I) {
        self.request_interceptors.push(Arc::new(interceptor));
    }

    /// 添加响应拦截器
    pub fn add_response_interceptor<I: ResponseInterceptor + 'static>(&mut self, interceptor: I) {
        self.response_interceptors.push(Arc::new(interceptor));
    }

    /// 处理请求
    pub async fn on_request(&self, mut request: HttpRequest) -> Result<HttpRequest, HttpError> {
        for interceptor in &self.request_interceptors {
            request = interceptor.on_request(request).await?;
        }
        Ok(request)
    }

    /// 处理响应
    pub async fn on_response(
        &self,
        mut response: HttpResponse<serde_json::Value>,
    ) -> Result<HttpResponse<serde_json::Value>, HttpError> {
        for interceptor in &self.response_interceptors {
            response = interceptor.on_response(response).await?;
        }
        Ok(response)
    }
}
