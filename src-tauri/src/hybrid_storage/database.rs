use crate::errors::{VaultError, VaultResult};
use crate::hybrid_storage::models::{DatabaseError, Item, Login, LoginEntry, QueryResult, Vault};
use chrono::{DateTime, Utc};
use rusqlite::Connection;
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};

/// 数据库管理器
/// 负责初始化数据库、执行迁移和管理连接
pub struct DatabaseManager {
    connection: Arc<Mutex<Connection>>,
}

impl DatabaseManager {
    /// 创建新的数据库管理器实例
    pub async fn new(app_handle: &AppHandle) -> VaultResult<Self> {
        let db_path = Self::get_db_path(app_handle)?;
        log::info!("Initializing database at: {:?}", db_path);

        // 确保数据库目录存在
        if let Some(parent) = db_path.parent() {
            std::fs::create_dir_all(parent).map_err(|e| {
                VaultError::InternalError(format!("Failed to create database directory: {}", e))
            })?;
        }

        // 创建数据库连接
        let connection = Connection::open(&db_path)
            .map_err(|e| VaultError::InternalError(format!("Failed to open database: {}", e)))?;

        let manager = Self {
            connection: Arc::new(Mutex::new(connection)),
        };

        // 初始化数据库架构
        manager.initialize_schema().await?;

        log::info!("Database initialized successfully");
        Ok(manager)
    }

    /// 获取数据库文件路径
    fn get_db_path(app_handle: &AppHandle) -> VaultResult<PathBuf> {
        let path = app_handle
            .path()
            .app_local_data_dir()
            .map_err(|_| VaultError::InternalError("Failed to get app local data dir".to_string()))?
            .join("vault_v2.sqlite");

        Ok(path)
    }

    /// 初始化数据库架构
    /// 根据文档5.1的数据库设计创建表结构
    async fn initialize_schema(&self) -> VaultResult<()> {
        let conn = self.connection.lock().map_err(|e| {
            VaultError::InternalError(format!("Failed to acquire database lock: {}", e))
        })?;

        // 启用外键约束
        conn.execute("PRAGMA foreign_keys = ON", []).map_err(|e| {
            VaultError::InternalError(format!("Failed to enable foreign keys: {}", e))
        })?;

        // 创建密码库表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS vaults (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                last_synced_at TEXT
            )",
            [],
        )
        .map_err(|e| VaultError::InternalError(format!("Failed to create vaults table: {}", e)))?;

        // 创建密码项表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS items (
                id TEXT PRIMARY KEY,
                vault_id TEXT NOT NULL,
                type TEXT NOT NULL, -- 'login', 'card', 'note', etc.
                name TEXT NOT NULL,
                favorite INTEGER DEFAULT 0,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                FOREIGN KEY (vault_id) REFERENCES vaults(id) ON DELETE CASCADE
            )",
            [],
        )
        .map_err(|e| VaultError::InternalError(format!("Failed to create items table: {}", e)))?;

        // 创建登录信息表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS logins (
                item_id TEXT PRIMARY KEY,
                username TEXT,
                encrypted_password TEXT NOT NULL,
                website TEXT,
                notes TEXT,
                FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
            )",
            [],
        )
        .map_err(|e| VaultError::InternalError(format!("Failed to create logins table: {}", e)))?;

        // 创建同步状态表
        conn.execute(
            "CREATE TABLE IF NOT EXISTS sync_status (
                id TEXT PRIMARY KEY,
                last_sync_time TEXT,
                last_sync_status TEXT,
                sync_token TEXT
            )",
            [],
        )
        .map_err(|e| {
            VaultError::InternalError(format!("Failed to create sync_status table: {}", e))
        })?;

        // 创建索引以提高查询性能
        self.create_indexes(&conn)?;

        // 创建默认密码库（如果不存在）
        self.create_default_vault(&conn)?;

        log::info!("Database schema initialized successfully");
        Ok(())
    }

    /// 创建数据库索引
    fn create_indexes(&self, conn: &Connection) -> VaultResult<()> {
        let indexes = vec![
            "CREATE INDEX IF NOT EXISTS idx_items_vault_id ON items(vault_id)",
            "CREATE INDEX IF NOT EXISTS idx_items_type ON items(type)",
            "CREATE INDEX IF NOT EXISTS idx_items_name ON items(name)",
            "CREATE INDEX IF NOT EXISTS idx_items_favorite ON items(favorite)",
            "CREATE INDEX IF NOT EXISTS idx_logins_website ON logins(website)",
            "CREATE INDEX IF NOT EXISTS idx_vaults_name ON vaults(name)",
        ];

        for index_sql in indexes {
            conn.execute(index_sql, [])
                .map_err(|e| VaultError::InternalError(format!("Failed to create index: {}", e)))?;
        }

        Ok(())
    }

    /// 创建默认密码库
    fn create_default_vault(&self, conn: &Connection) -> VaultResult<()> {
        // 检查是否已有密码库
        let mut stmt = conn.prepare("SELECT COUNT(*) FROM vaults").map_err(|e| {
            VaultError::InternalError(format!("Failed to prepare count query: {}", e))
        })?;

        let count: i64 = stmt.query_row([], |row| row.get(0)).map_err(|e| {
            VaultError::InternalError(format!("Failed to execute count query: {}", e))
        })?;

        if count == 0 {
            let default_vault = Vault::new(
                "My Vault".to_string(),
                Some("Default password vault".to_string()),
            );

            conn.execute(
                "INSERT INTO vaults (id, name, description, created_at, updated_at, last_synced_at) 
                 VALUES (?1, ?2, ?3, ?4, ?5, ?6)",
                rusqlite::params![
                    default_vault.id,
                    default_vault.name,
                    default_vault.description,
                    default_vault.created_at.to_rfc3339(),
                    default_vault.updated_at.to_rfc3339(),
                    default_vault.last_synced_at.map(|dt| dt.to_rfc3339())
                ],
            ).map_err(|e| VaultError::InternalError(format!("Failed to create default vault: {}", e)))?;

            log::info!("Created default vault: {}", default_vault.id);
        }

        Ok(())
    }

    /// 获取所有密码库
    pub async fn get_vaults(&self) -> Result<QueryResult<Vault>, DatabaseError> {
        let conn = self
            .connection
            .lock()
            .map_err(|e| DatabaseError::Connection(format!("Failed to acquire lock: {}", e)))?;

        let mut stmt = conn.prepare(
            "SELECT id, name, description, created_at, updated_at, last_synced_at FROM vaults ORDER BY name"
        )?;

        let vault_iter = stmt.query_map([], |row| {
            Ok(Vault {
                id: row.get(0)?,
                name: row.get(1)?,
                description: row.get(2)?,
                created_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(3)?)
                    .map_err(|_| {
                        rusqlite::Error::InvalidColumnType(
                            3,
                            "created_at".to_string(),
                            rusqlite::types::Type::Text,
                        )
                    })?
                    .with_timezone(&Utc),
                updated_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(4)?)
                    .map_err(|_| {
                        rusqlite::Error::InvalidColumnType(
                            4,
                            "updated_at".to_string(),
                            rusqlite::types::Type::Text,
                        )
                    })?
                    .with_timezone(&Utc),
                last_synced_at: row
                    .get::<_, Option<String>>(5)?
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
            })
        })?;

        let mut vaults = Vec::new();
        for vault in vault_iter {
            vaults.push(vault?);
        }

        Ok(QueryResult::new(vaults))
    }

    /// 根据ID获取密码库
    pub async fn get_vault_by_id(&self, vault_id: &str) -> Result<Option<Vault>, DatabaseError> {
        let conn = self
            .connection
            .lock()
            .map_err(|e| DatabaseError::Connection(format!("Failed to acquire lock: {}", e)))?;

        let mut stmt = conn.prepare(
            "SELECT id, name, description, created_at, updated_at, last_synced_at FROM vaults WHERE id = ?1"
        )?;

        let vault = stmt.query_row([vault_id], |row| {
            Ok(Vault {
                id: row.get(0)?,
                name: row.get(1)?,
                description: row.get(2)?,
                created_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(3)?)
                    .map_err(|_| {
                        rusqlite::Error::InvalidColumnType(
                            3,
                            "created_at".to_string(),
                            rusqlite::types::Type::Text,
                        )
                    })?
                    .with_timezone(&Utc),
                updated_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(4)?)
                    .map_err(|_| {
                        rusqlite::Error::InvalidColumnType(
                            4,
                            "updated_at".to_string(),
                            rusqlite::types::Type::Text,
                        )
                    })?
                    .with_timezone(&Utc),
                last_synced_at: row
                    .get::<_, Option<String>>(5)?
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
            })
        });

        match vault {
            Ok(vault) => Ok(Some(vault)),
            Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
            Err(e) => Err(e.into()),
        }
    }

    /// 获取默认密码库ID
    pub async fn get_default_vault_id(&self) -> Result<String, DatabaseError> {
        let vaults = self.get_vaults().await?;
        if let Some(vault) = vaults.data.first() {
            Ok(vault.id.clone())
        } else {
            Err(DatabaseError::NotFound("No vaults found".to_string()))
        }
    }

    /// 获取指定密码库的所有登录条目
    pub async fn get_login_entries(
        &self,
        vault_id: &str,
    ) -> Result<QueryResult<LoginEntry>, DatabaseError> {
        let conn = self
            .connection
            .lock()
            .map_err(|e| DatabaseError::Connection(format!("Failed to acquire lock: {}", e)))?;

        let mut stmt = conn.prepare(
            "SELECT i.id, i.vault_id, i.type, i.name, i.favorite, i.created_at, i.updated_at,
                    l.username, l.encrypted_password, l.website, l.notes
             FROM items i
             INNER JOIN logins l ON i.id = l.item_id
             WHERE i.vault_id = ?1 AND i.type = 'login'
             ORDER BY i.name",
        )?;

        let login_iter = stmt.query_map([vault_id], |row| {
            let item = Item {
                id: row.get(0)?,
                vault_id: row.get(1)?,
                item_type: crate::hybrid_storage::models::ItemType::Login,
                name: row.get(3)?,
                favorite: row.get::<_, i32>(4)? != 0,
                created_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(5)?)
                    .map_err(|_| {
                        rusqlite::Error::InvalidColumnType(
                            5,
                            "created_at".to_string(),
                            rusqlite::types::Type::Text,
                        )
                    })?
                    .with_timezone(&Utc),
                updated_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(6)?)
                    .map_err(|_| {
                        rusqlite::Error::InvalidColumnType(
                            6,
                            "updated_at".to_string(),
                            rusqlite::types::Type::Text,
                        )
                    })?
                    .with_timezone(&Utc),
            };

            let login = Login {
                item_id: item.id.clone(),
                username: row.get(7)?,
                encrypted_password: row.get(8)?,
                website: row.get(9)?,
                notes: row.get(10)?,
            };

            Ok(LoginEntry { item, login })
        })?;

        let mut entries = Vec::new();
        for entry in login_iter {
            entries.push(entry?);
        }

        Ok(QueryResult::new(entries))
    }

    /// 添加登录条目
    pub async fn add_login_entry(&self, entry: &LoginEntry) -> Result<(), DatabaseError> {
        let conn = self
            .connection
            .lock()
            .map_err(|e| DatabaseError::Connection(format!("Failed to acquire lock: {}", e)))?;

        let tx = conn.unchecked_transaction()?;

        // 插入item记录
        tx.execute(
            "INSERT INTO items (id, vault_id, type, name, favorite, created_at, updated_at)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)",
            rusqlite::params![
                entry.item.id,
                entry.item.vault_id,
                entry.item.item_type.to_string(),
                entry.item.name,
                if entry.item.favorite { 1 } else { 0 },
                entry.item.created_at.to_rfc3339(),
                entry.item.updated_at.to_rfc3339()
            ],
        )?;

        // 插入login记录
        tx.execute(
            "INSERT INTO logins (item_id, username, encrypted_password, website, notes)
             VALUES (?1, ?2, ?3, ?4, ?5)",
            rusqlite::params![
                entry.login.item_id,
                entry.login.username,
                entry.login.encrypted_password,
                entry.login.website,
                entry.login.notes
            ],
        )?;

        tx.commit()?;
        Ok(())
    }

    /// 根据域名获取登录条目
    pub async fn get_login_entries_by_domain(
        &self,
        domain: &str,
    ) -> Result<QueryResult<LoginEntry>, DatabaseError> {
        let conn = self
            .connection
            .lock()
            .map_err(|e| DatabaseError::Connection(format!("Failed to acquire lock: {}", e)))?;

        let mut stmt = conn.prepare(
            "SELECT i.id, i.vault_id, i.type, i.name, i.favorite, i.created_at, i.updated_at,
                    l.username, l.encrypted_password, l.website, l.notes
             FROM items i
             INNER JOIN logins l ON i.id = l.item_id
             WHERE i.type = 'login' AND l.website LIKE ?1
             ORDER BY i.name",
        )?;

        let domain_pattern = format!("%{}%", domain);
        let login_iter = stmt.query_map([domain_pattern], |row| {
            let item = Item {
                id: row.get(0)?,
                vault_id: row.get(1)?,
                item_type: crate::hybrid_storage::models::ItemType::Login,
                name: row.get(3)?,
                favorite: row.get::<_, i32>(4)? != 0,
                created_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(5)?)
                    .map_err(|_| {
                        rusqlite::Error::InvalidColumnType(
                            5,
                            "created_at".to_string(),
                            rusqlite::types::Type::Text,
                        )
                    })?
                    .with_timezone(&Utc),
                updated_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(6)?)
                    .map_err(|_| {
                        rusqlite::Error::InvalidColumnType(
                            6,
                            "updated_at".to_string(),
                            rusqlite::types::Type::Text,
                        )
                    })?
                    .with_timezone(&Utc),
            };

            let login = Login {
                item_id: item.id.clone(),
                username: row.get(7)?,
                encrypted_password: row.get(8)?,
                website: row.get(9)?,
                notes: row.get(10)?,
            };

            Ok(LoginEntry { item, login })
        })?;

        let mut entries = Vec::new();
        for entry in login_iter {
            entries.push(entry?);
        }

        Ok(QueryResult::new(entries))
    }

    /// 关闭数据库连接
    pub async fn close(self) -> VaultResult<()> {
        // rusqlite的Connection会在Drop时自动关闭
        log::info!("Database connection closed");
        Ok(())
    }
}
