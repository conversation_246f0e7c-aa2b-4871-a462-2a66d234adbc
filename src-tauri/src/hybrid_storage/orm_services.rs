use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;

use super::entities::vault;
use super::orm_database::{OrmDatabaseError, OrmDatabaseManager};

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginCredential {
    pub id: i32,
    pub name: String,
    pub username: Option<String>,
    pub password: String, // 这里是解密后的密码
    pub website: Option<String>,
    pub notes: Option<String>,
    pub favorite: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct VaultInfo {
    pub id: i32,
    pub name: String,
    pub description: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub last_synced_at: Option<chrono::DateTime<chrono::Utc>>,
}

impl From<vault::Model> for VaultInfo {
    fn from(vault: vault::Model) -> Self {
        Self {
            id: vault.id,
            name: vault.name,
            description: vault.description,
            created_at: chrono::DateTime::from_naive_utc_and_offset(vault.created_at, chrono::Utc),
            updated_at: chrono::DateTime::from_naive_utc_and_offset(vault.updated_at, chrono::Utc),
            last_synced_at: vault
                .last_synced_at
                .map(|dt| chrono::DateTime::from_naive_utc_and_offset(dt, chrono::Utc)),
        }
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ServiceError {
    #[error("Database error: {0}")]
    Database(#[from] OrmDatabaseError),
    #[error("Encryption error: {0}")]
    Encryption(String),
    #[error("Decryption error: {0}")]
    Decryption(String),
    #[error("Invalid input: {0}")]
    InvalidInput(String),
}

pub type ServiceResult<T> = std::result::Result<T, ServiceError>;

pub struct OrmPasswordService {
    db: Arc<Mutex<OrmDatabaseManager>>,
}

impl OrmPasswordService {
    pub fn new(db: OrmDatabaseManager) -> Self {
        Self {
            db: Arc::new(Mutex::new(db)),
        }
    }

    // Vault 操作
    pub async fn get_all_vaults(&self) -> ServiceResult<Vec<VaultInfo>> {
        let db = self.db.lock().await;
        let vaults = db.get_all_vaults().await?;
        Ok(vaults.into_iter().map(VaultInfo::from).collect())
    }

    pub async fn create_vault(
        &self,
        name: String,
        description: Option<String>,
    ) -> ServiceResult<VaultInfo> {
        if name.trim().is_empty() {
            return Err(ServiceError::InvalidInput(
                "Vault name cannot be empty".to_string(),
            ));
        }

        let db = self.db.lock().await;
        let vault = db.create_vault(name, description).await?;
        Ok(VaultInfo::from(vault))
    }

    pub async fn update_vault(
        &self,
        id: i32,
        name: Option<String>,
        description: Option<String>,
    ) -> ServiceResult<VaultInfo> {
        if let Some(ref name) = name {
            if name.trim().is_empty() {
                return Err(ServiceError::InvalidInput(
                    "Vault name cannot be empty".to_string(),
                ));
            }
        }

        let db = self.db.lock().await;
        let vault = db.update_vault(id, name, description).await?;
        Ok(VaultInfo::from(vault))
    }

    pub async fn delete_vault(&self, id: i32) -> ServiceResult<()> {
        let db = self.db.lock().await;
        db.delete_vault(id).await?;
        Ok(())
    }

    // 登录凭据操作
    /// 使用外部对称密钥保存登录凭据
    pub async fn save_login_credential_with_key(
        &self,
        vault_id: i32,
        name: String,
        username: Option<String>,
        password: String,
        website: Option<String>,
        notes: Option<String>,
        favorite: bool,
        symmetric_key: &[u8; crate::crypto::KEY_SIZE],
    ) -> ServiceResult<LoginCredential> {
        use crate::crypto::symmetric::SymmetricCipher;

        if name.trim().is_empty() {
            return Err(ServiceError::InvalidInput(
                "Name cannot be empty".to_string(),
            ));
        }

        if password.trim().is_empty() {
            return Err(ServiceError::InvalidInput(
                "Password cannot be empty".to_string(),
            ));
        }

        // 使用外部对称密钥加密密码
        let cipher = SymmetricCipher::new(symmetric_key);
        let encrypted_data = cipher
            .encrypt_text(&password)
            .map_err(|e| ServiceError::Encryption(e.to_string()))?;

        // 序列化为字符串存储
        let encrypted_password = encrypted_data.to_base64();

        // 加密备注（如果有的话）
        let encrypted_notes = if let Some(ref notes_text) = notes {
            if !notes_text.is_empty() {
                let encrypted_notes_data = cipher
                    .encrypt_text(notes_text)
                    .map_err(|e| ServiceError::Encryption(e.to_string()))?;
                Some(encrypted_notes_data.to_base64())
            } else {
                Some(notes_text.clone())
            }
        } else {
            None
        };

        let db = self.db.lock().await;

        // 创建条目
        let item = db
            .create_item(vault_id, "login".to_string(), name, favorite)
            .await?;

        // 创建登录信息
        let login = db
            .create_login(
                item.id,
                username,
                encrypted_password,
                website,
                encrypted_notes,
            )
            .await?;

        Ok(LoginCredential {
            id: item.id,
            name: item.name,
            username: login.username,
            password, // 返回解密后的密码
            website: login.website,
            notes, // 返回解密后的备注
            favorite: item.favorite,
            created_at: chrono::DateTime::from_naive_utc_and_offset(item.created_at, chrono::Utc),
            updated_at: chrono::DateTime::from_naive_utc_and_offset(item.updated_at, chrono::Utc),
        })
    }

    /// 删除登录凭据（硬删除）
    pub async fn delete_login_credential(&self, id: i32) -> ServiceResult<()> {
        let db = self.db.lock().await;
        db.delete_item(id).await?;
        Ok(())
    }

    /// 软删除登录凭据（放入回收站）
    pub async fn soft_delete_login_credential(&self, id: i32) -> ServiceResult<LoginCredential> {
        let db = self.db.lock().await;
        let updated_item = db.soft_delete_item(id).await?;

        // 获取关联的登录信息
        if let Some(login) = db.get_login_by_item_id(id).await? {
            Ok(LoginCredential {
                id: updated_item.id,
                name: updated_item.name,
                username: login.username,
                password: "***DELETED***".to_string(), // 不返回已删除项目的密码
                website: login.website,
                notes: login.notes,
                favorite: updated_item.favorite,
                created_at: chrono::DateTime::from_naive_utc_and_offset(
                    updated_item.created_at,
                    chrono::Utc,
                ),
                updated_at: chrono::DateTime::from_naive_utc_and_offset(
                    updated_item.updated_at,
                    chrono::Utc,
                ),
            })
        } else {
            Err(ServiceError::InvalidInput(
                "Login credential not found".to_string(),
            ))
        }
    }

    /// 从回收站恢复登录凭据
    pub async fn restore_login_credential(&self, id: i32) -> ServiceResult<LoginCredential> {
        let db = self.db.lock().await;
        let restored_item = db.restore_item(id).await?;

        // 获取关联的登录信息并解密（需要对称密钥）
        if let Some(login) = db.get_login_by_item_id(id).await? {
            // 注意：这里需要对称密钥来解密密码，但我们暂时返回加密的密码
            // 实际使用时应该通过带密钥的方法来恢复
            Ok(LoginCredential {
                id: restored_item.id,
                name: restored_item.name,
                username: login.username,
                password: "***ENCRYPTED***".to_string(), // 需要密钥解密
                website: login.website,
                notes: login.notes,
                favorite: restored_item.favorite,
                created_at: chrono::DateTime::from_naive_utc_and_offset(
                    restored_item.created_at,
                    chrono::Utc,
                ),
                updated_at: chrono::DateTime::from_naive_utc_and_offset(
                    restored_item.updated_at,
                    chrono::Utc,
                ),
            })
        } else {
            Err(ServiceError::InvalidInput(
                "Login credential not found".to_string(),
            ))
        }
    }

    /// 使用对称密钥从回收站恢复登录凭据
    pub async fn restore_login_credential_with_key(
        &self,
        id: i32,
        symmetric_key: &[u8; crate::crypto::KEY_SIZE],
    ) -> ServiceResult<LoginCredential> {
        use crate::crypto::symmetric::{EncryptedData, SymmetricCipher};

        let db = self.db.lock().await;
        let restored_item = db.restore_item(id).await?;

        // 获取关联的登录信息并解密
        if let Some(login) = db.get_login_by_item_id(id).await? {
            let cipher = SymmetricCipher::new(symmetric_key);

            // 解密密码
            let encrypted_data =
                EncryptedData::from_base64(&login.encrypted_password).map_err(|e| {
                    ServiceError::Decryption(format!("Failed to parse encrypted password: {}", e))
                })?;

            let decrypted_password = cipher.decrypt_text(&encrypted_data).map_err(|e| {
                ServiceError::Decryption(format!("Failed to decrypt password: {}", e))
            })?;

            // 解密备注（如果有的话）
            let decrypted_notes = if let Some(ref notes_encrypted) = login.notes {
                if !notes_encrypted.is_empty() {
                    match EncryptedData::from_base64(notes_encrypted) {
                        Ok(encrypted_notes_data) => {
                            match cipher.decrypt_text(&encrypted_notes_data) {
                                Ok(decrypted) => Some(decrypted),
                                Err(_) => {
                                    // 如果解密失败，可能是未加密的旧数据
                                    Some(notes_encrypted.clone())
                                }
                            }
                        }
                        Err(_) => {
                            // 如果解析失败，可能是未加密的旧数据
                            Some(notes_encrypted.clone())
                        }
                    }
                } else {
                    Some(notes_encrypted.clone())
                }
            } else {
                None
            };

            Ok(LoginCredential {
                id: restored_item.id,
                name: restored_item.name,
                username: login.username,
                password: decrypted_password,
                website: login.website,
                notes: decrypted_notes,
                favorite: restored_item.favorite,
                created_at: chrono::DateTime::from_naive_utc_and_offset(
                    restored_item.created_at,
                    chrono::Utc,
                ),
                updated_at: chrono::DateTime::from_naive_utc_and_offset(
                    restored_item.updated_at,
                    chrono::Utc,
                ),
            })
        } else {
            Err(ServiceError::InvalidInput(
                "Login credential not found".to_string(),
            ))
        }
    }

    /// 获取回收站中的登录凭据
    pub async fn get_trash_login_credentials(
        &self,
        vault_id: i32,
    ) -> ServiceResult<Vec<LoginCredential>> {
        let db = self.db.lock().await;
        let trash_items = db.get_trash_items(vault_id).await?;

        let mut credentials = Vec::new();
        for item in trash_items {
            if item.item_type == "login" {
                if let Some(login) = db.get_login_by_item_id(item.id).await? {
                    credentials.push(LoginCredential {
                        id: item.id,
                        name: item.name,
                        username: login.username,
                        password: "***DELETED***".to_string(), // 不显示已删除项目的密码
                        website: login.website,
                        notes: login.notes,
                        favorite: item.favorite,
                        created_at: chrono::DateTime::from_naive_utc_and_offset(
                            item.created_at,
                            chrono::Utc,
                        ),
                        updated_at: chrono::DateTime::from_naive_utc_and_offset(
                            item.updated_at,
                            chrono::Utc,
                        ),
                    });
                }
            }
        }

        Ok(credentials)
    }

    /// 永久删除登录凭据（物理删除）
    pub async fn permanently_delete_login_credential(&self, id: i32) -> ServiceResult<()> {
        let db = self.db.lock().await;
        db.permanently_delete_item(id).await?;
        Ok(())
    }

    /// 清理过期的已删除项目（超过30天自动永久删除）
    pub async fn cleanup_expired_deleted_items(&self) -> ServiceResult<u64> {
        let db = self.db.lock().await;
        let deleted_count = db.cleanup_expired_deleted_items().await?;
        Ok(deleted_count)
    }

    /// 获取需要永久删除的项目数量
    pub async fn get_items_pending_permanent_deletion_count(&self) -> ServiceResult<usize> {
        let db = self.db.lock().await;
        let items = db.get_items_for_permanent_deletion().await?;
        Ok(items.len())
    }

    /// 封存登录凭据
    pub async fn archive_login_credential(&self, id: i32) -> ServiceResult<LoginCredential> {
        let db = self.db.lock().await;

        // 封存项目
        let archived_item = db.archive_item(id).await?;

        // 获取登录信息
        let login = db
            .get_login_by_item_id(id)
            .await?
            .ok_or(ServiceError::InvalidInput("Login not found".to_string()))?;

        // 返回封存后的凭据（密码显示为已封存）
        Ok(LoginCredential {
            id: archived_item.id,
            name: archived_item.name,
            username: login.username,
            password: "***ARCHIVED***".to_string(),
            website: login.website,
            notes: login.notes,
            favorite: archived_item.favorite,
            created_at: chrono::DateTime::from_naive_utc_and_offset(
                archived_item.created_at,
                chrono::Utc,
            ),
            updated_at: chrono::DateTime::from_naive_utc_and_offset(
                archived_item.updated_at,
                chrono::Utc,
            ),
        })
    }

    /// 从封存中恢复登录凭据
    pub async fn unarchive_login_credential_with_key(
        &self,
        id: i32,
        symmetric_key: &[u8; crate::crypto::KEY_SIZE],
    ) -> ServiceResult<LoginCredential> {
        use crate::crypto::symmetric::{EncryptedData, SymmetricCipher};

        let db = self.db.lock().await;

        // 恢复项目
        let restored_item = db.unarchive_item(id).await?;

        // 获取登录信息
        let login = db
            .get_login_by_item_id(id)
            .await?
            .ok_or(ServiceError::InvalidInput("Login not found".to_string()))?;

        let cipher = SymmetricCipher::new(symmetric_key);

        // 解密密码
        let decrypted_password = if login.encrypted_password.is_empty() {
            String::new()
        } else {
            match EncryptedData::from_base64(&login.encrypted_password) {
                Ok(encrypted_data) => cipher.decrypt_text(&encrypted_data).map_err(|e| {
                    ServiceError::Decryption(format!("Failed to decrypt password: {}", e))
                })?,
                Err(_) => {
                    // 可能是旧的未加密数据，直接返回
                    login.encrypted_password.clone()
                }
            }
        };

        // 解密备注
        let decrypted_notes = if let Some(ref notes_text) = login.notes {
            if notes_text.is_empty() {
                Some(notes_text.clone())
            } else {
                match EncryptedData::from_base64(notes_text) {
                    Ok(encrypted_data) => {
                        match cipher.decrypt_text(&encrypted_data) {
                            Ok(decrypted) => Some(decrypted),
                            Err(_) => Some(notes_text.clone()), // 可能是旧的未加密数据
                        }
                    }
                    Err(_) => Some(notes_text.clone()), // 可能是旧的未加密数据
                }
            }
        } else {
            None
        };

        Ok(LoginCredential {
            id: restored_item.id,
            name: restored_item.name,
            username: login.username,
            password: decrypted_password,
            website: login.website,
            notes: decrypted_notes,
            favorite: restored_item.favorite,
            created_at: chrono::DateTime::from_naive_utc_and_offset(
                restored_item.created_at,
                chrono::Utc,
            ),
            updated_at: chrono::DateTime::from_naive_utc_and_offset(
                restored_item.updated_at,
                chrono::Utc,
            ),
        })
    }

    /// 获取已封存的登录凭据
    pub async fn get_archived_login_credentials(
        &self,
        vault_id: i32,
    ) -> ServiceResult<Vec<LoginCredential>> {
        let db = self.db.lock().await;
        let archived_items = db.get_archived_items(vault_id).await?;

        let mut credentials = Vec::new();

        for item in archived_items {
            if item.item_type == "login" {
                let login = db.get_login_by_item_id(item.id).await?;

                if let Some(login) = login {
                    credentials.push(LoginCredential {
                        id: item.id,
                        name: item.name,
                        username: login.username,
                        password: "***ARCHIVED***".to_string(), // 封存的凭据不显示密码
                        website: login.website,
                        notes: login.notes,
                        favorite: item.favorite,
                        created_at: chrono::DateTime::from_naive_utc_and_offset(
                            item.created_at,
                            chrono::Utc,
                        ),
                        updated_at: chrono::DateTime::from_naive_utc_and_offset(
                            item.updated_at,
                            chrono::Utc,
                        ),
                    });
                }
            }
        }

        Ok(credentials)
    }

    /// 批量封存登录凭据
    pub async fn batch_archive_login_credentials(&self, ids: Vec<i32>) -> ServiceResult<u64> {
        let db = self.db.lock().await;
        let affected_rows = db.batch_archive_items(ids).await?;
        Ok(affected_rows)
    }

    /// 批量从封存中恢复登录凭据
    pub async fn batch_unarchive_login_credentials(&self, ids: Vec<i32>) -> ServiceResult<u64> {
        let db = self.db.lock().await;
        let affected_rows = db.batch_unarchive_items(ids).await?;
        Ok(affected_rows)
    }

    // 同步状态操作
    pub async fn update_sync_status(
        &self,
        last_sync_time: Option<chrono::DateTime<chrono::Utc>>,
        status: Option<String>,
        token: Option<String>,
    ) -> ServiceResult<()> {
        let db = self.db.lock().await;
        db.update_sync_status(last_sync_time, status, token).await?;
        Ok(())
    }

    pub async fn get_sync_status(
        &self,
    ) -> ServiceResult<Option<super::entities::sync_status::Model>> {
        let db = self.db.lock().await;
        Ok(db.get_sync_status().await?)
    }

    /// 使用外部对称密钥获取指定密码库的所有登录凭据
    pub async fn get_login_credentials_by_vault_with_key(
        &self,
        vault_id: i32,
        symmetric_key: &[u8; crate::crypto::KEY_SIZE],
    ) -> ServiceResult<Vec<LoginCredential>> {
        use crate::crypto::symmetric::{EncryptedData, SymmetricCipher};

        let db = self.db.lock().await;
        let items_with_logins = db.get_login_entries_by_vault(vault_id).await?;

        let cipher = SymmetricCipher::new(symmetric_key);
        let mut credentials = Vec::new();

        for (item, login_opt) in items_with_logins {
            if let Some(login) = login_opt {
                // 从 base64 字符串反序列化 EncryptedData
                let encrypted_data = EncryptedData::from_base64(&login.encrypted_password)
                    .map_err(|e| {
                        ServiceError::Decryption(format!(
                            "Failed to parse encrypted password: {}",
                            e
                        ))
                    })?;

                // 使用外部对称密钥解密密码
                let decrypted_password = cipher.decrypt_text(&encrypted_data).map_err(|e| {
                    ServiceError::Decryption(format!("Failed to decrypt password: {}", e))
                })?;

                // 解密备注（如果有的话）
                let decrypted_notes = if let Some(ref notes_encrypted) = login.notes {
                    if !notes_encrypted.is_empty() {
                        match EncryptedData::from_base64(notes_encrypted) {
                            Ok(encrypted_notes_data) => {
                                match cipher.decrypt_text(&encrypted_notes_data) {
                                    Ok(decrypted) => Some(decrypted),
                                    Err(_) => {
                                        // 如果解密失败，可能是未加密的旧数据
                                        Some(notes_encrypted.clone())
                                    }
                                }
                            }
                            Err(_) => {
                                // 如果解析失败，可能是未加密的旧数据
                                Some(notes_encrypted.clone())
                            }
                        }
                    } else {
                        Some(notes_encrypted.clone())
                    }
                } else {
                    None
                };

                credentials.push(LoginCredential {
                    id: item.id,
                    name: item.name,
                    username: login.username,
                    password: decrypted_password,
                    website: login.website,
                    notes: decrypted_notes,
                    favorite: item.favorite,
                    created_at: chrono::DateTime::from_naive_utc_and_offset(
                        item.created_at,
                        chrono::Utc,
                    ),
                    updated_at: chrono::DateTime::from_naive_utc_and_offset(
                        item.updated_at,
                        chrono::Utc,
                    ),
                });
            }
        }

        Ok(credentials)
    }

    /// 使用外部对称密钥根据ID获取登录凭据
    pub async fn get_login_credential_by_id_with_key(
        &self,
        id: i32,
        symmetric_key: &[u8; crate::crypto::KEY_SIZE],
    ) -> ServiceResult<Option<LoginCredential>> {
        use crate::crypto::symmetric::{EncryptedData, SymmetricCipher};

        let db = self.db.lock().await;

        let item = match db.get_item_by_id(id).await? {
            Some(item) => item,
            None => return Ok(None),
        };

        let login = match db.get_login_by_item_id(id).await? {
            Some(login) => login,
            None => return Ok(None),
        };

        let cipher = SymmetricCipher::new(symmetric_key);

        // 从 base64 字符串反序列化 EncryptedData
        let encrypted_data =
            EncryptedData::from_base64(&login.encrypted_password).map_err(|e| {
                ServiceError::Decryption(format!("Failed to parse encrypted data: {}", e))
            })?;

        // 使用外部对称密钥解密密码
        let decrypted_password = cipher
            .decrypt_text(&encrypted_data)
            .map_err(|e| ServiceError::Decryption(format!("Failed to decrypt password: {}", e)))?;

        // 解密备注（如果有的话）
        let decrypted_notes = if let Some(ref notes_encrypted) = login.notes {
            if !notes_encrypted.is_empty() {
                match EncryptedData::from_base64(notes_encrypted) {
                    Ok(encrypted_notes_data) => {
                        match cipher.decrypt_text(&encrypted_notes_data) {
                            Ok(decrypted) => Some(decrypted),
                            Err(_) => {
                                // 如果解密失败，可能是未加密的旧数据
                                Some(notes_encrypted.clone())
                            }
                        }
                    }
                    Err(_) => {
                        // 如果解析失败，可能是未加密的旧数据
                        Some(notes_encrypted.clone())
                    }
                }
            } else {
                Some(notes_encrypted.clone())
            }
        } else {
            None
        };

        Ok(Some(LoginCredential {
            id: item.id,
            name: item.name,
            username: login.username,
            password: decrypted_password,
            website: login.website,
            notes: decrypted_notes,
            favorite: item.favorite,
            created_at: chrono::DateTime::from_naive_utc_and_offset(item.created_at, chrono::Utc),
            updated_at: chrono::DateTime::from_naive_utc_and_offset(item.updated_at, chrono::Utc),
        }))
    }

    /// 使用外部对称密钥更新登录凭据
    pub async fn update_login_credential_with_key(
        &self,
        id: i32,
        name: Option<String>,
        username: Option<String>,
        password: Option<String>,
        website: Option<String>,
        notes: Option<String>,
        favorite: Option<bool>,
        symmetric_key: &[u8; crate::crypto::KEY_SIZE],
    ) -> ServiceResult<LoginCredential> {
        use crate::crypto::symmetric::SymmetricCipher;

        if let Some(ref name) = name {
            if name.trim().is_empty() {
                return Err(ServiceError::InvalidInput(
                    "Name cannot be empty".to_string(),
                ));
            }
        }

        let cipher = SymmetricCipher::new(symmetric_key);
        let db = self.db.lock().await;

        // 更新条目
        let updated_item = db.update_item(id, name, favorite).await?;

        // 准备加密的密码
        let encrypted_password = if let Some(password) = password {
            if password.trim().is_empty() {
                return Err(ServiceError::InvalidInput(
                    "Password cannot be empty".to_string(),
                ));
            }
            let encrypted_data = cipher
                .encrypt_text(&password)
                .map_err(|e| ServiceError::Encryption(e.to_string()))?;
            Some(encrypted_data.to_base64())
        } else {
            None
        };

        // 加密备注（如果有的话）
        let encrypted_notes = if let Some(ref notes_text) = notes {
            if !notes_text.is_empty() {
                let encrypted_notes_data = cipher
                    .encrypt_text(notes_text)
                    .map_err(|e| ServiceError::Encryption(e.to_string()))?;
                Some(encrypted_notes_data.to_base64())
            } else {
                Some(notes_text.clone())
            }
        } else {
            notes
        };

        // 更新登录信息
        let updated_login = db
            .update_login(id, username, encrypted_password, website, encrypted_notes)
            .await?;

        // 解密最终的密码和备注用于返回
        let final_password = cipher
            .decrypt_text(
                &crate::crypto::symmetric::EncryptedData::from_base64(
                    &updated_login.encrypted_password,
                )
                .map_err(|e| ServiceError::Decryption(e.to_string()))?,
            )
            .map_err(|e| ServiceError::Decryption(e.to_string()))?;

        // 解密备注
        let final_notes = if let Some(ref notes_encrypted) = updated_login.notes {
            if !notes_encrypted.is_empty() {
                match crate::crypto::symmetric::EncryptedData::from_base64(notes_encrypted) {
                    Ok(encrypted_notes_data) => {
                        match cipher.decrypt_text(&encrypted_notes_data) {
                            Ok(decrypted) => Some(decrypted),
                            Err(_) => {
                                // 如果解密失败，可能是未加密的旧数据
                                Some(notes_encrypted.clone())
                            }
                        }
                    }
                    Err(_) => {
                        // 如果解析失败，可能是未加密的旧数据
                        Some(notes_encrypted.clone())
                    }
                }
            } else {
                Some(notes_encrypted.clone())
            }
        } else {
            None
        };

        Ok(LoginCredential {
            id: updated_item.id,
            name: updated_item.name,
            username: updated_login.username,
            password: final_password,
            website: updated_login.website,
            notes: final_notes,
            favorite: updated_item.favorite,
            created_at: chrono::DateTime::from_naive_utc_and_offset(
                updated_item.created_at,
                chrono::Utc,
            ),
            updated_at: chrono::DateTime::from_naive_utc_and_offset(
                updated_item.updated_at,
                chrono::Utc,
            ),
        })
    }

    /// 使用外部对称密钥根据域名搜索凭据
    pub async fn search_credentials_by_domain_with_key(
        &self,
        domain: &str,
        symmetric_key: &[u8; crate::crypto::KEY_SIZE],
    ) -> ServiceResult<Vec<LoginCredential>> {
        use crate::crypto::symmetric::{EncryptedData, SymmetricCipher};

        if domain.trim().is_empty() {
            return Ok(Vec::new());
        }

        let db = self.db.lock().await;
        let items_with_logins = db.search_login_entries_by_domain(domain).await?;

        let cipher = SymmetricCipher::new(symmetric_key);
        let mut credentials = Vec::new();

        for (item, login) in items_with_logins {
            // 从 base64 字符串反序列化 EncryptedData
            let encrypted_data =
                EncryptedData::from_base64(&login.encrypted_password).map_err(|e| {
                    ServiceError::Decryption(format!("Failed to parse encrypted password: {}", e))
                })?;

            // 使用外部对称密钥解密密码
            let decrypted_password = cipher.decrypt_text(&encrypted_data).map_err(|e| {
                ServiceError::Decryption(format!("Failed to decrypt password: {}", e))
            })?;

            // 解密备注（如果有的话）
            let decrypted_notes = if let Some(ref notes_encrypted) = login.notes {
                if !notes_encrypted.is_empty() {
                    match EncryptedData::from_base64(notes_encrypted) {
                        Ok(encrypted_notes_data) => {
                            match cipher.decrypt_text(&encrypted_notes_data) {
                                Ok(decrypted) => Some(decrypted),
                                Err(_) => {
                                    // 如果解密失败，可能是未加密的旧数据
                                    Some(notes_encrypted.clone())
                                }
                            }
                        }
                        Err(_) => {
                            // 如果解析失败，可能是未加密的旧数据
                            Some(notes_encrypted.clone())
                        }
                    }
                } else {
                    Some(notes_encrypted.clone())
                }
            } else {
                None
            };

            credentials.push(LoginCredential {
                id: item.id,
                name: item.name,
                username: login.username,
                password: decrypted_password,
                website: login.website,
                notes: decrypted_notes,
                favorite: item.favorite,
                created_at: chrono::DateTime::from_naive_utc_and_offset(
                    item.created_at,
                    chrono::Utc,
                ),
                updated_at: chrono::DateTime::from_naive_utc_and_offset(
                    item.updated_at,
                    chrono::Utc,
                ),
            });
        }

        Ok(credentials)
    }

    /// 使用外部对称密钥获取收藏的凭据
    pub async fn get_favorite_credentials_with_key(
        &self,
        vault_id: i32,
        symmetric_key: &[u8; crate::crypto::KEY_SIZE],
    ) -> ServiceResult<Vec<LoginCredential>> {
        use crate::crypto::symmetric::{EncryptedData, SymmetricCipher};

        let db = self.db.lock().await;
        let favorite_items = db.get_favorite_items(vault_id).await?;

        let cipher = SymmetricCipher::new(symmetric_key);
        let mut credentials = Vec::new();

        for item in favorite_items {
            if item.item_type == "login" {
                if let Some(login) = db.get_login_by_item_id(item.id).await? {
                    // 从 base64 字符串反序列化 EncryptedData
                    let encrypted_data = EncryptedData::from_base64(&login.encrypted_password)
                        .map_err(|e| {
                            ServiceError::Decryption(format!(
                                "Failed to parse encrypted password: {}",
                                e
                            ))
                        })?;

                    // 使用外部对称密钥解密密码
                    let decrypted_password = cipher.decrypt_text(&encrypted_data).map_err(|e| {
                        ServiceError::Decryption(format!("Failed to decrypt password: {}", e))
                    })?;

                    // 解密备注（如果有的话）
                    let decrypted_notes = if let Some(ref notes_encrypted) = login.notes {
                        if !notes_encrypted.is_empty() {
                            match EncryptedData::from_base64(notes_encrypted) {
                                Ok(encrypted_notes_data) => {
                                    match cipher.decrypt_text(&encrypted_notes_data) {
                                        Ok(decrypted) => Some(decrypted),
                                        Err(_) => {
                                            // 如果解密失败，可能是未加密的旧数据
                                            Some(notes_encrypted.clone())
                                        }
                                    }
                                }
                                Err(_) => {
                                    // 如果解析失败，可能是未加密的旧数据
                                    Some(notes_encrypted.clone())
                                }
                            }
                        } else {
                            Some(notes_encrypted.clone())
                        }
                    } else {
                        None
                    };

                    credentials.push(LoginCredential {
                        id: item.id,
                        name: item.name,
                        username: login.username,
                        password: decrypted_password,
                        website: login.website,
                        notes: decrypted_notes,
                        favorite: item.favorite,
                        created_at: chrono::DateTime::from_naive_utc_and_offset(
                            item.created_at,
                            chrono::Utc,
                        ),
                        updated_at: chrono::DateTime::from_naive_utc_and_offset(
                            item.updated_at,
                            chrono::Utc,
                        ),
                    });
                }
            }
        }

        Ok(credentials)
    }
}
