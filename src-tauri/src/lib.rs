// Learn more about <PERSON>ri commands at https://tauri.app/develop/calling-rust/

pub mod async_handler; // 添加异步处理器模块
pub mod auth;
pub mod config; // 配置管理模块
pub mod crypto; // 更改为新的模块化加密系统
mod errors;
pub mod http;
pub mod hybrid_storage; // 已更新为使用新的加密系统

// 桌面平台特定模块
#[cfg(not(any(target_os = "android", target_os = "ios")))]
pub mod native_messaging; // 模块化的 Native Messaging

#[cfg(not(any(target_os = "android", target_os = "ios")))]
pub mod tray; // 跨平台可插拔托盘模块

#[cfg(not(any(target_os = "android", target_os = "ios")))]
pub mod tray_integration; // 托盘集成模块

mod state; // 认证模块
pub mod sync; // 独立模块化的日志同步系统

// 移动平台特定模块
#[cfg(any(target_os = "android", target_os = "ios"))]
pub mod mobile; // 移动端功能模块

use crate::config::CONFIG;
use state::AppState;
use tauri::{Emitter, Manager};

// 桌面平台特定导入
#[cfg(not(any(target_os = "android", target_os = "ios")))]
/// 获取应用版本信息
#[tauri::command]
fn get_app_version() -> String {
    env!("CARGO_PKG_VERSION").to_string()
}

/// 获取应用信息
#[tauri::command]
fn get_app_info() -> serde_json::Value {
    serde_json::json!({
        "name": env!("CARGO_PKG_NAME"),
        "version": env!("CARGO_PKG_VERSION"),
        "description": env!("CARGO_PKG_DESCRIPTION"),
        "authors": env!("CARGO_PKG_AUTHORS").split(':').collect::<Vec<&str>>(),
        "repository": env!("CARGO_PKG_REPOSITORY"),
        "license": env!("CARGO_PKG_LICENSE"),
        "rust_version": env!("CARGO_PKG_RUST_VERSION"),
    })
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化配置（这会触发环境变量加载）
    let _config = &*CONFIG;
    log::info!("应用配置已加载，服务端地址: {}", _config.server_base_url);

    // 初始化日志记录器 (在开发时很有用)
    env_logger::init();

    let app_state = AppState::new();

    let mut builder = tauri::Builder::default()
        .plugin(tauri_plugin_store::Builder::new().build())
        .plugin(tauri_plugin_stronghold::Builder::new(|_pass| todo!()).build())
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_os::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .plugin(tauri_plugin_machine_uid::init());

    // 桌面平台特定插件
    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    {
        builder = builder.plugin(tauri_plugin_single_instance::init(|app, args, cwd| {
            log::info!(
                "Another instance launched with args: {:?}, cwd: {}",
                args,
                cwd
            );
            // Bring the existing window to the front
            if let Some(window) = app.get_webview_window("main") {
                let _ = window.unminimize();
                let _ = window.set_focus();
            }
        }));
    }

    // 移动平台特定插件
    #[cfg(any(target_os = "android", target_os = "ios"))]
    {
        builder = builder
            .plugin(tauri_plugin_biometric::init())
            .plugin(tauri_plugin_keychain::init());
    }

    builder
        .manage(app_state)
        .setup(|app| setup_application(app))
        .invoke_handler(tauri::generate_handler![
            // 核心功能命令
            get_app_version,
            get_app_info,
            // Hybrid Storage 命令
            hybrid_storage::commands::get_all_vaults_hybrid,
            hybrid_storage::commands::create_vault_hybrid,
            hybrid_storage::commands::update_vault_hybrid,
            hybrid_storage::commands::delete_vault_hybrid,
            hybrid_storage::commands::get_login_credentials_by_vault_hybrid,
            hybrid_storage::commands::get_all_login_credentials_hybrid,
            hybrid_storage::commands::get_login_credential_by_id_hybrid,
            hybrid_storage::commands::save_login_credential_hybrid,
            hybrid_storage::commands::add_login_credential_hybrid,
            hybrid_storage::commands::update_login_credential_hybrid,
            hybrid_storage::commands::delete_login_credential_hybrid,
            hybrid_storage::commands::search_credentials_by_domain_hybrid,
            hybrid_storage::commands::get_favorite_credentials_hybrid,
            hybrid_storage::commands::get_all_favorite_credentials_hybrid,
            // Hybrid Storage 软删除命令
            hybrid_storage::commands::soft_delete_login_credential_hybrid,
            hybrid_storage::commands::restore_login_credential_hybrid,
            hybrid_storage::commands::get_trash_login_credentials_hybrid,
            hybrid_storage::commands::get_all_trash_login_credentials_hybrid,
            hybrid_storage::commands::permanently_delete_login_credential_hybrid,
            hybrid_storage::commands::cleanup_expired_deleted_items_hybrid,
            hybrid_storage::commands::get_items_pending_permanent_deletion_count_hybrid,
            // Hybrid Storage 封存命令
            hybrid_storage::commands::archive_login_credential_hybrid,
            hybrid_storage::commands::unarchive_login_credential_hybrid,
            hybrid_storage::commands::get_archived_login_credentials_hybrid,
            hybrid_storage::commands::get_all_archived_login_credentials_hybrid,
            hybrid_storage::commands::batch_archive_login_credentials_hybrid,
            hybrid_storage::commands::batch_unarchive_login_credentials_hybrid,
            // ORM 服务管理命令
            hybrid_storage::commands::get_orm_service_status,
            hybrid_storage::commands::retry_orm_service_initialization,
            hybrid_storage::commands::reset_orm_service,
            hybrid_storage::commands::check_crypto_unlock_capability,
            // 认证相关命令
            auth::commands::send_verification_code,
            auth::commands::validate_password_strength,
            auth::commands::check_username_availability,
            auth::commands::check_contact_availability,
            auth::commands::validate_password_hint,
            // Auth 模块命令 - 注册功能（新流程）
            auth::commands::register_user_remote_only,
            // Auth 模块命令 - 高安全性功能
            auth::commands::hash_password_secure,
            auth::commands::validate_password_strength_enhanced,
            auth::commands::create_user_vault,
            auth::commands::check_keychain_status,
            auth::commands::get_kdf_recommendations,
            // Auth 模块命令 - 远程认证功能
            auth::commands::register_user_remote,
            auth::commands::login_user_remote,
            auth::commands::test_remote_connection,
            auth::commands::get_remote_server_status,
            auth::commands::register_complete_flow,
            auth::commands::login_user_universal,
            // Auth 模块命令 - Token 管理功能
            auth::commands::get_current_token,
            auth::commands::get_token_info,
            auth::commands::clear_token,
            auth::commands::is_token_expiring_soon,
            auth::commands::refresh_access_token,
            // Auth 模块命令 - 密钥管理功能
            auth::commands::check_user_keys_status,
            auth::commands::cleanup_user_keys,
            auth::commands::generate_test_keypair,
            // Auth 模块命令 - 远程密钥同步功能
            auth::commands::sync_remote_keys,
            auth::commands::get_remote_keys_info,
            // Auth 模块命令 - 用户状态管理功能
            auth::commands::get_current_user_info,
            auth::commands::is_user_logged_in,
            auth::commands::logout_current_user,
            auth::commands::reload_user_info,
            // 桌面平台特定命令
            #[cfg(not(any(target_os = "android", target_os = "ios")))]
            tray_integration::show_main_window_command,
            #[cfg(not(any(target_os = "android", target_os = "ios")))]
            tray_integration::hide_to_tray_command,
            #[cfg(not(any(target_os = "android", target_os = "ios")))]
            tray_integration::quit_application_command,
            // 移动平台特定命令
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::initialize_mobile_feature_manager,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::get_device_info,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::get_mobile_device_info,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::get_device_uid,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::get_platform_info,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::get_detailed_platform_info,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::check_platform_capabilities,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::save_secure_data,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::get_secure_data,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::remove_secure_data,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::authenticate_with_biometric,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::check_biometric_availability,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::get_mobile_platform_status,
            #[cfg(any(target_os = "android", target_os = "ios"))]
            mobile::commands::reinitialize_mobile_platform,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

/// 应用程序设置函数
fn setup_application(app: &mut tauri::App) -> Result<(), Box<dyn std::error::Error>> {
    let app_handle = app.handle().clone();

    // 桌面平台特定设置
    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    {
        setup_desktop_features(&app_handle)?;
    }

    // 移动平台特定设置
    #[cfg(any(target_os = "android", target_os = "ios"))]
    {
        setup_mobile_features(&app_handle)?;
    }

    // 通用设置
    setup_common_features(&app_handle)?;

    Ok(())
}

/// 桌面平台特定设置
#[cfg(not(any(target_os = "android", target_os = "ios")))]
fn setup_desktop_features(app_handle: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    // 启动 Native Messaging
    if let Err(e) = native_messaging::setup_native_messaging(app_handle.clone()) {
        log::error!("启动 Native Messaging 失败: {}", e);
    } else {
        log::info!("Native Messaging 服务已启动");
    }

    // 创建桌面窗口
    create_desktop_window(app_handle)?;

    Ok(())
}

/// 移动平台特定设置
#[cfg(any(target_os = "android", target_os = "ios"))]
fn setup_mobile_features(_app_handle: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    log::info!("移动平台初始化");

    // 初始化移动端功能管理器
    let mut mobile_manager = mobile::feature_manager::MobileFeatureManager::new();

    // 异步初始化移动端功能
    async_handler::AsyncTaskManager::spawn_task(async move {
        if let Err(e) = mobile_manager.initialize().await {
            log::error!("移动端功能初始化失败: {}", e);
        } else {
            log::info!("移动端功能初始化成功");
        }
    });

    Ok(())
}

/// 通用功能设置
fn setup_common_features(app_handle: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    let main_window = app_handle.get_webview_window("main");
    let app_handle_clone = app_handle.clone();

    // 使用 async_handler 来运行异步初始化代码
    async_handler::AsyncTaskManager::spawn_task(async move {
        let app_handle = app_handle_clone;
        let app_state = app_handle.state::<AppState>().clone();

        log::info!("设置 AppHandle 到应用状态...");
        app_state.set_app_handle(app_handle.clone()).await;

        log::info!("AppHandle 已设置，开始初始化 ORM 服务...");

        // 初始化 ORM 服务
        match initialize_orm_service(&app_handle, &app_state).await {
            Ok(_) => {
                log::info!("ORM 服务初始化成功");
                let service_guard = app_state.orm_service_lock().await;
                if service_guard.is_some() {
                    log::info!("ORM 服务已正确设置到应用状态中");
                } else {
                    log::error!("ORM 服务初始化成功但未正确设置到应用状态中");
                }
            }
            Err(e) => {
                log::error!("ORM 服务初始化失败: {}", e);
                if let Some(window) = main_window.as_ref() {
                    let _ = window.eval(format!(
                        "alert('ORM service initialization failed: {}')",
                        e
                    ));
                }
            }
        }

        log::info!("开始初始化 Token 管理器...");

        // 初始化 Token 管理器
        if let Err(e) =
            auth::token_manager::initialize_global_token_manager(app_handle.clone()).await
        {
            log::error!("Failed to initialize token manager: {}", e);
        } else {
            log::info!("Token manager initialized successfully.");
        }

        // 检查是否有已登录的用户
        if app_state.has_logged_in_user().await {
            let user = app_state.get_current_user().await;
            if let Some(user) = user {
                log::info!("发现已登录用户: {}", user.contact);
                if let Some(window) = main_window.as_ref() {
                    let _ = window.emit("user_logged_in", &user);
                }
            }
        } else {
            log::info!("没有发现已登录的用户");
        }

        // 桌面平台特定初始化
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            // 初始化托盘集成
            if let Ok(tray_integration) = tray_integration::AppTrayIntegration::new(
                app_handle.clone(),
                Some("main".to_string()),
            )
            .await
            {
                let tray_integration_arc = std::sync::Arc::new(tray_integration);

                // 设置窗口关闭行为
                if let Some(window) = main_window.as_ref() {
                    let _ = tray_integration::setup_window_close_behavior(
                        window,
                        tray_integration_arc.clone(),
                    );
                }

                // 注册到应用状态
                app_handle.manage(tray_integration_arc);
            }
        }

        // 初始化完成后通知前端
        if let Some(window) = main_window.as_ref() {
            let _ = window.emit("app_ready", ());
        }

        log::info!("应用初始化完成");
    });

    Ok(())
}

/// 配置桌面窗口
#[cfg(not(any(target_os = "android", target_os = "ios")))]
fn create_desktop_window(app_handle: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    // 获取已存在的主窗口（由 tauri.conf.json 自动创建）
    if let Some(window) = app_handle.get_webview_window("main") {
        // 根据平台设置不同的窗口属性
        #[cfg(not(target_os = "macos"))]
        {
            // 非 macOS 平台设置无装饰窗口
            window.set_decorations(false)?;
        }

        #[cfg(debug_assertions)] // 只在 debug 模式下打开 devtools
        {
            window.open_devtools();
        }

        // 显示窗口（其他配置已在 tauri.conf.json 中设置）
        window.show()?;

        log::info!("主窗口配置完成");
    } else {
        let error_msg = "无法找到主窗口";
        log::error!("{}", error_msg);
        return Err(error_msg.into());
    }

    Ok(())
}

/// 初始化 ORM 服务
pub async fn initialize_orm_service(
    app_handle: &tauri::AppHandle,
    app_state: &AppState,
) -> Result<(), String> {
    use crate::hybrid_storage::{OrmDatabaseManager, OrmPasswordService};

    log::info!("开始获取应用数据目录...");

    // 获取应用数据目录
    let app_data_dir = app_handle
        .path()
        .app_local_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;

    log::info!("应用数据目录: {:?}", app_data_dir);

    // 确保目录存在
    std::fs::create_dir_all(&app_data_dir)
        .map_err(|e| format!("Failed to create app data dir: {}", e))?;

    log::info!("应用数据目录创建成功");

    // 创建数据库路径
    let db_path = app_data_dir.join("hybrid_vault.db");
    log::info!("数据库路径: {:?}", db_path);

    // 检查加密系统状态并等待解锁
    log::info!("检查加密系统状态...");
    let crypto_arc = app_state.crypto_arc();

    // 实现重试机制等待加密系统解锁
    const MAX_WAIT_SECONDS: u64 = 30;
    const CHECK_INTERVAL_SECONDS: u64 = 1;
    let mut attempts = 0;
    let max_attempts = MAX_WAIT_SECONDS / CHECK_INTERVAL_SECONDS;

    // 发送初始化状态事件到前端
    if let Some(window) = app_handle.get_webview_window("main") {
        let _ = window.emit(
            "orm_initialization_status",
            serde_json::json!({
                "status": "checking_crypto_system",
                "message": "正在检查加密系统状态..."
            }),
        );
    }

    loop {
        let crypto_state = crypto_arc.state().await;
        log::info!(
            "当前加密系统状态: {:?} (尝试 {}/{})",
            crypto_state,
            attempts + 1,
            max_attempts
        );

        match crypto_state {
            crate::crypto::vault_crypto::CryptoState::Uninitialized => {
                log::info!("加密系统未初始化，使用默认配置初始化...");

                // 发送状态更新
                if let Some(window) = app_handle.get_webview_window("main") {
                    let _ = window.emit(
                        "orm_initialization_status",
                        serde_json::json!({
                            "status": "initializing_crypto",
                            "message": "正在初始化加密系统..."
                        }),
                    );
                }

                // 生成一个有效的盐值
                let salt = match crate::crypto::key_derivation::generate_salt() {
                    Ok(s) => s,
                    Err(e) => {
                        log::error!("生成盐值失败: {}", e);
                        return Err(format!("Failed to generate salt: {}", e));
                    }
                };

                log::info!("生成的盐值: {}", salt);
                let default_password = "default_password_123!"; // 在生产环境中应该要求用户设置密码

                crypto_arc
                    .initialize(default_password, &salt)
                    .await
                    .map_err(|e| format!("Failed to initialize crypto system: {}", e))?;

                log::info!("加密系统初始化成功");
                break; // 初始化成功，退出循环
            }
            crate::crypto::vault_crypto::CryptoState::Locked => {
                log::info!(
                    "加密系统已锁定，等待解锁... (尝试 {}/{})",
                    attempts + 1,
                    max_attempts
                );

                // 发送状态更新
                if let Some(window) = app_handle.get_webview_window("main") {
                    let _ = window.emit("orm_initialization_status", serde_json::json!({
                        "status": "waiting_for_unlock",
                        "message": format!("等待加密系统解锁... ({}/{})", attempts + 1, max_attempts),
                        "progress": (attempts as f64 / max_attempts as f64 * 100.0) as u32
                    }));
                }

                if attempts >= max_attempts {
                    let error_msg = format!(
                        "加密系统在 {} 秒内未解锁，ORM 服务初始化超时。请手动解锁保险库后重试。",
                        MAX_WAIT_SECONDS
                    );
                    log::error!("{}", error_msg);

                    // 发送超时错误状态
                    if let Some(window) = app_handle.get_webview_window("main") {
                        let _ = window.emit(
                            "orm_initialization_status",
                            serde_json::json!({
                                "status": "timeout",
                                "message": error_msg,
                                "error": true
                            }),
                        );
                    }

                    return Err(error_msg);
                }

                // 等待一秒后重试
                tokio::time::sleep(tokio::time::Duration::from_secs(CHECK_INTERVAL_SECONDS)).await;
                attempts += 1;
                continue;
            }
            crate::crypto::vault_crypto::CryptoState::Unlocked => {
                log::info!("加密系统已解锁，可以正常使用");
                break; // 已解锁，退出循环
            }
            crate::crypto::vault_crypto::CryptoState::Error(ref err) => {
                let error_msg = format!("加密系统处于错误状态: {}", err);
                log::error!("{}", error_msg);

                // 发送错误状态
                if let Some(window) = app_handle.get_webview_window("main") {
                    let _ = window.emit(
                        "orm_initialization_status",
                        serde_json::json!({
                            "status": "crypto_error",
                            "message": error_msg,
                            "error": true
                        }),
                    );
                }

                return Err(error_msg);
            }
        }
    }

    log::info!("加密系统状态检查完成，开始初始化数据库...");

    // 发送数据库初始化状态
    if let Some(window) = app_handle.get_webview_window("main") {
        let _ = window.emit(
            "orm_initialization_status",
            serde_json::json!({
                "status": "initializing_database",
                "message": "正在初始化数据库..."
            }),
        );
    }

    // 初始化 ORM 数据库管理器
    log::info!("开始初始化 ORM 数据库管理器...");
    let db_manager = OrmDatabaseManager::new(db_path).await.map_err(|e| {
        let error_msg = format!("Failed to initialize ORM database: {}", e);
        log::error!("{}", error_msg);

        // 发送数据库错误状态
        if let Some(window) = app_handle.get_webview_window("main") {
            let _ = window.emit(
                "orm_initialization_status",
                serde_json::json!({
                    "status": "database_error",
                    "message": error_msg,
                    "error": true
                }),
            );
        }

        error_msg
    })?;

    log::info!("ORM 数据库管理器初始化成功");

    // 创建 ORM 密码服务
    log::info!("创建 ORM 密码服务...");
    let orm_service = OrmPasswordService::new(db_manager);

    log::info!("ORM 密码服务创建成功");

    // 设置到应用状态
    log::info!("设置 ORM 服务到应用状态...");
    app_state.set_orm_service(orm_service).await;

    // 发送成功状态
    if let Some(window) = app_handle.get_webview_window("main") {
        let _ = window.emit(
            "orm_initialization_status",
            serde_json::json!({
                "status": "success",
                "message": "ORM 服务初始化成功"
            }),
        );
    }

    log::info!("ORM service initialized successfully");
    Ok(())
}
