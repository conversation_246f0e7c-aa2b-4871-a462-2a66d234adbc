/// 生物识别认证模块
///
/// 使用 Tauri 官方 biometric 插件提供跨平台生物识别功能
use crate::mobile::{
    errors::{MobileError, MobileResult},
    platform::MobilePlatform,
};
use serde::{Deserialize, Serialize};

#[cfg(any(target_os = "android", target_os = "ios"))]
use tauri_plugin_biometric::{AuthOptions, BiometricExt};

/// 生物识别认证配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BiometricConfig {
    /// 认证提示信息
    pub prompt_message: String,
    /// 取消按钮文本
    pub cancel_title: Option<String>,
    /// 回退按钮文本（仅iOS）
    pub fallback_title: Option<String>,
    /// 是否允许设备凭据认证
    pub allow_device_credential: bool,
    /// 认证标题（仅Android）
    pub title: Option<String>,
    /// 认证副标题（仅Android）
    pub subtitle: Option<String>,
    /// 是否需要确认（仅Android）
    pub confirmation_required: Option<bool>,
}

impl Default for BiometricConfig {
    fn default() -> Self {
        Self {
            prompt_message: "请使用生物识别验证身份".to_string(),
            cancel_title: Some("取消".to_string()),
            fallback_title: Some("使用密码".to_string()),
            allow_device_credential: true,
            title: Some("生物识别认证".to_string()),
            subtitle: Some("请验证您的身份".to_string()),
            confirmation_required: Some(true),
        }
    }
}

/// 生物识别状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BiometricStatus {
    /// 是否可用
    pub is_available: bool,
    /// 错误信息（如果不可用）
    pub error: Option<String>,
    /// 支持的生物识别类型
    pub biometric_types: Vec<String>,
}

/// 生物识别管理器
pub struct BiometricManager {
    /// 配置信息
    config: BiometricConfig,
    /// Tauri应用句柄
    #[cfg(any(target_os = "android", target_os = "ios"))]
    app_handle: tauri::AppHandle,
}

impl BiometricManager {
    /// 创建新的生物识别管理器
    ///
    /// # 参数
    /// * `app_handle` - Tauri应用句柄
    /// * `config` - 生物识别配置
    ///
    /// # 返回值
    /// 返回生物识别管理器实例
    #[cfg(any(target_os = "android", target_os = "ios"))]
    pub fn new(app_handle: tauri::AppHandle, config: BiometricConfig) -> Self {
        Self { config, app_handle }
    }

    /// 创建默认配置的生物识别管理器
    ///
    /// # 参数
    /// * `app_handle` - Tauri应用句柄
    ///
    /// # 返回值
    /// 返回生物识别管理器实例
    #[cfg(any(target_os = "android", target_os = "ios"))]
    pub fn with_default_config(app_handle: tauri::AppHandle) -> Self {
        Self::new(app_handle, BiometricConfig::default())
    }

    /// 检查生物识别状态
    ///
    /// # 返回值
    /// 返回生物识别状态信息
    #[cfg(any(target_os = "android", target_os = "ios"))]
    pub async fn check_status(&self) -> MobileResult<BiometricStatus> {
        match self.app_handle.biometric().status() {
            Ok(status) => {
                let biometric_types = if status.is_available {
                    // 根据平台返回支持的生物识别类型
                    #[cfg(target_os = "ios")]
                    {
                        vec!["Touch ID / Face ID".to_string()]
                    }
                    #[cfg(target_os = "android")]
                    {
                        vec!["指纹识别".to_string(), "面部识别".to_string()]
                    }
                } else {
                    vec![]
                };

                Ok(BiometricStatus {
                    is_available: status.is_available,
                    error: status.error,
                    biometric_types,
                })
            }
            Err(e) => Err(MobileError::internal_error(format!(
                "检查生物识别状态失败: {}",
                e
            ))),
        }
    }

    /// 执行生物识别认证
    ///
    /// # 参数
    /// * `reason` - 认证原因
    ///
    /// # 返回值
    /// 认证成功返回true，失败返回false
    #[cfg(any(target_os = "android", target_os = "ios"))]
    pub async fn authenticate(&self, reason: &str) -> MobileResult<bool> {
        let auth_options = AuthOptions {
            allow_device_credential: self.config.allow_device_credential,
            cancel_title: self.config.cancel_title.clone(),
            fallback_title: self.config.fallback_title.clone(),
            title: self.config.title.clone(),
            subtitle: self.config.subtitle.clone(),
            confirmation_required: self.config.confirmation_required,
        };

        match self
            .app_handle
            .biometric()
            .authenticate(reason.to_string(), auth_options)
        {
            Ok(_) => {
                log::info!("生物识别认证成功");
                Ok(true)
            }
            Err(e) => {
                log::warn!("生物识别认证失败: {}", e);
                // 不将认证失败视为错误，而是返回false
                Ok(false)
            }
        }
    }

    /// 检查生物识别是否可用
    ///
    /// # 返回值
    /// 可用返回true，不可用返回false
    #[cfg(any(target_os = "android", target_os = "ios"))]
    pub async fn is_available(&self) -> MobileResult<bool> {
        let status = self.check_status().await?;
        Ok(status.is_available)
    }

    /// 检查生物识别可用性（兼容旧接口）
    ///
    /// # 返回值
    /// 返回生物识别状态信息
    #[cfg(any(target_os = "android", target_os = "ios"))]
    pub async fn check_availability(&self) -> MobileResult<BiometricStatus> {
        self.check_status().await
    }

    /// 更新配置
    ///
    /// # 参数
    /// * `config` - 新的配置
    pub fn update_config(&mut self, config: BiometricConfig) {
        self.config = config;
        log::info!("生物识别配置已更新");
    }

    /// 获取当前配置
    ///
    /// # 返回值
    /// 返回当前配置的克隆
    pub fn get_config(&self) -> BiometricConfig {
        self.config.clone()
    }

    // 非移动平台的占位符实现
    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    pub fn new(_config: BiometricConfig) -> Self {
        Self { config: _config }
    }

    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    pub fn with_default_config() -> Self {
        Self::new(BiometricConfig::default())
    }

    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    pub async fn check_status(&self) -> MobileResult<BiometricStatus> {
        Ok(BiometricStatus {
            is_available: false,
            error: Some("当前平台不支持生物识别".to_string()),
            biometric_types: vec![],
        })
    }

    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    pub async fn check_availability(&self) -> MobileResult<BiometricStatus> {
        self.check_status().await
    }

    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    pub async fn authenticate(&self, _reason: &str) -> MobileResult<bool> {
        Err(MobileError::unsupported_platform("生物识别认证"))
    }

    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    pub async fn is_available(&self) -> MobileResult<bool> {
        Ok(false)
    }
}

/// 生物识别工厂
pub struct BiometricFactory;

impl BiometricFactory {
    /// 创建生物识别管理器
    ///
    /// # 参数
    /// * `app_handle` - Tauri应用句柄
    /// * `config` - 可选的配置
    ///
    /// # 返回值
    /// 返回生物识别管理器实例
    #[cfg(any(target_os = "android", target_os = "ios"))]
    pub fn create(
        app_handle: tauri::AppHandle,
        config: Option<BiometricConfig>,
    ) -> BiometricManager {
        match config {
            Some(config) => BiometricManager::new(app_handle, config),
            None => BiometricManager::with_default_config(app_handle),
        }
    }

    /// 创建默认配置的生物识别管理器
    ///
    /// # 参数
    /// * `app_handle` - Tauri应用句柄
    ///
    /// # 返回值
    /// 返回生物识别管理器实例
    #[cfg(any(target_os = "android", target_os = "ios"))]
    pub fn create_default(app_handle: tauri::AppHandle) -> BiometricManager {
        BiometricManager::with_default_config(app_handle)
    }

    /// 检查平台是否支持生物识别
    ///
    /// # 返回值
    /// 支持返回true，不支持返回false
    pub fn is_platform_supported() -> bool {
        cfg!(any(target_os = "android", target_os = "ios"))
    }

    /// 为指定平台创建生物识别管理器
    ///
    /// # 参数
    /// * `platform` - 目标平台
    ///
    /// # 返回值
    /// 返回生物识别管理器实例
    #[cfg(any(target_os = "android", target_os = "ios"))]
    pub fn create_for_platform(
        platform: &MobilePlatform,
        app_handle: tauri::AppHandle,
    ) -> BiometricManager {
        match platform {
            MobilePlatform::iOS => {
                log::info!("创建iOS生物识别管理器");
                BiometricManager::with_default_config(app_handle)
            }
            MobilePlatform::Android => {
                log::info!("创建Android生物识别管理器");
                BiometricManager::with_default_config(app_handle)
            }
            MobilePlatform::Unknown => {
                log::info!("创建通用生物识别管理器");
                BiometricManager::with_default_config(app_handle)
            }
        }
    }

    // 非移动平台的占位符实现
    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    pub fn create(config: Option<BiometricConfig>) -> BiometricManager {
        match config {
            Some(config) => BiometricManager::new(config),
            None => BiometricManager::with_default_config(),
        }
    }

    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    pub fn create_default() -> BiometricManager {
        BiometricManager::with_default_config()
    }

    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    pub fn create_for_platform(platform: &MobilePlatform) -> BiometricManager {
        match platform {
            MobilePlatform::iOS => {
                log::info!("创建iOS生物识别管理器");
                BiometricManager::with_default_config()
            }
            MobilePlatform::Android => {
                log::info!("创建Android生物识别管理器");
                BiometricManager::with_default_config()
            }
            MobilePlatform::Unknown => {
                log::info!("创建通用生物识别管理器");
                BiometricManager::with_default_config()
            }
        }
    }

    /// 检查生物识别是否可用
    ///
    /// # 返回值
    /// 返回是否可用
    pub async fn is_available() -> bool {
        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            // 在移动平台上检查实际可用性
            true // TODO: 实现实际检查
        }
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            // 非移动平台不支持生物识别
            false
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_biometric_config_default() {
        let config = BiometricConfig::default();
        assert!(!config.prompt_message.is_empty());
        assert!(config.allow_device_credential);
        assert!(config.cancel_title.is_some());
    }

    #[test]
    fn test_platform_support() {
        let is_supported = BiometricFactory::is_platform_supported();

        #[cfg(any(target_os = "android", target_os = "ios"))]
        assert!(is_supported);

        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        assert!(!is_supported);
    }

    #[tokio::test]
    async fn test_biometric_manager_non_mobile() {
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            let manager = BiometricFactory::create_default();
            let status = manager.check_status().await.unwrap();
            assert!(!status.is_available);
            assert!(status.error.is_some());

            let is_available = manager.is_available().await.unwrap();
            assert!(!is_available);

            let auth_result = manager.authenticate("test").await;
            assert!(auth_result.is_err());
        }
    }

    #[test]
    fn test_config_update() {
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            let mut manager = BiometricFactory::create_default();
            let new_config = BiometricConfig {
                prompt_message: "新的提示信息".to_string(),
                ..Default::default()
            };

            manager.update_config(new_config.clone());
            let current_config = manager.get_config();
            assert_eq!(current_config.prompt_message, "新的提示信息");
        }
    }
}
