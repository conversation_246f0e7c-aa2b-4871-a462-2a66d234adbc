/// 移动端功能管理器
///
/// 统一管理移动端的各种功能，包括安全存储、生物识别等
use crate::mobile::{
    device_info::DeviceInfo,
    errors::MobileResult,
    keychain::{KeychainConfig, KeychainSecureStorageFactory},
    platform::{PlatformDetector, PlatformInfo},
    traits::{PlatformProvider, SecureStorageProvider},
};
use async_trait::async_trait;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 移动端功能管理器
pub struct MobileFeatureManager {
    /// 安全存储提供者
    pub secure_storage: Option<Arc<RwLock<dyn SecureStorageProvider>>>,
    /// 是否已初始化
    initialized: bool,
}

impl MobileFeatureManager {
    /// 创建新的功能管理器
    pub fn new() -> Self {
        Self {
            secure_storage: None,
            initialized: false,
        }
    }

    /// 初始化功能管理器
    ///
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    pub async fn initialize(&mut self) -> MobileResult<()> {
        if self.initialized {
            return Ok(());
        }

        log::info!("初始化移动端功能管理器");

        // 初始化安全存储
        self.initialize_secure_storage().await?;

        self.initialized = true;
        log::info!("移动端功能管理器初始化完成");
        Ok(())
    }

    /// 初始化安全存储
    ///
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    async fn initialize_secure_storage(&mut self) -> MobileResult<()> {
        log::info!("初始化安全存储");

        // 创建 Keychain 安全存储
        let config = KeychainConfig {
            key_prefix: "secure_password_".to_string(),
            debug_logging: true,
        };

        let mut storage = KeychainSecureStorageFactory::create_with_config(config);
        storage.initialize().await?;

        self.secure_storage = Some(Arc::new(RwLock::new(storage)));
        log::info!("安全存储初始化完成");
        Ok(())
    }

    /// 检查是否已初始化
    ///
    /// # 返回值
    /// 已初始化返回true，未初始化返回false
    pub fn is_initialized(&self) -> bool {
        self.initialized
    }

    /// 获取安全存储
    ///
    /// # 返回值
    /// 返回安全存储的引用，如果未初始化返回None
    pub fn get_secure_storage(&self) -> Option<&Arc<RwLock<dyn SecureStorageProvider>>> {
        self.secure_storage.as_ref()
    }

    /// 重新初始化功能管理器
    ///
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    pub async fn reinitialize(&mut self) -> MobileResult<()> {
        log::info!("重新初始化移动端功能管理器");

        // 清除现有状态
        self.secure_storage = None;
        self.initialized = false;

        // 重新初始化
        self.initialize().await
    }

    /// 获取功能状态
    ///
    /// # 返回值
    /// 返回功能状态信息
    pub fn get_status(&self) -> std::collections::HashMap<String, String> {
        let mut status = std::collections::HashMap::new();

        status.insert("initialized".to_string(), self.initialized.to_string());
        status.insert(
            "secure_storage_available".to_string(),
            self.secure_storage.is_some().to_string(),
        );
        status.insert(
            "platform".to_string(),
            PlatformDetector::detect_current_platform().to_string(),
        );

        status
    }

    /// 获取设备信息
    pub async fn get_device_info(&self) -> MobileResult<DeviceInfo> {
        log::info!("获取设备信息");

        // 获取平台信息
        let platform_info = PlatformInfo::current();

        // 创建设备信息
        let device_info = DeviceInfo {
            platform: platform_info.platform.clone(),
            version: platform_info.version.clone(),
            model: "Unknown".to_string(),        // 可以根据需要扩展
            manufacturer: "Unknown".to_string(), // 可以根据需要扩展
            device_id: uuid::Uuid::new_v4().to_string(), // 生成临时设备ID
            arch: std::env::consts::ARCH.to_string(),
            family: std::env::consts::FAMILY.to_string(),
            os_type: std::env::consts::OS.to_string(),
        };

        Ok(device_info)
    }

    /// 获取状态摘要
    pub async fn get_status_summary(&self) -> MobileResult<String> {
        let mut summary = Vec::new();

        summary.push(format!(
            "初始化状态: {}",
            if self.initialized {
                "已初始化"
            } else {
                "未初始化"
            }
        ));

        if let Some(storage) = &self.secure_storage {
            let storage_guard = storage.read().await;
            let is_storage_initialized = storage_guard.is_initialized().await;
            summary.push(format!(
                "安全存储: {}",
                if is_storage_initialized {
                    "可用"
                } else {
                    "不可用"
                }
            ));
        } else {
            summary.push("安全存储: 未配置".to_string());
        }

        // 获取平台信息
        let platform_info = PlatformInfo::current();
        summary.push(format!(
            "平台: {} {}",
            platform_info.platform, platform_info.version
        ));

        Ok(summary.join("\n"))
    }
}

impl Default for MobileFeatureManager {
    fn default() -> Self {
        Self::new()
    }
}

/// PlatformProvider trait 实现
#[async_trait]
impl PlatformProvider for MobileFeatureManager {
    /// 获取设备信息
    async fn get_device_info(&self) -> MobileResult<DeviceInfo> {
        Ok(DeviceInfo::default())
    }

    /// 获取平台信息
    async fn get_platform_info(&self) -> MobileResult<PlatformInfo> {
        Ok(PlatformInfo::current())
    }

    /// 检查功能是否可用
    async fn is_feature_available(&self, feature: &str) -> MobileResult<bool> {
        let platform = PlatformDetector::detect_current_platform();
        Ok(platform.supports_feature(feature))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_feature_manager_creation() {
        let manager = MobileFeatureManager::new();
        assert!(!manager.is_initialized());
        assert!(manager.get_secure_storage().is_none());
    }

    #[tokio::test]
    async fn test_feature_manager_initialization() {
        let mut manager = MobileFeatureManager::new();

        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            let result = manager.initialize().await;
            assert!(result.is_ok());
            assert!(manager.is_initialized());
            assert!(manager.get_secure_storage().is_some());
        }

        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            let result = manager.initialize().await;
            assert!(result.is_err());
            assert!(!manager.is_initialized());
        }
    }
}
