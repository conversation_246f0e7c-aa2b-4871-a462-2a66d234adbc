//! 移动端集成测试
//! 
//! 测试移动端各个模块的集成功能

use crate::mobile::{
    feature_manager::MobileFeatureManager,
    keychain::{KeychainSecureStorageFactory, KeychainConfig},
    traits::SecureStorageProvider,
    platform::{MobilePlatform, PlatformDetector},
    device_info::DeviceInfo,
};

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_mobile_feature_manager_initialization() {
        let mut manager = MobileFeatureManager::new();
        let result = manager.initialize().await;
        
        // 在非移动平台上，初始化应该成功但功能有限
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        assert!(result.is_ok());
        
        // 在移动平台上，初始化应该成功
        #[cfg(any(target_os = "android", target_os = "ios"))]
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_keychain_storage_integration() {
        let mut storage = KeychainSecureStorageFactory::create_default();
        
        // 测试初始化
        let init_result = storage.initialize().await;
        
        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            assert!(init_result.is_ok());
            assert!(storage.is_initialized().await);
            
            // 测试存储和获取
            let store_result = storage.store("test_key", "test_value").await;
            assert!(store_result.is_ok());
            
            let retrieve_result = storage.retrieve("test_key").await;
            assert!(retrieve_result.is_ok());
            
            // 测试删除
            let remove_result = storage.remove("test_key").await;
            assert!(remove_result.is_ok());
        }
        
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            // 在非移动平台上，应该返回错误
            assert!(init_result.is_err());
        }
    }

    #[tokio::test]
    async fn test_feature_manager_with_keychain() {
        let mut manager = MobileFeatureManager::new();
        let init_result = manager.initialize().await;
        
        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            assert!(init_result.is_ok());
            assert!(manager.is_initialized());
            
            // 测试安全存储功能
            if let Some(storage) = &manager.secure_storage {
                let storage_guard = storage.read().await;
                
                let store_result = storage_guard.store("integration_test", "test_data").await;
                assert!(store_result.is_ok());
                
                let retrieve_result = storage_guard.retrieve("integration_test").await;
                assert!(retrieve_result.is_ok());
                
                let remove_result = storage_guard.remove("integration_test").await;
                assert!(remove_result.is_ok());
            }
        }
        
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            // 在非移动平台上，初始化应该成功但功能有限
            assert!(init_result.is_ok());
        }
    }

    #[test]
    fn test_keychain_config() {
        let config = KeychainConfig {
            key_prefix: "test_prefix_".to_string(),
            debug_logging: true,
        };
        
        let storage = KeychainSecureStorageFactory::create_with_config(config.clone());
        assert_eq!(storage.get_config().key_prefix, "test_prefix_");
        assert!(storage.get_config().debug_logging);
    }

    #[test]
    fn test_platform_availability() {
        let is_available = KeychainSecureStorageFactory::is_available();
        
        #[cfg(any(target_os = "android", target_os = "ios"))]
        assert!(is_available);
        
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        assert!(!is_available);
    }

    #[test]
    fn test_platform_detection() {
        let platform = PlatformDetector::detect_current_platform();
        
        #[cfg(target_os = "ios")]
        assert_eq!(platform, MobilePlatform::iOS);
        
        #[cfg(target_os = "android")]
        assert_eq!(platform, MobilePlatform::Android);
        
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        assert_eq!(platform, MobilePlatform::Unknown);
    }

    #[test]
    fn test_device_info() {
        let device_info = DeviceInfo::default();
        assert!(!device_info.version.is_empty());
        assert!(!device_info.model.is_empty());
        assert!(!device_info.manufacturer.is_empty());
    }

    #[tokio::test]
    async fn test_concurrent_access() {
        let mut handles = vec![];
        
        // 创建多个并发任务
        for i in 0..3 {
            let handle = tokio::spawn(async move {
                let platform = PlatformDetector::detect_current_platform();
                assert!(!platform.name().is_empty());
                
                let device_info = DeviceInfo::default();
                assert!(!device_info.version.is_empty());
                
                i
            });
            handles.push(handle);
        }
        
        // 等待所有任务完成
        for handle in handles {
            let result = handle.await.unwrap();
            assert!(result < 3);
        }
    }

    #[test]
    fn test_platform_features() {
        let platform = PlatformDetector::detect_current_platform();
        let features = platform.supported_features();
        
        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            assert!(!features.is_empty());
            assert!(features.contains(&"secure_storage"));
        }
        
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            assert!(features.is_empty());
        }
    }
} 