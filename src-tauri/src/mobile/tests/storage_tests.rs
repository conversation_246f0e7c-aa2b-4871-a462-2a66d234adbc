//! 安全存储功能测试

#[cfg(test)]
mod tests {
    use crate::mobile::{
        traits::SecureStorageProvider,
        keychain::{KeychainSecureStorage, KeychainSecureStorageFactory, KeychainConfig},
        errors::MobileError,
    };

    /// 创建测试用的 keychain 存储实例
    fn create_test_storage() -> KeychainSecureStorage {
        let config = KeychainConfig {
            key_prefix: "test_secure_password_".to_string(),
            debug_logging: true,
        };
        KeychainSecureStorageFactory::create_with_config(config)
    }

    #[tokio::test]
    async fn test_keychain_secure_storage_basic_operations() {
        let mut storage = create_test_storage();
        
        // 初始化
        let init_result = storage.initialize().await;
        assert!(init_result.is_ok());
        
        // 存储数据
        let store_result = storage.store("test_key", "test_value").await;
        assert!(store_result.is_ok());
        
        // 检查存在性
        let exists_result = storage.exists("test_key").await;
        assert!(exists_result.is_ok());
        assert!(exists_result.unwrap());
        
        // 检索数据
        let retrieve_result = storage.retrieve("test_key").await;
        assert!(retrieve_result.is_ok());
        assert_eq!(retrieve_result.unwrap(), Some("test_value".to_string()));
        
        // 删除数据
        let remove_result = storage.remove("test_key").await;
        assert!(remove_result.is_ok());
        assert!(remove_result.unwrap());
        
        // 验证删除
        let exists_after_remove = storage.exists("test_key").await;
        assert!(exists_after_remove.is_ok());
        assert!(!exists_after_remove.unwrap());
    }

    #[tokio::test]
    async fn test_keychain_storage_without_initialization() {
        let mut storage = create_test_storage();
        
        // 测试未初始化时的操作
        let store_result = storage.store("key", "value").await;
        assert!(store_result.is_err());
        assert!(matches!(store_result.unwrap_err(), MobileError::SecureStorageError(_)));
    }

    #[tokio::test]
    async fn test_keychain_storage_multiple_keys() {
        let mut storage = create_test_storage();
        storage.initialize().await.unwrap();
        
        // 存储多个键值对
        let keys_values = vec![
            ("key1", "value1"),
            ("key2", "value2"),
            ("key3", "value3"),
        ];
        
        for (key, value) in &keys_values {
            let result = storage.store(key, value).await;
            assert!(result.is_ok());
        }
        
        // 获取所有键
        let all_keys_result = storage.get_all_keys().await;
        assert!(all_keys_result.is_ok());
        let all_keys = all_keys_result.unwrap();
        
        // 验证所有键都存在
        for (key, _) in &keys_values {
            assert!(all_keys.contains(&key.to_string()));
        }
        
        // 清除所有数据
        let clear_result = storage.clear().await;
        assert!(clear_result.is_ok());
        
        // 验证清除后没有键
        let keys_after_clear = storage.get_all_keys().await.unwrap();
        assert!(keys_after_clear.is_empty());
    }

    #[tokio::test]
    async fn test_keychain_storage_factory_configurations() {
        // 测试默认配置
        let default_storage = KeychainSecureStorageFactory::create_default();
        assert!(!default_storage.is_initialized());
        
        // 测试自定义配置
        let custom_config = KeychainConfig {
            key_prefix: "custom_prefix_".to_string(),
            debug_logging: false,
        };
        let custom_storage = KeychainSecureStorageFactory::create_with_config(custom_config);
        assert!(!custom_storage.is_initialized());
    }

    #[tokio::test]
    async fn test_keychain_storage_edge_cases() {
        let mut storage = create_test_storage();
        storage.initialize().await.unwrap();
        
        // 测试空键
        let empty_key_result = storage.store("", "value").await;
        assert!(empty_key_result.is_ok());
        
        // 测试空值
        let empty_value_result = storage.store("key", "").await;
        assert!(empty_value_result.is_ok());
        
        // 测试特殊字符
        let special_chars_result = storage.store("key_with_特殊字符_123", "value_with_特殊字符").await;
        assert!(special_chars_result.is_ok());
        
        // 测试长键名
        let long_key = "a".repeat(100); // 减少长度以避免潜在问题
        let long_key_result = storage.store(&long_key, "value").await;
        assert!(long_key_result.is_ok());
        
        // 测试长值
        let long_value = "b".repeat(1000); // 减少长度以避免潜在问题
        let long_value_result = storage.store("key", &long_value).await;
        assert!(long_value_result.is_ok());
    }

    #[tokio::test]
    async fn test_keychain_storage_retrieve_nonexistent() {
        let mut storage = create_test_storage();
        storage.initialize().await.unwrap();
        
        // 检索不存在的键
        let result = storage.retrieve("nonexistent_key").await;
        assert!(result.is_ok());
        assert!(result.unwrap().is_none());
        
        // 检查不存在的键
        let exists_result = storage.exists("nonexistent_key").await;
        assert!(exists_result.is_ok());
        assert!(!exists_result.unwrap());
        
        // 删除不存在的键
        let remove_result = storage.remove("nonexistent_key").await;
        assert!(remove_result.is_ok());
        // 删除不存在的键应该返回false
        assert!(!remove_result.unwrap());
    }

    #[tokio::test]
    async fn test_keychain_storage_concurrent_operations() {
        let mut storage = create_test_storage();
        storage.initialize().await.unwrap();
        
        // 并发存储操作
        let mut handles = vec![];
        for i in 0..10 {
            let key = format!("concurrent_key_{}", i);
            let value = format!("concurrent_value_{}", i);
            let storage_clone = storage.clone();
            
            let handle = tokio::spawn(async move {
                storage_clone.store(&key, &value).await
            });
            handles.push(handle);
        }
        
        // 等待所有操作完成
        for handle in handles {
            let result = handle.await.unwrap();
            assert!(result.is_ok());
        }
        
        // 验证所有数据都存储成功
        for i in 0..10 {
            let key = format!("concurrent_key_{}", i);
            let expected_value = format!("concurrent_value_{}", i);
            
            let result = storage.retrieve(&key).await;
            assert!(result.is_ok());
            assert_eq!(result.unwrap(), Some(expected_value));
        }
    }

    #[tokio::test]
    async fn test_keychain_storage_error_scenarios() {
        let mut storage = create_test_storage();
        
        // 测试未初始化的错误场景
        let uninit_result = storage.store("key", "value").await;
        assert!(uninit_result.is_err());
        
        // 初始化后测试正常操作
        storage.initialize().await.unwrap();
        let normal_result = storage.store("key", "value").await;
        assert!(normal_result.is_ok());
    }

    #[tokio::test]
    async fn test_keychain_storage_performance() {
        let mut storage = create_test_storage();
        storage.initialize().await.unwrap();
        
        let start = std::time::Instant::now();
        
        // 执行100次存储操作
        for i in 0..100 {
            let key = format!("perf_key_{}", i);
            let value = format!("perf_value_{}", i);
            storage.store(&key, &value).await.unwrap();
        }
        
        let duration = start.elapsed();
        println!("100次存储操作耗时: {:?}", duration);
        
        // 验证性能合理（这里只是一个基本检查）
        assert!(duration.as_secs() < 10); // 应该在10秒内完成
    }

    #[tokio::test]
    async fn test_keychain_config_variations() {
        // 测试不同的配置选项
        let configs = vec![
            KeychainConfig {
                key_prefix: "prefix1_".to_string(),
                debug_logging: true,
            },
            KeychainConfig {
                key_prefix: "prefix2_".to_string(),
                debug_logging: false,
            },
            KeychainConfig {
                key_prefix: "".to_string(),
                debug_logging: true,
            },
        ];
        
        for config in configs {
            let mut storage = KeychainSecureStorageFactory::create_with_config(config);
            let init_result = storage.initialize().await;
            assert!(init_result.is_ok());
            
            let store_result = storage.store("test_key", "test_value").await;
            assert!(store_result.is_ok());
        }
    }
} 