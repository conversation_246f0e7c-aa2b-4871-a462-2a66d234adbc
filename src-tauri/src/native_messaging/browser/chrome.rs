//! Chrome 浏览器适配器实现
//!
//! 提供针对 Chrome 浏览器的 Native Messaging 支持

use super::{BrowserAdapter, BrowserAdapterConfig, BrowserAdapterStats, ConnectionStatus};
use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::{NativeMessage, OutgoingMessage},
    config::BrowserType,
};
use async_trait::async_trait;
use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime};
use tokio::sync::Mutex;

/// Chrome 浏览器适配器
///
/// 实现 Chrome 特定的 Native Messaging 协议
pub struct ChromeAdapter {
    /// 适配器配置
    config: BrowserAdapterConfig,
    /// 连接状态
    connection_status: Arc<RwLock<ConnectionStatus>>,
    /// 统计信息
    stats: Arc<Mutex<BrowserAdapterStats>>,
    /// 是否已初始化
    initialized: Arc<RwLock<bool>>,
}

impl ChromeAdapter {
    /// 创建新的 Chrome 适配器
    ///
    /// # 参数
    /// - `config`: 适配器配置
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new(config: BrowserAdapterConfig) -> Result<Self> {
        let mut chrome_config = config;
        chrome_config.browser_type = BrowserType::Chrome;
        if chrome_config.browser_version == "unknown" {
            chrome_config.browser_version = Self::detect_chrome_version();
        }
        
        Ok(Self {
            config: chrome_config,
            connection_status: Arc::new(RwLock::new(ConnectionStatus::Disconnected)),
            stats: Arc::new(Mutex::new(BrowserAdapterStats::default())),
            initialized: Arc::new(RwLock::new(false)),
        })
    }

    /// 创建默认的 Chrome 适配器
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new_default() -> Result<Self> {
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Chrome,
            browser_version: Self::detect_chrome_version(),
            ..Default::default()
        };
        Self::new(config)
    }

    /// 检测 Chrome 版本
    ///
    /// # 返回
    /// String - Chrome 版本信息
    fn detect_chrome_version() -> String {
        // 在实际实现中，这里会读取 Chrome 的版本信息
        // 这里返回一个模拟版本
        "Chrome/120.0.0.0".to_string()
    }

    /// 验证消息格式是否符合 Chrome 规范
    ///
    /// # 参数
    /// - `message`: 要验证的消息
    ///
    /// # 返回
    /// Result<()> - 验证结果
    fn validate_chrome_message(&self, message: &OutgoingMessage) -> Result<()> {
        // Chrome Native Messaging 格式验证
        if message.message.payload.is_null() {
            return Err(NativeMessagingError::ProtocolError(
                "Chrome 消息数据不能为空".to_string(),
            ));
        }

        // 检查消息大小限制 (Chrome 限制为 1MB)
        let message_str = serde_json::to_string(&message.message.payload)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;
        
        if message_str.len() > 1024 * 1024 {
            return Err(NativeMessagingError::ProtocolError(
                "Chrome 消息大小超过 1MB 限制".to_string(),
            ));
        }

        Ok(())
    }

    /// 设置连接状态
    ///
    /// # 参数
    /// - `status`: 新的连接状态
    fn set_connection_status(&self, status: ConnectionStatus) {
        if let Ok(mut current_status) = self.connection_status.write() {
            *current_status = status;
        }
    }

    /// 获取连接状态
    ///
    /// # 返回
    /// ConnectionStatus - 当前连接状态
    fn get_connection_status(&self) -> ConnectionStatus {
        self.connection_status
            .read()
            .map(|status| status.clone())
            .unwrap_or(ConnectionStatus::Failed("无法读取状态".to_string()))
    }
}

#[async_trait]
impl BrowserAdapter for ChromeAdapter {
    fn browser_type(&self) -> BrowserType {
        BrowserType::Chrome
    }

    fn browser_version(&self) -> &str {
        &self.config.browser_version
    }

    async fn send_message(&self, message: OutgoingMessage) -> Result<()> {
        let start_time = SystemTime::now();
        
        // 验证消息格式
        self.validate_chrome_message(&message)?;

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_send(false);
            return Err(NativeMessagingError::ConnectionError(
                "Chrome 连接未建立".to_string(),
            ));
        }

        // 模拟发送消息到 Chrome
        // 在实际实现中，这里会通过 stdout 发送消息
        tokio::time::sleep(Duration::from_millis(10)).await;

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_send(true);
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        Ok(())
    }

    async fn receive_message(&self) -> Result<NativeMessage> {
        let start_time = SystemTime::now();

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_receive(false);
            return Err(NativeMessagingError::ConnectionError(
                "Chrome 连接未建立".to_string(),
            ));
        }

        // 模拟从 Chrome 接收消息
        // 在实际实现中，这里会从 stdin 读取消息
        tokio::time::sleep(Duration::from_millis(5)).await;
        
        let message = NativeMessage::new(
            crate::native_messaging::protocol::message::MessageType::HealthCheck,
            "chrome-test".to_string(),
            serde_json::json!({"source": "chrome"}),
            "chrome-extension".to_string(),
        );

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_receive(true);
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        Ok(message)
    }

    async fn is_connected(&self) -> bool {
        matches!(self.get_connection_status(), ConnectionStatus::Connected)
    }

    async fn initialize(&mut self) -> Result<()> {
        // 检查是否已经初始化
        if let Ok(initialized) = self.initialized.read() {
            if *initialized {
                return Ok(());
            }
        }

        self.set_connection_status(ConnectionStatus::Connecting);

        // 模拟 Chrome 初始化过程
        tokio::time::sleep(Duration::from_millis(100)).await;

        // 更新统计信息
        {
            let mut stats = self.stats.lock().await;
            stats.connection_time = Some(SystemTime::now());
        }

        // 设置已初始化状态
        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = true;
        }

        self.set_connection_status(ConnectionStatus::Connected);
        
        Ok(())
    }

    async fn close(&mut self) -> Result<()> {
        self.set_connection_status(ConnectionStatus::Disconnected);
        
        // 重置初始化状态
        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = false;
        }

        Ok(())
    }

    fn supported_message_types(&self) -> Vec<String> {
        vec![
            "HealthCheck".to_string(),
            "Ping".to_string(),
            "Auth".to_string(),
            "Password".to_string(),
            "Version".to_string(),
            "Test".to_string(),
        ]
    }

    fn config(&self) -> &BrowserAdapterConfig {
        &self.config
    }
}

impl Default for ChromeAdapter {
    fn default() -> Self {
        Self::new_default().expect("创建默认 Chrome 适配器失败")
    }
}

#[cfg(test)]
mod tests {
    use super::*;


    #[test]
    fn test_chrome_adapter_creation() {
        let config = BrowserAdapterConfig::default();
        let adapter = ChromeAdapter::new(config);
        assert!(adapter.is_ok());
        
        let adapter = adapter.unwrap();
        assert_eq!(adapter.browser_type(), BrowserType::Chrome);
        assert!(adapter.browser_version().contains("Chrome"));
    }

    #[test]
    fn test_chrome_adapter_default() {
        let adapter = ChromeAdapter::default();
        assert_eq!(adapter.browser_type(), BrowserType::Chrome);
        assert_eq!(adapter.supported_message_types().len(), 6);
    }

    #[tokio::test]
    async fn test_chrome_adapter_initialization() {
        let mut adapter = ChromeAdapter::default();
        
        // 初始状态应该是未连接
        assert!(!adapter.is_connected().await);
        
        // 初始化
        let result = adapter.initialize().await;
        assert!(result.is_ok());
        
        // 初始化后应该已连接
        assert!(adapter.is_connected().await);
    }

    #[tokio::test]
    async fn test_chrome_adapter_send_message() {
        let mut adapter = ChromeAdapter::default();
        adapter.initialize().await.unwrap();

        let message = OutgoingMessage::success(
            "test-request".to_string(),
            serde_json::json!({"test": "data"}),
        );

        let result = adapter.send_message(message).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_chrome_adapter_receive_message() {
        let mut adapter = ChromeAdapter::default();
        adapter.initialize().await.unwrap();

        let result = adapter.receive_message().await;
        assert!(result.is_ok());
        
        let message = result.unwrap();
        assert_eq!(message.source, "chrome-extension");
    }

    #[tokio::test]
    async fn test_chrome_adapter_close() {
        let mut adapter = ChromeAdapter::default();
        adapter.initialize().await.unwrap();
        assert!(adapter.is_connected().await);

        let result = adapter.close().await;
        assert!(result.is_ok());
        assert!(!adapter.is_connected().await);
    }

    #[test]
    fn test_chrome_message_validation() {
        let adapter = ChromeAdapter::default();
        
        // 测试正常消息
        let valid_message = OutgoingMessage::success(
            "test".to_string(),
            serde_json::json!({"data": "test"}),
        );
        assert!(adapter.validate_chrome_message(&valid_message).is_ok());
        
        // 测试空消息
        let empty_message = OutgoingMessage::success(
            "test".to_string(),
            serde_json::Value::Null,
        );
        assert!(adapter.validate_chrome_message(&empty_message).is_err());
    }

    #[test]
    fn test_chrome_version_detection() {
        let version = ChromeAdapter::detect_chrome_version();
        assert!(version.contains("Chrome"));
        assert!(!version.is_empty());
    }
} 