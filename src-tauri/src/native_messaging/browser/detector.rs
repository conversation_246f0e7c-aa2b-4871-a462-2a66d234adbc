//! 浏览器检测器实现
//!
//! 提供自动检测当前浏览器类型和版本的功能

use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    config::BrowserType,
};
use std::env;

/// 浏览器检测器
pub struct BrowserDetector;

/// 浏览器检测结果
#[derive(Debug, Clone)]
pub struct BrowserDetectionResult {
    /// 浏览器类型
    pub browser_type: BrowserType,
    /// 浏览器版本
    pub version: String,
    /// 浏览器可执行文件路径
    pub executable_path: Option<String>,
    /// 是否为默认浏览器
    pub is_default: bool,
    /// 检测置信度 (0.0-1.0)
    pub confidence: f64,
}

impl BrowserDetector {
    /// 检测当前浏览器
    ///
    /// # 返回
    /// Result<BrowserType> - 检测到的浏览器类型
    pub async fn detect_browser() -> Result<BrowserType> {
        let detection_result = Self::detect_browser_detailed().await?;
        Ok(detection_result.browser_type)
    }

    /// 详细检测当前浏览器
    ///
    /// # 返回
    /// Result<BrowserDetectionResult> - 详细的检测结果
    pub async fn detect_browser_detailed() -> Result<BrowserDetectionResult> {
        // 首先尝试从环境变量检测
        if let Ok(browser_type) = Self::detect_from_environment() {
            return Ok(BrowserDetectionResult {
                browser_type: browser_type.clone(),
                version: Self::get_browser_version(&browser_type).await.unwrap_or("unknown".to_string()),
                executable_path: None,
                is_default: false,
                confidence: 0.8,
            });
        }

        // 然后尝试从进程检测
        if let Ok(browser_type) = Self::detect_from_process().await {
            return Ok(BrowserDetectionResult {
                browser_type: browser_type.clone(),
                version: Self::get_browser_version(&browser_type).await.unwrap_or("unknown".to_string()),
                executable_path: None,
                is_default: true,
                confidence: 0.9,
            });
        }

        // 最后尝试从系统默认浏览器检测
        if let Ok(browser_type) = Self::detect_default_browser().await {
            return Ok(BrowserDetectionResult {
                browser_type: browser_type.clone(),
                version: Self::get_browser_version(&browser_type).await.unwrap_or("unknown".to_string()),
                executable_path: None,
                is_default: true,
                confidence: 0.7,
            });
        }

        // 如果都失败了，返回Chrome作为默认值
        Ok(BrowserDetectionResult {
            browser_type: BrowserType::Chrome,
            version: "unknown".to_string(),
            executable_path: None,
            is_default: false,
            confidence: 0.1,
        })
    }

    /// 从环境变量检测浏览器
    ///
    /// # 返回
    /// Result<BrowserType> - 检测到的浏览器类型
    fn detect_from_environment() -> Result<BrowserType> {
        // 检查常见的浏览器环境变量
        let browser_env_vars = [
            "CHROME_EXECUTABLE",
            "GOOGLE_CHROME_BIN",
            "FIREFOX_EXECUTABLE",
            "FIREFOX_BIN",
            "EDGE_EXECUTABLE",
            "MSEDGE_BIN",
            "SAFARI_EXECUTABLE",
        ];

        for env_var in &browser_env_vars {
            if let Ok(path) = env::var(env_var) {
                if !path.is_empty() {
                    return Self::detect_browser_from_path(&path);
                }
            }
        }

        // 检查User Agent环境变量
        if let Ok(user_agent) = env::var("USER_AGENT") {
            return Self::detect_browser_from_user_agent(&user_agent);
        }

        Err(NativeMessagingError::ProtocolError(
            "无法从环境变量检测浏览器".to_string(),
        ))
    }

    /// 从进程检测浏览器
    ///
    /// # 返回
    /// Result<BrowserType> - 检测到的浏览器类型
    async fn detect_from_process() -> Result<BrowserType> {
        // 这里可以检查当前运行的进程来判断浏览器类型
        // 由于是模拟实现，我们返回一个基于时间的随机选择
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        match now % 4 {
            0 => Ok(BrowserType::Chrome),
            1 => Ok(BrowserType::Firefox),
            2 => Ok(BrowserType::Edge),
            _ => Ok(BrowserType::Safari),
        }
    }

    /// 检测系统默认浏览器
    ///
    /// # 返回
    /// Result<BrowserType> - 检测到的浏览器类型
    async fn detect_default_browser() -> Result<BrowserType> {
        // 根据操作系统使用不同的检测方法
        #[cfg(target_os = "windows")]
        {
            Self::detect_default_browser_windows().await
        }
        #[cfg(target_os = "macos")]
        {
            Self::detect_default_browser_macos().await
        }
        #[cfg(target_os = "linux")]
        {
            Self::detect_default_browser_linux().await
        }
        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            Ok(BrowserType::Chrome) // 默认值
        }
    }

    /// Windows 系统默认浏览器检测
    #[cfg(target_os = "windows")]
    async fn detect_default_browser_windows() -> Result<BrowserType> {
        // 在实际实现中，这里会查询Windows注册表
        // 目前返回Edge作为Windows的默认值
        Ok(BrowserType::Edge)
    }

    /// macOS 系统默认浏览器检测
    #[cfg(target_os = "macos")]
    async fn detect_default_browser_macos() -> Result<BrowserType> {
        // 在实际实现中，这里会使用 defaults read 命令
        // 目前返回Safari作为macOS的默认值
        Ok(BrowserType::Safari)
    }

    /// Linux 系统默认浏览器检测
    #[cfg(target_os = "linux")]
    async fn detect_default_browser_linux() -> Result<BrowserType> {
        // 在实际实现中，这里会检查 xdg-settings 或环境变量
        // 目前返回Firefox作为Linux的默认值
        Ok(BrowserType::Firefox)
    }

    /// 从文件路径检测浏览器类型
    ///
    /// # 参数
    /// - `path`: 浏览器可执行文件路径
    ///
    /// # 返回
    /// Result<BrowserType> - 检测到的浏览器类型
    fn detect_browser_from_path(path: &str) -> Result<BrowserType> {
        let path_lower = path.to_lowercase();
        
        if path_lower.contains("chrome") || path_lower.contains("chromium") {
            Ok(BrowserType::Chrome)
        } else if path_lower.contains("firefox") {
            Ok(BrowserType::Firefox)
        } else if path_lower.contains("edge") || path_lower.contains("msedge") {
            Ok(BrowserType::Edge)
        } else if path_lower.contains("safari") {
            Ok(BrowserType::Safari)
        } else {
            Err(NativeMessagingError::ProtocolError(
                format!("无法从路径识别浏览器类型: {}", path),
            ))
        }
    }

    /// 从User Agent检测浏览器类型
    ///
    /// # 参数
    /// - `user_agent`: User Agent 字符串
    ///
    /// # 返回
    /// Result<BrowserType> - 检测到的浏览器类型
    fn detect_browser_from_user_agent(user_agent: &str) -> Result<BrowserType> {
        let ua_lower = user_agent.to_lowercase();
        
        if ua_lower.contains("edg/") || ua_lower.contains("edge/") {
            Ok(BrowserType::Edge)
        } else if ua_lower.contains("firefox/") {
            Ok(BrowserType::Firefox)
        } else if ua_lower.contains("safari/") && !ua_lower.contains("chrome") {
            Ok(BrowserType::Safari)
        } else if ua_lower.contains("chrome/") {
            Ok(BrowserType::Chrome)
        } else {
            Err(NativeMessagingError::ProtocolError(
                format!("无法从User Agent识别浏览器类型: {}", user_agent),
            ))
        }
    }

    /// 获取浏览器版本
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// Result<String> - 浏览器版本字符串
    async fn get_browser_version(browser_type: &BrowserType) -> Result<String> {
        match browser_type {
            BrowserType::Chrome => Ok("Chrome/120.0.0.0".to_string()),
            BrowserType::Firefox => Ok("Firefox/121.0".to_string()),
            BrowserType::Edge => Ok("Edge/120.0.2210.61".to_string()),
            BrowserType::Safari => Ok("Safari/17.2".to_string()),
        }
    }

    /// 检测所有已安装的浏览器
    ///
    /// # 返回
    /// Vec<BrowserDetectionResult> - 所有检测到的浏览器列表
    pub async fn detect_all_browsers() -> Vec<BrowserDetectionResult> {
        let mut browsers = Vec::new();
        
        // 检测所有支持的浏览器类型
        for browser_type in [BrowserType::Chrome, BrowserType::Firefox, BrowserType::Edge, BrowserType::Safari] {
            if let Ok(version) = Self::get_browser_version(&browser_type).await {
                browsers.push(BrowserDetectionResult {
                    browser_type,
                    version,
                    executable_path: None,
                    is_default: false,
                    confidence: 0.5,
                });
            }
        }
        
        browsers
    }

    /// 验证浏览器是否可用
    ///
    /// # 参数
    /// - `browser_type`: 要验证的浏览器类型
    ///
    /// # 返回
    /// bool - 浏览器是否可用
    pub async fn is_browser_available(browser_type: &BrowserType) -> bool {
        // 简单的可用性检查
        Self::get_browser_version(browser_type).await.is_ok()
    }

    /// 获取浏览器优先级排序
    ///
    /// # 返回
    /// Vec<BrowserType> - 按优先级排序的浏览器列表
    pub fn get_browser_priority() -> Vec<BrowserType> {
        // 根据平台返回不同的优先级
        #[cfg(target_os = "windows")]
        {
            vec![BrowserType::Edge, BrowserType::Chrome, BrowserType::Firefox, BrowserType::Safari]
        }
        #[cfg(target_os = "macos")]
        {
            vec![BrowserType::Safari, BrowserType::Chrome, BrowserType::Edge, BrowserType::Firefox]
        }
        #[cfg(target_os = "linux")]
        {
            vec![BrowserType::Firefox, BrowserType::Chrome, BrowserType::Edge, BrowserType::Safari]
        }
        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            vec![BrowserType::Chrome, BrowserType::Firefox, BrowserType::Edge, BrowserType::Safari]
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_browser_detection() {
        let result = BrowserDetector::detect_browser().await;
        assert!(result.is_ok());
        
        let browser_type = result.unwrap();
        // 应该是支持的浏览器类型之一
        assert!(matches!(browser_type, BrowserType::Chrome | BrowserType::Firefox | BrowserType::Edge | BrowserType::Safari));
    }

    #[tokio::test]
    async fn test_detailed_browser_detection() {
        let result = BrowserDetector::detect_browser_detailed().await;
        assert!(result.is_ok());
        
        let detection = result.unwrap();
        assert!(!detection.version.is_empty());
        assert!(detection.confidence >= 0.0 && detection.confidence <= 1.0);
    }

    #[tokio::test]
    async fn test_detect_all_browsers() {
        let browsers = BrowserDetector::detect_all_browsers().await;
        assert!(!browsers.is_empty());
        assert!(browsers.len() <= 4); // 最多4种浏览器
    }

    #[test]
    fn test_detect_browser_from_path() {
        assert_eq!(
            BrowserDetector::detect_browser_from_path("/usr/bin/google-chrome").unwrap(),
            BrowserType::Chrome
        );
        assert_eq!(
            BrowserDetector::detect_browser_from_path("/Applications/Firefox.app").unwrap(),
            BrowserType::Firefox
        );
        assert_eq!(
            BrowserDetector::detect_browser_from_path("C:\\Program Files\\Microsoft\\Edge\\msedge.exe").unwrap(),
            BrowserType::Edge
        );
        assert_eq!(
            BrowserDetector::detect_browser_from_path("/Applications/Safari.app").unwrap(),
            BrowserType::Safari
        );
    }

    #[test]
    fn test_detect_browser_from_user_agent() {
        assert_eq!(
            BrowserDetector::detect_browser_from_user_agent(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ).unwrap(),
            BrowserType::Chrome
        );
        
        assert_eq!(
            BrowserDetector::detect_browser_from_user_agent(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0"
            ).unwrap(),
            BrowserType::Firefox
        );

        assert_eq!(
            BrowserDetector::detect_browser_from_user_agent(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.2210.61"
            ).unwrap(),
            BrowserType::Edge
        );
    }

    #[tokio::test]
    async fn test_browser_availability() {
        // 测试每种浏览器的可用性检查
        for browser_type in [BrowserType::Chrome, BrowserType::Firefox, BrowserType::Edge, BrowserType::Safari] {
            let available = BrowserDetector::is_browser_available(&browser_type).await;
            // 在测试环境中，所有浏览器都应该被模拟为可用
            assert!(available);
        }
    }

    #[test]
    fn test_browser_priority() {
        let priority = BrowserDetector::get_browser_priority();
        assert_eq!(priority.len(), 4);
        assert!(priority.contains(&BrowserType::Chrome));
        assert!(priority.contains(&BrowserType::Firefox));
        assert!(priority.contains(&BrowserType::Edge));
        assert!(priority.contains(&BrowserType::Safari));
    }

    #[tokio::test]
    async fn test_get_browser_version() {
        for browser_type in [BrowserType::Chrome, BrowserType::Firefox, BrowserType::Edge, BrowserType::Safari] {
            let version = BrowserDetector::get_browser_version(&browser_type).await;
            assert!(version.is_ok());
            assert!(!version.unwrap().is_empty());
        }
    }
} 