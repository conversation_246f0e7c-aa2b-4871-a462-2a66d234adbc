//! Edge 浏览器适配器实现
//!
//! 提供针对 Microsoft Edge 浏览器的 Native Messaging 支持

use super::{BrowserAdapter, BrowserAdapterConfig, BrowserAdapterStats, ConnectionStatus};
use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::{NativeMessage, OutgoingMessage},
    config::BrowserType,
};
use async_trait::async_trait;
use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime};
use tokio::sync::Mutex;

/// Edge 浏览器适配器
///
/// 实现 Microsoft Edge 特定的 Native Messaging 协议
/// 基于 Chromium 内核，但有微软特定的扩展和安全增强
pub struct EdgeAdapter {
    /// 适配器配置
    config: BrowserAdapterConfig,
    /// 连接状态
    connection_status: Arc<RwLock<ConnectionStatus>>,
    /// 统计信息
    stats: Arc<Mutex<BrowserAdapterStats>>,
    /// 是否已初始化
    initialized: Arc<RwLock<bool>>,
}

impl EdgeAdapter {
    /// 创建新的 Edge 适配器
    ///
    /// # 参数
    /// - `config`: 适配器配置
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new(config: BrowserAdapterConfig) -> Result<Self> {
        let mut edge_config = config;
        edge_config.browser_type = BrowserType::Edge;
        if edge_config.browser_version == "unknown" {
            edge_config.browser_version = Self::detect_edge_version();
        }
        
        Ok(Self {
            config: edge_config,
            connection_status: Arc::new(RwLock::new(ConnectionStatus::Disconnected)),
            stats: Arc::new(Mutex::new(BrowserAdapterStats::default())),
            initialized: Arc::new(RwLock::new(false)),
        })
    }

    /// 创建默认的 Edge 适配器
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new_default() -> Result<Self> {
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Edge,
            browser_version: Self::detect_edge_version(),
            // Edge 需要更严格的安全设置
            connection_timeout: 6000,
            message_timeout: 12000,
            max_retries: 2, // Edge 重试次数较少
            debug_mode: false,
            ..Default::default()
        };
        Self::new(config)
    }

    /// 检测 Edge 版本
    ///
    /// # 返回
    /// String - Edge 版本信息
    fn detect_edge_version() -> String {
        // 在实际实现中，这里会读取 Edge 的版本信息
        // Edge 版本格式通常为 Edge/120.0.2210.61
        "Edge/120.0.2210.61".to_string()
    }

    /// 验证消息格式是否符合 Edge 规范
    ///
    /// # 参数
    /// - `message`: 要验证的消息
    ///
    /// # 返回
    /// Result<()> - 验证结果
    fn validate_edge_message(&self, message: &OutgoingMessage) -> Result<()> {
        // Edge Native Messaging 格式验证（基于 Chromium 但有额外限制）
        if message.message.payload.is_null() {
            return Err(NativeMessagingError::ProtocolError(
                "Edge 消息数据不能为空".to_string(),
            ));
        }

        // Edge 的消息大小限制（比Chrome更严格，512KB）
        let message_str = serde_json::to_string(&message.message.payload)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;
        
        if message_str.len() > 512 * 1024 {
            return Err(NativeMessagingError::ProtocolError(
                "Edge 消息大小超过 512KB 限制".to_string(),
            ));
        }

        // Edge 要求消息包含Microsoft安全头
        if message.message.source.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "Edge 消息必须包含有效的源标识".to_string(),
            ));
        }

        Ok(())
    }

    /// 转换消息格式为 Edge 兼容格式
    ///
    /// # 参数
    /// - `message`: 原始消息
    ///
    /// # 返回
    /// Result<OutgoingMessage> - 转换后的消息
    fn convert_to_edge_format(&self, mut message: OutgoingMessage) -> Result<OutgoingMessage> {
        // Edge 需要在消息中添加微软特定的metadata
        if let Some(obj) = message.message.payload.as_object_mut() {
            obj.insert("browser".to_string(), serde_json::Value::String("edge".to_string()));
            obj.insert("microsoft_security_token".to_string(), serde_json::Value::String("edge-token-v1".to_string()));
            obj.insert("protocol_version".to_string(), serde_json::Value::String("1.0".to_string()));
        }

        Ok(message)
    }

    /// 设置连接状态
    ///
    /// # 参数
    /// - `status`: 新的连接状态
    fn set_connection_status(&self, status: ConnectionStatus) {
        if let Ok(mut current_status) = self.connection_status.write() {
            *current_status = status;
        }
    }

    /// 获取连接状态
    ///
    /// # 返回
    /// ConnectionStatus - 当前连接状态
    fn get_connection_status(&self) -> ConnectionStatus {
        self.connection_status
            .read()
            .map(|status| status.clone())
            .unwrap_or(ConnectionStatus::Failed("无法读取状态".to_string()))
    }
}

#[async_trait]
impl BrowserAdapter for EdgeAdapter {
    fn browser_type(&self) -> BrowserType {
        BrowserType::Edge
    }

    fn browser_version(&self) -> &str {
        &self.config.browser_version
    }

    async fn send_message(&self, message: OutgoingMessage) -> Result<()> {
        let start_time = SystemTime::now();
        
        // 验证消息格式
        self.validate_edge_message(&message)?;

        // 转换为 Edge 格式
        let _edge_message = self.convert_to_edge_format(message)?;

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_send(false);
            return Err(NativeMessagingError::ConnectionError(
                "Edge 连接未建立".to_string(),
            ));
        }

        // 模拟发送消息到 Edge (比Chrome稍慢，因为有额外的安全检查)
        // 在实际实现中，这里会通过 stdout 发送消息
        tokio::time::sleep(Duration::from_millis(12)).await;

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_send(true);
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        Ok(())
    }

    async fn receive_message(&self) -> Result<NativeMessage> {
        let start_time = SystemTime::now();

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_receive(false);
            return Err(NativeMessagingError::ConnectionError(
                "Edge 连接未建立".to_string(),
            ));
        }

        // 模拟从 Edge 接收消息 (包含微软安全验证时间)
        // 在实际实现中，这里会从 stdin 读取消息
        tokio::time::sleep(Duration::from_millis(7)).await;
        
        let message = NativeMessage::new(
            crate::native_messaging::protocol::message::MessageType::HealthCheck,
            "edge-test".to_string(),
            serde_json::json!({
                "source": "edge",
                "microsoft_security_token": "edge-token-v1",
                "edge_version": self.browser_version()
            }),
            "edge-extension".to_string(),
        );

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_receive(true);
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        Ok(message)
    }

    async fn is_connected(&self) -> bool {
        matches!(self.get_connection_status(), ConnectionStatus::Connected)
    }

    async fn initialize(&mut self) -> Result<()> {
        if let Ok(initialized) = self.initialized.read() {
            if *initialized {
                return Ok(());
            }
        }

        self.set_connection_status(ConnectionStatus::Connecting);
        
        // Edge 初始化包含微软安全验证
        tokio::time::sleep(Duration::from_millis(120)).await;

        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = true;
        }

        self.set_connection_status(ConnectionStatus::Connected);
        Ok(())
    }

    async fn close(&mut self) -> Result<()> {
        self.set_connection_status(ConnectionStatus::Disconnected);
        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = false;
        }
        Ok(())
    }

    fn supported_message_types(&self) -> Vec<String> {
        vec![
            "HealthCheck".to_string(),
            "Ping".to_string(),
            "Auth".to_string(),
            "Password".to_string(),
            "Version".to_string(),
            "Test".to_string(),
            // Edge 特定的消息类型
            "EdgeSecurity".to_string(),
        ]
    }

    fn config(&self) -> &BrowserAdapterConfig {
        &self.config
    }
}

impl Default for EdgeAdapter {
    fn default() -> Self {
        Self::new_default().expect("创建默认 Edge 适配器失败")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;

    #[test]
    fn test_edge_adapter_creation() {
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Edge,
            browser_version: "Edge/120.0.2210.61".to_string(),
            ..Default::default()
        };
        let adapter = EdgeAdapter::new(config).unwrap();
        assert_eq!(adapter.browser_type(), BrowserType::Edge);
        assert_eq!(adapter.browser_version(), "Edge/120.0.2210.61");
    }

    #[test]
    fn test_edge_adapter_default() {
        let adapter = EdgeAdapter::default();
        assert_eq!(adapter.browser_type(), BrowserType::Edge);
        assert!(adapter.browser_version().starts_with("Edge/"));
    }

    #[tokio::test]
    async fn test_edge_adapter_initialization() {
        let mut adapter = EdgeAdapter::default();
        assert!(!adapter.is_connected().await);
        
        let result = adapter.initialize().await;
        assert!(result.is_ok());
        assert!(adapter.is_connected().await);
    }

    #[tokio::test]
    async fn test_edge_adapter_send_message() {
        let mut adapter = EdgeAdapter::default();
        adapter.initialize().await.unwrap();

        let message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"test": "data"}),
                "test-source".to_string(),
            ),
        );

        let result = adapter.send_message(message).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_edge_adapter_receive_message() {
        let mut adapter = EdgeAdapter::default();
        adapter.initialize().await.unwrap();

        let result = adapter.receive_message().await;
        assert!(result.is_ok());
        
        let message = result.unwrap();
        assert!(message.payload.get("microsoft_security_token").is_some());
    }

    #[tokio::test]
    async fn test_edge_adapter_close() {
        let mut adapter = EdgeAdapter::default();
        adapter.initialize().await.unwrap();
        assert!(adapter.is_connected().await);

        let result = adapter.close().await;
        assert!(result.is_ok());
        assert!(!adapter.is_connected().await);
    }

    #[test]
    fn test_edge_message_validation() {
        let adapter = EdgeAdapter::default();
        
        // 测试有效消息
        let valid_message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"test": "data"}),
                "test-source".to_string(),
            ),
        );
        assert!(adapter.validate_edge_message(&valid_message).is_ok());

        // 测试空数据消息
        let invalid_message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::Value::Null,
                "test-source".to_string(),
            ),
        );
        assert!(adapter.validate_edge_message(&invalid_message).is_err());

        // 测试空源消息
        let invalid_source_message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"test": "data"}),
                "".to_string(),
            ),
        );
        assert!(adapter.validate_edge_message(&invalid_source_message).is_err());
    }

    #[test]
    fn test_edge_message_format_conversion() {
        let adapter = EdgeAdapter::default();
        let message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"original": "data"}),
                "test-source".to_string(),
            ),
        );

        let result = adapter.convert_to_edge_format(message);
        assert!(result.is_ok());
        
        let converted = result.unwrap();
        assert!(converted.message.payload.get("browser").is_some());
        assert!(converted.message.payload.get("microsoft_security_token").is_some());
        assert_eq!(converted.message.payload["browser"], "edge");
    }

    #[test]
    fn test_edge_version_detection() {
        let version = EdgeAdapter::detect_edge_version();
        assert!(version.starts_with("Edge/"));
        assert!(version.contains("."));
    }

    #[test]
    fn test_edge_config_differences() {
        let edge_config = EdgeAdapter::new_default().unwrap();
        
        // Edge 特定的配置值
        assert_eq!(edge_config.config.connection_timeout, 6000);
        assert_eq!(edge_config.config.message_timeout, 12000);
        assert_eq!(edge_config.config.max_retries, 2);
        
        // 支持的消息类型包含Edge特定类型
        let supported = edge_config.supported_message_types();
        assert!(supported.contains(&"EdgeSecurity".to_string()));
    }
} 