//! 浏览器管理器实现
//!
//! 提供统一的浏览器适配器管理和高级功能

use super::{
    BrowserAdapter, BrowserAdapterConfig, BrowserAdapterFactory, BrowserDetector
};
use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::{NativeMessage, OutgoingMessage},
    config::BrowserType,
};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};

/// 浏览器管理器
///
/// 统一管理多个浏览器适配器，提供自动检测、负载均衡和故障转移功能
pub struct BrowserManager {
    /// 浏览器适配器集合
    adapters: Arc<RwLock<HashMap<BrowserType, Box<dyn BrowserAdapter>>>>,
    /// 当前活跃的适配器
    active_adapter: Arc<RwLock<Option<BrowserType>>>,
    /// 管理器配置
    config: BrowserManagerConfig,
    /// 管理器统计信息
    stats: Arc<Mutex<BrowserManagerStats>>,
}

/// 浏览器管理器配置
#[derive(Debug, Clone)]
pub struct BrowserManagerConfig {
    /// 是否启用自动检测
    pub auto_detection: bool,
    /// 是否启用故障转移
    pub enable_failover: bool,
    /// 故障转移超时时间（毫秒）
    pub failover_timeout: u64,
    /// 最大重试次数
    pub max_retries: u32,
    /// 健康检查间隔（毫秒）
    pub health_check_interval: u64,
    /// 是否启用负载均衡
    pub enable_load_balancing: bool,
}

impl Default for BrowserManagerConfig {
    fn default() -> Self {
        Self {
            auto_detection: true,
            enable_failover: true,
            failover_timeout: 5000,
            max_retries: 3,
            health_check_interval: 30000,
            enable_load_balancing: false,
        }
    }
}

/// 浏览器管理器统计信息
#[derive(Debug, Clone, Default)]
pub struct BrowserManagerStats {
    /// 总消息处理数
    pub total_messages: u64,
    /// 成功处理数
    pub successful_messages: u64,
    /// 失败处理数
    pub failed_messages: u64,
    /// 故障转移次数
    pub failover_count: u64,
    /// 活跃适配器数量
    pub active_adapters: usize,
    /// 最后健康检查时间
    pub last_health_check: Option<std::time::SystemTime>,
}

impl BrowserManager {
    /// 创建新的浏览器管理器
    ///
    /// # 参数
    /// - `config`: 管理器配置
    ///
    /// # 返回
    /// Self - 创建的管理器
    pub fn new(config: BrowserManagerConfig) -> Self {
        Self {
            adapters: Arc::new(RwLock::new(HashMap::new())),
            active_adapter: Arc::new(RwLock::new(None)),
            config,
            stats: Arc::new(Mutex::new(BrowserManagerStats::default())),
        }
    }

    /// 创建默认的浏览器管理器
    ///
    /// # 返回
    /// Self - 创建的管理器
    pub fn new_default() -> Self {
        Self::new(BrowserManagerConfig::default())
    }

    /// 初始化管理器
    ///
    /// # 返回
    /// Result<()> - 初始化结果
    pub async fn initialize(&self) -> Result<()> {
        if self.config.auto_detection {
            self.auto_detect_and_register_browsers().await?;
        }

        // 选择默认的活跃适配器
        self.select_best_adapter().await?;

        Ok(())
    }

    /// 自动检测并注册浏览器
    ///
    /// # 返回
    /// Result<()> - 注册结果
    async fn auto_detect_and_register_browsers(&self) -> Result<()> {
        let detected_browsers = BrowserDetector::detect_all_browsers().await;
        
        for detection in detected_browsers {
            let config = BrowserAdapterConfig {
                browser_type: detection.browser_type.clone(),
                browser_version: detection.version,
                ..Default::default()
            };

            if let Ok(adapter) = BrowserAdapterFactory::create_adapter(detection.browser_type.clone(), config) {
                self.register_adapter(detection.browser_type, adapter).await?;
            }
        }

        Ok(())
    }

    /// 注册浏览器适配器
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    /// - `adapter`: 适配器实例
    ///
    /// # 返回
    /// Result<()> - 注册结果
    pub async fn register_adapter(
        &self,
        browser_type: BrowserType,
        adapter: Box<dyn BrowserAdapter>,
    ) -> Result<()> {
        let mut adapters = self.adapters.write().await;
        adapters.insert(browser_type, adapter);

        // 更新统计信息
        let mut stats = self.stats.lock().await;
        stats.active_adapters = adapters.len();

        Ok(())
    }

    /// 注销浏览器适配器
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// Result<()> - 注销结果
    pub async fn unregister_adapter(&self, browser_type: &BrowserType) -> Result<()> {
        let mut adapters = self.adapters.write().await;
        adapters.remove(browser_type);

        // 如果移除的是当前活跃适配器，需要重新选择
        {
            let active = self.active_adapter.read().await;
            if let Some(active_type) = active.as_ref() {
                if active_type == browser_type {
                    drop(active); // 释放读锁
                    self.select_best_adapter().await?;
                }
            }
        }

        // 更新统计信息
        let mut stats = self.stats.lock().await;
        stats.active_adapters = adapters.len();

        Ok(())
    }

    /// 选择最佳适配器
    ///
    /// # 返回
    /// Result<()> - 选择结果
    async fn select_best_adapter(&self) -> Result<()> {
        let adapters = self.adapters.read().await;
        
        if adapters.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "没有可用的浏览器适配器".to_string(),
            ));
        }

        // 按照优先级选择浏览器
        let priority = BrowserDetector::get_browser_priority();
        for browser_type in priority {
            if adapters.contains_key(&browser_type) {
                let mut active = self.active_adapter.write().await;
                *active = Some(browser_type);
                return Ok(());
            }
        }

        // 如果按优先级找不到，就选择第一个可用的
        if let Some((browser_type, _)) = adapters.iter().next() {
            let mut active = self.active_adapter.write().await;
            *active = Some(browser_type.clone());
        }

        Ok(())
    }

    /// 发送消息
    ///
    /// # 参数
    /// - `message`: 要发送的消息
    ///
    /// # 返回
    /// Result<()> - 发送结果
    pub async fn send_message(&self, message: OutgoingMessage) -> Result<()> {
        let mut retries = 0;
        let max_retries = self.config.max_retries;

        while retries < max_retries {
            match self.try_send_message(&message).await {
                Ok(()) => {
                    // 记录成功统计
                    let mut stats = self.stats.lock().await;
                    stats.total_messages += 1;
                    stats.successful_messages += 1;
                    return Ok(());
                }
                Err(e) => {
                    retries += 1;
                    if retries >= max_retries {
                        // 记录失败统计
                        let mut stats = self.stats.lock().await;
                        stats.total_messages += 1;
                        stats.failed_messages += 1;
                        return Err(e);
                    }

                    // 尝试故障转移
                    if self.config.enable_failover {
                        self.try_failover().await?;
                    }
                }
            }
        }

        Err(NativeMessagingError::ProtocolError(
            "消息发送失败，已达到最大重试次数".to_string(),
        ))
    }

    /// 尝试发送消息
    ///
    /// # 参数
    /// - `message`: 要发送的消息
    ///
    /// # 返回
    /// Result<()> - 发送结果
    async fn try_send_message(&self, message: &OutgoingMessage) -> Result<()> {
        let active_type = {
            let active = self.active_adapter.read().await;
            active.clone()
        };

        if let Some(browser_type) = active_type {
            let adapters = self.adapters.read().await;
            if let Some(adapter) = adapters.get(&browser_type) {
                return adapter.send_message(message.clone()).await;
            }
        }

        Err(NativeMessagingError::ProtocolError(
            "没有可用的活跃适配器".to_string(),
        ))
    }

    /// 接收消息
    ///
    /// # 返回
    /// Result<NativeMessage> - 接收到的消息
    pub async fn receive_message(&self) -> Result<NativeMessage> {
        let active_type = {
            let active = self.active_adapter.read().await;
            active.clone()
        };

        if let Some(browser_type) = active_type {
            let adapters = self.adapters.read().await;
            if let Some(adapter) = adapters.get(&browser_type) {
                return adapter.receive_message().await;
            }
        }

        Err(NativeMessagingError::ProtocolError(
            "没有可用的活跃适配器".to_string(),
        ))
    }

    /// 尝试故障转移
    ///
    /// # 返回
    /// Result<()> - 故障转移结果
    async fn try_failover(&self) -> Result<()> {
        let current_active = {
            let active = self.active_adapter.read().await;
            active.clone()
        };

        let adapters = self.adapters.read().await;
        let priority = BrowserDetector::get_browser_priority();

        // 尝试找到下一个可用的适配器
        for browser_type in priority {
            if Some(browser_type.clone()) != current_active && adapters.contains_key(&browser_type) {
                if let Some(adapter) = adapters.get(&browser_type) {
                    if adapter.is_connected().await {
                        // 切换到新的适配器
                        drop(adapters); // 释放读锁
                        let mut active = self.active_adapter.write().await;
                        *active = Some(browser_type);

                        // 记录故障转移统计
                        let mut stats = self.stats.lock().await;
                        stats.failover_count += 1;

                        return Ok(());
                    }
                }
            }
        }

        Err(NativeMessagingError::ProtocolError(
            "故障转移失败，没有可用的备用适配器".to_string(),
        ))
    }

    /// 获取当前活跃的适配器类型
    ///
    /// # 返回
    /// Option<BrowserType> - 活跃的适配器类型
    pub async fn get_active_adapter(&self) -> Option<BrowserType> {
        let active = self.active_adapter.read().await;
        active.clone()
    }

    /// 获取所有注册的适配器类型
    ///
    /// # 返回
    /// Vec<BrowserType> - 所有适配器类型
    pub async fn get_registered_adapters(&self) -> Vec<BrowserType> {
        let adapters = self.adapters.read().await;
        adapters.keys().cloned().collect()
    }

    /// 检查适配器是否已连接
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// Result<bool> - 连接状态
    pub async fn is_adapter_connected(&self, browser_type: &BrowserType) -> Result<bool> {
        let adapters = self.adapters.read().await;
        if let Some(adapter) = adapters.get(browser_type) {
            Ok(adapter.is_connected().await)
        } else {
            Err(NativeMessagingError::ProtocolError(
                format!("适配器 {:?} 未注册", browser_type),
            ))
        }
    }

    /// 获取管理器统计信息
    ///
    /// # 返回
    /// BrowserManagerStats - 统计信息
    pub async fn get_stats(&self) -> BrowserManagerStats {
        let stats = self.stats.lock().await;
        stats.clone()
    }

    /// 执行健康检查
    ///
    /// # 返回
    /// Result<HashMap<BrowserType, bool>> - 每个适配器的健康状态
    pub async fn health_check(&self) -> Result<HashMap<BrowserType, bool>> {
        let adapters = self.adapters.read().await;
        let mut health_status = HashMap::new();

        for (browser_type, adapter) in adapters.iter() {
            let is_healthy = adapter.is_connected().await;
            health_status.insert(browser_type.clone(), is_healthy);
        }

        // 更新统计信息
        let mut stats = self.stats.lock().await;
        stats.last_health_check = Some(std::time::SystemTime::now());

        Ok(health_status)
    }

    /// 关闭管理器
    ///
    /// # 返回
    /// Result<()> - 关闭结果
    pub async fn close(&self) -> Result<()> {
        let mut adapters = self.adapters.write().await;
        
        // 关闭所有适配器
        for (_, mut adapter) in adapters.drain() {
            let _ = adapter.close().await; // 忽略关闭错误
        }

        // 重置活跃适配器
        let mut active = self.active_adapter.write().await;
        *active = None;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::browser::ChromeAdapter;

    #[tokio::test]
    async fn test_browser_manager_creation() {
        let manager = BrowserManager::new_default();
        assert!(manager.get_active_adapter().await.is_none());
        assert!(manager.get_registered_adapters().await.is_empty());
    }

    #[tokio::test]
    async fn test_adapter_registration() {
        let manager = BrowserManager::new_default();
        
        // 注册Chrome适配器
        let chrome_config = BrowserAdapterConfig::default();
        let chrome_adapter = Box::new(ChromeAdapter::new(chrome_config).unwrap());
        
        let result = manager.register_adapter(BrowserType::Chrome, chrome_adapter).await;
        assert!(result.is_ok());
        
        let registered = manager.get_registered_adapters().await;
        assert_eq!(registered.len(), 1);
        assert!(registered.contains(&BrowserType::Chrome));
    }

    #[tokio::test]
    async fn test_adapter_unregistration() {
        let manager = BrowserManager::new_default();
        
        // 先注册
        let chrome_config = BrowserAdapterConfig::default();
        let chrome_adapter = Box::new(ChromeAdapter::new(chrome_config).unwrap());
        manager.register_adapter(BrowserType::Chrome, chrome_adapter).await.unwrap();
        
        // 再注销
        let result = manager.unregister_adapter(&BrowserType::Chrome).await;
        assert!(result.is_ok());
        
        let registered = manager.get_registered_adapters().await;
        assert!(registered.is_empty());
    }

    #[tokio::test]
    async fn test_auto_detection_and_initialization() {
        let manager = BrowserManager::new_default();
        
        let result = manager.initialize().await;
        assert!(result.is_ok());
        
        // 应该有一些适配器被自动注册
        let registered = manager.get_registered_adapters().await;
        assert!(!registered.is_empty());
        
        // 应该有一个活跃的适配器
        let active = manager.get_active_adapter().await;
        assert!(active.is_some());
    }

    #[tokio::test]
    async fn test_health_check() {
        let manager = BrowserManager::new_default();
        manager.initialize().await.unwrap();
        
        let health_status = manager.health_check().await;
        assert!(health_status.is_ok());
        
        let status = health_status.unwrap();
        assert!(!status.is_empty());
    }

    #[tokio::test]
    async fn test_stats_tracking() {
        let manager = BrowserManager::new_default();
        let stats = manager.get_stats().await;
        
        assert_eq!(stats.total_messages, 0);
        assert_eq!(stats.successful_messages, 0);
        assert_eq!(stats.failed_messages, 0);
        assert_eq!(stats.failover_count, 0);
    }

    #[tokio::test]
    async fn test_manager_close() {
        let manager = BrowserManager::new_default();
        manager.initialize().await.unwrap();
        
        let result = manager.close().await;
        assert!(result.is_ok());
        
        // 关闭后应该没有活跃适配器
        let active = manager.get_active_adapter().await;
        assert!(active.is_none());
    }
} 