//! Native Messaging 跨浏览器适配模块
//!
//! 提供跨主流浏览器的统一适配接口和实现

pub mod chrome;
pub mod edge;
pub mod firefox;
pub mod safari;
pub mod detector;
pub mod manager;

use crate::native_messaging::{
    error::Result,
    protocol::message::{NativeMessage, OutgoingMessage},
    config::BrowserType,
};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::Duration;

/// 浏览器适配器接口
///
/// 定义跨浏览器消息处理的统一接口
#[async_trait]
pub trait BrowserAdapter: Send + Sync {
    /// 获取浏览器类型
    fn browser_type(&self) -> BrowserType;

    /// 获取浏览器版本信息
    fn browser_version(&self) -> &str;

    /// 发送消息到浏览器
    ///
    /// # 参数
    /// - `message`: 要发送的消息
    ///
    /// # 返回
    /// Result<()> - 发送结果
    async fn send_message(&self, message: OutgoingMessage) -> Result<()>;

    /// 从浏览器接收消息
    ///
    /// # 返回
    /// Result<NativeMessage> - 接收到的消息
    async fn receive_message(&self) -> Result<NativeMessage>;

    /// 检查浏览器连接状态
    ///
    /// # 返回
    /// bool - 连接是否正常
    async fn is_connected(&self) -> bool;

    /// 初始化浏览器连接
    ///
    /// # 返回
    /// Result<()> - 初始化结果
    async fn initialize(&mut self) -> Result<()>;

    /// 关闭浏览器连接
    ///
    /// # 返回
    /// Result<()> - 关闭结果
    async fn close(&mut self) -> Result<()>;

    /// 获取支持的消息类型
    ///
    /// # 返回
    /// Vec<String> - 支持的消息类型列表
    fn supported_message_types(&self) -> Vec<String>;

    /// 获取适配器配置
    ///
    /// # 返回
    /// &BrowserAdapterConfig - 适配器配置
    fn config(&self) -> &BrowserAdapterConfig;
}

/// 浏览器适配器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserAdapterConfig {
    /// 浏览器类型
    pub browser_type: BrowserType,
    /// 浏览器版本
    pub browser_version: String,
    /// 连接超时时间（毫秒）
    pub connection_timeout: u64,
    /// 消息超时时间（毫秒）
    pub message_timeout: u64,
    /// 最大重试次数
    pub max_retries: u32,
    /// 是否启用调试模式
    pub debug_mode: bool,
    /// 扩展配置
    pub extensions: HashMap<String, String>,
}

impl Default for BrowserAdapterConfig {
    fn default() -> Self {
        Self {
            browser_type: BrowserType::Chrome,
            browser_version: "unknown".to_string(),
            connection_timeout: 5000,
            message_timeout: 10000,
            max_retries: 3,
            debug_mode: false,
            extensions: HashMap::new(),
        }
    }
}

/// 浏览器连接状态
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ConnectionStatus {
    /// 未连接
    Disconnected,
    /// 连接中
    Connecting,
    /// 已连接
    Connected,
    /// 连接失败
    Failed(String),
    /// 连接超时
    Timeout,
}

/// 浏览器适配器统计信息
#[derive(Debug, Clone, Default)]
pub struct BrowserAdapterStats {
    /// 总发送消息数
    pub messages_sent: u64,
    /// 总接收消息数
    pub messages_received: u64,
    /// 发送成功数
    pub send_success: u64,
    /// 发送失败数
    pub send_failures: u64,
    /// 接收成功数
    pub receive_success: u64,
    /// 接收失败数
    pub receive_failures: u64,
    /// 平均响应时间（毫秒）
    pub average_response_time: Duration,
    /// 连接时间
    pub connection_time: Option<std::time::SystemTime>,
    /// 最后活动时间
    pub last_activity: Option<std::time::SystemTime>,
}

impl BrowserAdapterStats {
    /// 记录发送消息
    pub fn record_send(&mut self, success: bool) {
        self.messages_sent += 1;
        if success {
            self.send_success += 1;
        } else {
            self.send_failures += 1;
        }
        self.last_activity = Some(std::time::SystemTime::now());
    }

    /// 记录接收消息
    pub fn record_receive(&mut self, success: bool) {
        self.messages_received += 1;
        if success {
            self.receive_success += 1;
        } else {
            self.receive_failures += 1;
        }
        self.last_activity = Some(std::time::SystemTime::now());
    }

    /// 记录响应时间
    pub fn record_response_time(&mut self, response_time: Duration) {
        let total_messages = self.messages_sent + self.messages_received;
        if total_messages > 0 {
            let current_total = self.average_response_time.as_millis() as u64 * (total_messages - 1);
            let new_total = current_total + response_time.as_millis() as u64;
            self.average_response_time = Duration::from_millis(new_total / total_messages);
        } else {
            self.average_response_time = response_time;
        }
    }

    /// 获取成功率
    pub fn success_rate(&self) -> f64 {
        let total_operations = self.messages_sent + self.messages_received;
        if total_operations == 0 {
            return 0.0;
        }
        let successful_operations = self.send_success + self.receive_success;
        successful_operations as f64 / total_operations as f64
    }
}

/// 浏览器适配器工厂
pub struct BrowserAdapterFactory;

impl BrowserAdapterFactory {
    /// 创建浏览器适配器
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    /// - `config`: 适配器配置
    ///
    /// # 返回
    /// Result<Box<dyn BrowserAdapter>> - 创建的适配器
    pub fn create_adapter(
        browser_type: BrowserType,
        config: BrowserAdapterConfig,
    ) -> Result<Box<dyn BrowserAdapter>> {
        match browser_type {
            BrowserType::Chrome => Ok(Box::new(chrome::ChromeAdapter::new(config)?)),
            BrowserType::Firefox => Ok(Box::new(firefox::FirefoxAdapter::new(config)?)),
            BrowserType::Edge => Ok(Box::new(edge::EdgeAdapter::new(config)?)),
            BrowserType::Safari => Ok(Box::new(safari::SafariAdapter::new(config)?)),
        }
    }

    /// 检测并创建适配器
    ///
    /// # 返回
    /// Result<Box<dyn BrowserAdapter>> - 自动检测的适配器
    pub async fn auto_detect_adapter() -> Result<Box<dyn BrowserAdapter>> {
        let browser_type = detector::BrowserDetector::detect_browser().await?;
        let config = BrowserAdapterConfig {
            browser_type: browser_type.clone(),
            ..Default::default()
        };
        Self::create_adapter(browser_type, config)
    }

    /// 获取支持的浏览器类型
    ///
    /// # 返回
    /// Vec<BrowserType> - 支持的浏览器类型列表
    pub fn supported_browsers() -> Vec<BrowserType> {
        vec![
            BrowserType::Chrome,
            BrowserType::Firefox,
            BrowserType::Edge,
            BrowserType::Safari,
        ]
    }
}

// 重新导出主要类型
pub use chrome::ChromeAdapter;
pub use edge::EdgeAdapter;
pub use firefox::FirefoxAdapter;
pub use safari::SafariAdapter;
pub use detector::BrowserDetector;
pub use manager::BrowserManager;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_browser_adapter_config_default() {
        let config = BrowserAdapterConfig::default();
        assert_eq!(config.browser_type, BrowserType::Chrome);
        assert_eq!(config.connection_timeout, 5000);
        assert_eq!(config.message_timeout, 10000);
        assert_eq!(config.max_retries, 3);
        assert!(!config.debug_mode);
    }

    #[test]
    fn test_browser_adapter_stats() {
        let mut stats = BrowserAdapterStats::default();
        
        // 测试记录发送消息
        stats.record_send(true);
        assert_eq!(stats.messages_sent, 1);
        assert_eq!(stats.send_success, 1);
        assert_eq!(stats.send_failures, 0);
        
        stats.record_send(false);
        assert_eq!(stats.messages_sent, 2);
        assert_eq!(stats.send_success, 1);
        assert_eq!(stats.send_failures, 1);
        
        // 测试记录接收消息
        stats.record_receive(true);
        assert_eq!(stats.messages_received, 1);
        assert_eq!(stats.receive_success, 1);
        
        // 测试成功率计算
        let success_rate = stats.success_rate();
        assert_eq!(success_rate, 2.0/3.0); // 2 成功 / 3 总计
    }

    #[test]
    fn test_connection_status() {
        let status = ConnectionStatus::Connected;
        assert_eq!(status, ConnectionStatus::Connected);
        
        let failed_status = ConnectionStatus::Failed("测试错误".to_string());
        match failed_status {
            ConnectionStatus::Failed(msg) => assert_eq!(msg, "测试错误"),
            _ => panic!("应该是Failed状态"),
        }
    }

    #[test]
    fn test_browser_adapter_factory_supported_browsers() {
        let browsers = BrowserAdapterFactory::supported_browsers();
        assert_eq!(browsers.len(), 4);
        assert!(browsers.contains(&BrowserType::Chrome));
        assert!(browsers.contains(&BrowserType::Firefox));
        assert!(browsers.contains(&BrowserType::Edge));
        assert!(browsers.contains(&BrowserType::Safari));
    }
}
