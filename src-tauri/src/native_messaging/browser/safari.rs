//! Safari 浏览器适配器实现
//!
//! 提供针对 Safari 浏览器的 Native Messaging 支持

use super::{BrowserAdapter, BrowserAdapterConfig, BrowserAdapterStats, ConnectionStatus};
use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::{NativeMessage, OutgoingMessage},
    config::BrowserType,
};
use async_trait::async_trait;
use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime};
use tokio::sync::Mutex;

/// Safari 浏览器适配器
///
/// 实现 Safari 特定的 Native Messaging 协议和 Web Extensions API
/// Safari 使用不同的扩展架构，需要特殊的消息处理
pub struct SafariAdapter {
    /// 适配器配置
    config: BrowserAdapterConfig,
    /// 连接状态
    connection_status: Arc<RwLock<ConnectionStatus>>,
    /// 统计信息
    stats: Arc<Mutex<BrowserAdapterStats>>,
    /// 是否已初始化
    initialized: Arc<RwLock<bool>>,
}

impl SafariAdapter {
    /// 创建新的 Safari 适配器
    ///
    /// # 参数
    /// - `config`: 适配器配置
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new(config: BrowserAdapterConfig) -> Result<Self> {
        let mut safari_config = config;
        safari_config.browser_type = BrowserType::Safari;
        if safari_config.browser_version == "unknown" {
            safari_config.browser_version = Self::detect_safari_version();
        }
        
        Ok(Self {
            config: safari_config,
            connection_status: Arc::new(RwLock::new(ConnectionStatus::Disconnected)),
            stats: Arc::new(Mutex::new(BrowserAdapterStats::default())),
            initialized: Arc::new(RwLock::new(false)),
        })
    }

    /// 创建默认的 Safari 适配器
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new_default() -> Result<Self> {
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Safari,
            browser_version: Self::detect_safari_version(),
            // Safari 通常需要更长的初始化时间
            connection_timeout: 10000,
            message_timeout: 20000,
            max_retries: 3,
            debug_mode: false,
            ..Default::default()
        };
        Self::new(config)
    }

    /// 检测 Safari 版本
    ///
    /// # 返回
    /// String - Safari 版本信息
    fn detect_safari_version() -> String {
        // 在实际实现中，这里会读取 Safari 的版本信息
        // Safari 版本格式通常为 Safari/17.2
        "Safari/17.2".to_string()
    }

    /// 验证消息格式是否符合 Safari 规范
    ///
    /// # 参数
    /// - `message`: 要验证的消息
    ///
    /// # 返回
    /// Result<()> - 验证结果
    fn validate_safari_message(&self, message: &OutgoingMessage) -> Result<()> {
        // Safari Native Messaging 格式验证
        if message.message.payload.is_null() {
            return Err(NativeMessagingError::ProtocolError(
                "Safari 消息数据不能为空".to_string(),
            ));
        }

        // Safari 的消息大小限制（相对较小，256KB）
        let message_str = serde_json::to_string(&message.message.payload)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;
        
        if message_str.len() > 256 * 1024 {
            return Err(NativeMessagingError::ProtocolError(
                "Safari 消息大小超过 256KB 限制".to_string(),
            ));
        }

        // Safari 要求特定的消息结构
        if message.message.source.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "Safari 消息必须包含有效的源标识".to_string(),
            ));
        }

        // Safari 需要特定的请求ID格式
        if message.message.request_id.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "Safari 消息必须包含请求ID".to_string(),
            ));
        }

        Ok(())
    }

    /// 转换消息格式为 Safari 兼容格式
    ///
    /// # 参数
    /// - `message`: 原始消息
    ///
    /// # 返回
    /// Result<OutgoingMessage> - 转换后的消息
    fn convert_to_safari_format(&self, mut message: OutgoingMessage) -> Result<OutgoingMessage> {
        // Safari 需要在消息中添加特定的Apple metadata
        if let Some(obj) = message.message.payload.as_object_mut() {
            obj.insert("browser".to_string(), serde_json::Value::String("safari".to_string()));
            obj.insert("webkit_version".to_string(), serde_json::Value::String("webkit-618.1.15".to_string()));
            obj.insert("safari_extension_api".to_string(), serde_json::Value::String("2.0".to_string()));
            obj.insert("apple_platform".to_string(), serde_json::Value::String(
                if cfg!(target_os = "macos") { "macos" } else { "ios" }.to_string()
            ));
        }

        Ok(message)
    }

    /// 设置连接状态
    ///
    /// # 参数
    /// - `status`: 新的连接状态
    fn set_connection_status(&self, status: ConnectionStatus) {
        if let Ok(mut current_status) = self.connection_status.write() {
            *current_status = status;
        }
    }

    /// 获取连接状态
    ///
    /// # 返回
    /// ConnectionStatus - 当前连接状态
    fn get_connection_status(&self) -> ConnectionStatus {
        self.connection_status
            .read()
            .map(|status| status.clone())
            .unwrap_or(ConnectionStatus::Failed("无法读取状态".to_string()))
    }
}

#[async_trait]
impl BrowserAdapter for SafariAdapter {
    fn browser_type(&self) -> BrowserType {
        BrowserType::Safari
    }

    fn browser_version(&self) -> &str {
        &self.config.browser_version
    }

    async fn send_message(&self, message: OutgoingMessage) -> Result<()> {
        let start_time = SystemTime::now();
        
        // 验证消息格式
        self.validate_safari_message(&message)?;

        // 转换为 Safari 格式
        let _safari_message = self.convert_to_safari_format(message)?;

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_send(false);
            return Err(NativeMessagingError::ConnectionError(
                "Safari 连接未建立".to_string(),
            ));
        }

        // 模拟发送消息到 Safari (最慢，因为Safari的沙箱限制)
        // 在实际实现中，这里会通过 Safari 特定的通信机制
        tokio::time::sleep(Duration::from_millis(20)).await;

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_send(true);
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        Ok(())
    }

    async fn receive_message(&self) -> Result<NativeMessage> {
        let start_time = SystemTime::now();

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_receive(false);
            return Err(NativeMessagingError::ConnectionError(
                "Safari 连接未建立".to_string(),
            ));
        }

        // 模拟从 Safari 接收消息 (包含Apple安全验证时间)
        // 在实际实现中，这里会从 Safari 扩展接收消息
        tokio::time::sleep(Duration::from_millis(15)).await;
        
        let message = NativeMessage::new(
            crate::native_messaging::protocol::message::MessageType::HealthCheck,
            "safari-test".to_string(),
            serde_json::json!({
                "source": "safari",
                "webkit_version": "webkit-618.1.15",
                "safari_version": self.browser_version(),
                "apple_platform": if cfg!(target_os = "macos") { "macos" } else { "ios" }
            }),
            "safari-extension".to_string(),
        );

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_receive(true);
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        Ok(message)
    }

    async fn is_connected(&self) -> bool {
        matches!(self.get_connection_status(), ConnectionStatus::Connected)
    }

    async fn initialize(&mut self) -> Result<()> {
        if let Ok(initialized) = self.initialized.read() {
            if *initialized {
                return Ok(());
            }
        }

        self.set_connection_status(ConnectionStatus::Connecting);
        
        // Safari 初始化最慢，包含Apple安全验证和沙箱初始化
        tokio::time::sleep(Duration::from_millis(200)).await;

        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = true;
        }

        self.set_connection_status(ConnectionStatus::Connected);
        Ok(())
    }

    async fn close(&mut self) -> Result<()> {
        self.set_connection_status(ConnectionStatus::Disconnected);
        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = false;
        }
        Ok(())
    }

    fn supported_message_types(&self) -> Vec<String> {
        vec![
            "HealthCheck".to_string(),
            "Ping".to_string(),
            "Auth".to_string(),
            "Password".to_string(),
            "Version".to_string(),
            "Test".to_string(),
            // Safari 特定的消息类型
            "SafariExtension".to_string(),
            "WebKitMessage".to_string(),
        ]
    }

    fn config(&self) -> &BrowserAdapterConfig {
        &self.config
    }
}

impl Default for SafariAdapter {
    fn default() -> Self {
        Self::new_default().expect("创建默认 Safari 适配器失败")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;

    #[test]
    fn test_safari_adapter_creation() {
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Safari,
            browser_version: "Safari/17.2".to_string(),
            ..Default::default()
        };
        let adapter = SafariAdapter::new(config).unwrap();
        assert_eq!(adapter.browser_type(), BrowserType::Safari);
        assert_eq!(adapter.browser_version(), "Safari/17.2");
    }

    #[test]
    fn test_safari_adapter_default() {
        let adapter = SafariAdapter::default();
        assert_eq!(adapter.browser_type(), BrowserType::Safari);
        assert!(adapter.browser_version().starts_with("Safari/"));
    }

    #[tokio::test]
    async fn test_safari_adapter_initialization() {
        let mut adapter = SafariAdapter::default();
        assert!(!adapter.is_connected().await);
        
        let result = adapter.initialize().await;
        assert!(result.is_ok());
        assert!(adapter.is_connected().await);
    }

    #[tokio::test]
    async fn test_safari_adapter_send_message() {
        let mut adapter = SafariAdapter::default();
        adapter.initialize().await.unwrap();

        let message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"test": "data"}),
                "test-source".to_string(),
            ),
        );

        let result = adapter.send_message(message).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_safari_adapter_receive_message() {
        let mut adapter = SafariAdapter::default();
        adapter.initialize().await.unwrap();

        let result = adapter.receive_message().await;
        assert!(result.is_ok());
        
        let message = result.unwrap();
        assert!(message.payload.get("webkit_version").is_some());
        assert!(message.payload.get("apple_platform").is_some());
    }

    #[tokio::test]
    async fn test_safari_adapter_close() {
        let mut adapter = SafariAdapter::default();
        adapter.initialize().await.unwrap();
        assert!(adapter.is_connected().await);

        let result = adapter.close().await;
        assert!(result.is_ok());
        assert!(!adapter.is_connected().await);
    }

    #[test]
    fn test_safari_message_validation() {
        let adapter = SafariAdapter::default();
        
        // 测试有效消息
        let valid_message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"test": "data"}),
                "test-source".to_string(),
            ),
        );
        assert!(adapter.validate_safari_message(&valid_message).is_ok());

        // 测试空数据消息
        let invalid_message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::Value::Null,
                "test-source".to_string(),
            ),
        );
        assert!(adapter.validate_safari_message(&invalid_message).is_err());

        // 测试空源消息
        let invalid_source_message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "".to_string(),
                serde_json::json!({"test": "data"}),
                "".to_string(),
            ),
        );
        assert!(adapter.validate_safari_message(&invalid_source_message).is_err());
    }

    #[test]
    fn test_safari_message_format_conversion() {
        let adapter = SafariAdapter::default();
        let message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"original": "data"}),
                "test-source".to_string(),
            ),
        );

        let result = adapter.convert_to_safari_format(message);
        assert!(result.is_ok());
        
        let converted = result.unwrap();
        assert!(converted.message.payload.get("browser").is_some());
        assert!(converted.message.payload.get("webkit_version").is_some());
        assert!(converted.message.payload.get("apple_platform").is_some());
        assert_eq!(converted.message.payload["browser"], "safari");
    }

    #[test]
    fn test_safari_version_detection() {
        let version = SafariAdapter::detect_safari_version();
        assert!(version.starts_with("Safari/"));
        assert!(version.contains("."));
    }

    #[test]
    fn test_safari_config_differences() {
        let safari_config = SafariAdapter::new_default().unwrap();
        
        // Safari 特定的配置值
        assert_eq!(safari_config.config.connection_timeout, 10000);
        assert_eq!(safari_config.config.message_timeout, 20000);
        assert_eq!(safari_config.config.max_retries, 3);
        
        // 支持的消息类型包含Safari特定类型
        let supported = safari_config.supported_message_types();
        assert!(supported.contains(&"SafariExtension".to_string()));
        assert!(supported.contains(&"WebKitMessage".to_string()));
    }

    #[test]
    fn test_safari_platform_detection() {
        let adapter = SafariAdapter::default();
        let message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"test": "data"}),
                "test-source".to_string(),
            ),
        );

        let result = adapter.convert_to_safari_format(message);
        assert!(result.is_ok());
        
        let converted = result.unwrap();
        let platform = converted.message.payload.get("apple_platform").unwrap();
        
        // 在测试环境中应该检测到正确的平台
        if cfg!(target_os = "macos") {
            assert_eq!(platform, "macos");
        } else {
            assert_eq!(platform, "ios");
        }
    }
} 