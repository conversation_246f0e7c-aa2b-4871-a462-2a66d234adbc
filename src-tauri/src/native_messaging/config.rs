//! Native Messaging 配置管理模块
//!
//! 提供统一的配置管理、验证机制、环境变量支持和热重载功能

use crate::native_messaging::error::{NativeMessagingError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::env;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::Duration;
use tokio::fs;
use tokio::sync::RwLock;
use tracing::{error, info, warn};

/// 配置变更通知器
pub type ConfigChangeNotifier = Arc<dyn Fn(&NativeMessagingConfig) -> Result<()> + Send + Sync>;

/// 配置管理器
///
/// 负责配置的加载、验证、热重载和变更通知
pub struct ConfigManager {
    config: Arc<RwLock<NativeMessagingConfig>>,
    config_path: Option<PathBuf>,
    change_notifiers: Vec<ConfigChangeNotifier>,
}

impl std::fmt::Debug for ConfigManager {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ConfigManager")
            .field("config", &"Arc<RwLock<NativeMessagingConfig>>")
            .field("config_path", &self.config_path)
            .field(
                "change_notifiers",
                &format!("{} notifiers", self.change_notifiers.len()),
            )
            .finish()
    }
}

impl ConfigManager {
    /// 创建配置管理器
    pub fn new(config: NativeMessagingConfig) -> Self {
        Self {
            config: Arc::new(RwLock::new(config)),
            config_path: None,
            change_notifiers: Vec::new(),
        }
    }

    /// 从文件创建配置管理器
    pub async fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let config = NativeMessagingConfig::load_from_file(&path).await?;
        let mut manager = Self::new(config);
        manager.config_path = Some(path.as_ref().to_path_buf());
        Ok(manager)
    }

    /// 获取当前配置
    pub async fn get_config(&self) -> NativeMessagingConfig {
        self.config.read().await.clone()
    }

    /// 更新配置
    pub async fn update_config(&self, new_config: NativeMessagingConfig) -> Result<()> {
        new_config.validate()?;

        {
            let mut config = self.config.write().await;
            *config = new_config.clone();
        }

        // 通知变更
        for notifier in &self.change_notifiers {
            if let Err(e) = notifier(&new_config) {
                error!("配置变更通知失败: {}", e);
            }
        }

        info!("配置已更新");
        Ok(())
    }

    /// 热重载配置
    pub async fn reload(&self) -> Result<()> {
        if let Some(path) = &self.config_path {
            let new_config = NativeMessagingConfig::load_from_file(path).await?;
            self.update_config(new_config).await?;
            info!("配置热重载完成");
        } else {
            warn!("未指定配置文件路径，无法热重载");
        }
        Ok(())
    }

    /// 添加配置变更通知器
    pub fn add_change_notifier(&mut self, notifier: ConfigChangeNotifier) {
        self.change_notifiers.push(notifier);
    }
}

/// Native Messaging 主配置结构
///
/// 包含监听器、安全、监控和浏览器等各模块的配置信息
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct NativeMessagingConfig {
    /// 监听器配置
    pub listener: ListenerConfig,
    /// 安全配置
    pub security: SecurityConfig,
    /// 监控配置  
    pub monitoring: MonitoringConfig,
    /// 浏览器配置
    pub browsers: BrowserConfigs,
}

impl NativeMessagingConfig {
    /// 创建配置构建器
    ///
    /// 返回配置构建器实例，用于链式调用设置配置参数
    ///
    /// # 返回
    /// ConfigBuilder - 配置构建器实例
    pub fn builder() -> ConfigBuilder {
        ConfigBuilder::new()
    }

    /// 从文件加载配置
    ///
    /// 支持 JSON 和 TOML 格式的配置文件
    ///
    /// # 参数
    /// - `path`: 配置文件路径
    ///
    /// # 返回
    /// Result<NativeMessagingConfig> - 加载的配置或错误
    pub async fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path = path.as_ref();
        let content = fs::read_to_string(path)
            .await
            .map_err(|e| NativeMessagingError::ConfigError(format!("读取配置文件失败: {}", e)))?;

        let config = if path.extension().and_then(|s| s.to_str()) == Some("toml") {
            toml::from_str(&content).map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析TOML配置失败: {}", e))
            })?
        } else {
            // 默认为 JSON
            serde_json::from_str(&content).map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析JSON配置失败: {}", e))
            })?
        };

        info!("从文件加载配置: {}", path.display());
        Ok(config)
    }

    /// 从环境变量加载配置
    ///
    /// 环境变量命名规则: NATIVE_MESSAGING_{MODULE}_{FIELD}
    /// 例如: NATIVE_MESSAGING_LISTENER_MAX_CONNECTIONS
    ///
    /// # 返回
    /// Result<NativeMessagingConfig> - 从环境变量构建的配置
    pub fn load_from_env() -> Result<Self> {
        let config = Self {
            listener: ListenerConfig::load_from_env()?,
            security: SecurityConfig::load_from_env()?,
            monitoring: MonitoringConfig::load_from_env()?,
            browsers: BrowserConfigs::load_from_env()?,
        };

        info!("从环境变量加载配置完成");
        config.validate()?;
        Ok(config)
    }

    /// 合并环境变量覆盖
    ///
    /// 在现有配置基础上，用环境变量值覆盖对应字段
    ///
    /// # 返回
    /// Result<Self> - 合并后的配置
    pub fn merge_env_overrides(mut self) -> Result<Self> {
        self.listener = self.listener.merge_env_overrides()?;
        self.security = self.security.merge_env_overrides()?;
        self.monitoring = self.monitoring.merge_env_overrides()?;
        self.browsers = self.browsers.merge_env_overrides()?;

        self.validate()?;
        Ok(self)
    }

    /// 保存配置到文件
    ///
    /// 根据文件扩展名选择格式 (.json 或 .toml)
    ///
    /// # 参数
    /// - `path`: 保存路径
    ///
    /// # 返回
    /// Result<()> - 保存成功或失败
    pub async fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let path = path.as_ref();

        let content = if path.extension().and_then(|s| s.to_str()) == Some("toml") {
            toml::to_string_pretty(self)
                .map_err(|e| NativeMessagingError::ConfigError(format!("序列化TOML失败: {}", e)))?
        } else {
            // 默认为 JSON
            serde_json::to_string_pretty(self)
                .map_err(|e| NativeMessagingError::ConfigError(format!("序列化JSON失败: {}", e)))?
        };

        fs::write(path, content)
            .await
            .map_err(|e| NativeMessagingError::ConfigError(format!("写入配置文件失败: {}", e)))?;

        info!("配置已保存到文件: {}", path.display());
        Ok(())
    }

    /// 验证配置参数有效性
    ///
    /// 检查所有配置模块的参数是否符合要求
    ///
    /// # 返回
    /// Result<()> - 验证成功或失败信息
    ///
    /// # 错误
    /// 当配置参数无效时返回具体错误信息
    pub fn validate(&self) -> Result<()> {
        self.listener.validate()?;
        self.security.validate()?;
        self.monitoring.validate()?;
        self.browsers.validate()?;
        info!("配置验证通过");
        Ok(())
    }
}

/// 配置构建器
///
/// 提供链式调用的配置构建方式
#[derive(Debug)]
pub struct ConfigBuilder {
    config: NativeMessagingConfig,
}

impl ConfigBuilder {
    /// 创建新的配置构建器
    pub fn new() -> Self {
        Self {
            config: NativeMessagingConfig::default(),
        }
    }

    /// 设置监听器配置
    pub fn listener(mut self, listener: ListenerConfig) -> Self {
        self.config.listener = listener;
        self
    }

    /// 设置安全配置
    pub fn security(mut self, security: SecurityConfig) -> Self {
        self.config.security = security;
        self
    }

    /// 设置监控配置
    pub fn monitoring(mut self, monitoring: MonitoringConfig) -> Self {
        self.config.monitoring = monitoring;
        self
    }

    /// 设置浏览器配置
    pub fn browsers(mut self, browsers: BrowserConfigs) -> Self {
        self.config.browsers = browsers;
        self
    }

    /// 启用所有浏览器支持
    pub fn enable_all_browsers(mut self) -> Self {
        self.config.browsers = BrowserConfigs::all_enabled();
        self
    }

    /// 设置最大连接数
    pub fn max_connections(mut self, max_connections: usize) -> Self {
        self.config.listener.max_connections = max_connections;
        self
    }

    /// 设置消息超时时间
    pub fn message_timeout(mut self, timeout: Duration) -> Self {
        self.config.listener.message_timeout = timeout;
        self
    }

    /// 启用/禁用身份验证
    pub fn enable_authentication(mut self, enable: bool) -> Self {
        self.config.security.enable_authentication = enable;
        self
    }

    /// 设置授权扩展列表
    pub fn authorized_extensions(mut self, extensions: Vec<String>) -> Self {
        self.config.security.authorized_extensions = extensions;
        self
    }

    /// 启用/禁用性能监控
    pub fn enable_metrics(mut self, enable: bool) -> Self {
        self.config.monitoring.enable_metrics = enable;
        self
    }

    /// 从环境变量合并配置
    pub fn merge_env(mut self) -> Result<Self> {
        self.config = self.config.merge_env_overrides()?;
        Ok(self)
    }

    /// 构建配置
    ///
    /// 验证配置参数并返回最终配置
    ///
    /// # 返回
    /// Result<NativeMessagingConfig> - 构建的配置或验证错误
    pub fn build(self) -> Result<NativeMessagingConfig> {
        self.config.validate()?;
        Ok(self.config)
    }
}

impl Default for ConfigBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 监听器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ListenerConfig {
    /// 最大连接数
    pub max_connections: usize,
    /// 消息超时时间
    #[serde(with = "serde_duration")]
    pub message_timeout: Duration,
    /// 缓冲区大小
    pub buffer_size: usize,
    /// 工作线程数
    pub worker_threads: usize,
    /// 是否启用连接池
    pub enable_connection_pool: bool,
    /// 连接池空闲超时
    #[serde(with = "serde_duration")]
    pub pool_idle_timeout: Duration,
}

impl Default for ListenerConfig {
    fn default() -> Self {
        Self {
            max_connections: crate::native_messaging::defaults::MAX_CONNECTIONS,
            message_timeout: crate::native_messaging::defaults::MESSAGE_TIMEOUT,
            buffer_size: crate::native_messaging::defaults::BUFFER_SIZE,
            worker_threads: crate::native_messaging::defaults::WORKER_THREADS,
            enable_connection_pool: true,
            pool_idle_timeout: Duration::from_secs(300), // 5分钟
        }
    }
}

impl ListenerConfig {
    /// 从环境变量加载配置
    pub fn load_from_env() -> Result<Self> {
        let mut config = Self::default();

        if let Ok(val) = env::var("NATIVE_MESSAGING_LISTENER_MAX_CONNECTIONS") {
            config.max_connections = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析MAX_CONNECTIONS失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_LISTENER_MESSAGE_TIMEOUT") {
            let seconds: u64 = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析MESSAGE_TIMEOUT失败: {}", e))
            })?;
            config.message_timeout = Duration::from_secs(seconds);
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_LISTENER_BUFFER_SIZE") {
            config.buffer_size = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析BUFFER_SIZE失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_LISTENER_WORKER_THREADS") {
            config.worker_threads = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析WORKER_THREADS失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_LISTENER_ENABLE_CONNECTION_POOL") {
            config.enable_connection_pool = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_CONNECTION_POOL失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_LISTENER_POOL_IDLE_TIMEOUT") {
            let seconds: u64 = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析POOL_IDLE_TIMEOUT失败: {}", e))
            })?;
            config.pool_idle_timeout = Duration::from_secs(seconds);
        }

        config.validate()?;
        Ok(config)
    }

    /// 合并环境变量覆盖
    pub fn merge_env_overrides(mut self) -> Result<Self> {
        if let Ok(val) = env::var("NATIVE_MESSAGING_LISTENER_MAX_CONNECTIONS") {
            self.max_connections = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析MAX_CONNECTIONS失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_LISTENER_MESSAGE_TIMEOUT") {
            let seconds: u64 = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析MESSAGE_TIMEOUT失败: {}", e))
            })?;
            self.message_timeout = Duration::from_secs(seconds);
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_LISTENER_BUFFER_SIZE") {
            self.buffer_size = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析BUFFER_SIZE失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_LISTENER_WORKER_THREADS") {
            self.worker_threads = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析WORKER_THREADS失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_LISTENER_ENABLE_CONNECTION_POOL") {
            self.enable_connection_pool = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_CONNECTION_POOL失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_LISTENER_POOL_IDLE_TIMEOUT") {
            let seconds: u64 = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析POOL_IDLE_TIMEOUT失败: {}", e))
            })?;
            self.pool_idle_timeout = Duration::from_secs(seconds);
        }

        self.validate()?;
        Ok(self)
    }

    /// 验证监听器配置
    pub fn validate(&self) -> Result<()> {
        if self.max_connections == 0 {
            return Err(NativeMessagingError::ConfigError(
                "最大连接数必须大于0".to_string(),
            ));
        }

        if self.message_timeout.is_zero() {
            return Err(NativeMessagingError::ConfigError(
                "消息超时时间必须大于0".to_string(),
            ));
        }

        if self.buffer_size == 0 {
            return Err(NativeMessagingError::ConfigError(
                "缓冲区大小必须大于0".to_string(),
            ));
        }

        if self.worker_threads == 0 {
            return Err(NativeMessagingError::ConfigError(
                "工作线程数必须大于0".to_string(),
            ));
        }

        Ok(())
    }
}

/// 安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// 是否启用身份验证
    pub enable_authentication: bool,
    /// 授权的扩展 ID 列表
    pub authorized_extensions: Vec<String>,
    /// 是否启用消息加密
    pub enable_encryption: bool,
    /// 会话超时时间
    #[serde(with = "serde_duration")]
    pub session_timeout: Duration,
    /// 是否启用审计日志
    pub enable_audit_log: bool,
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            enable_authentication: false, // Module 1阶段默认关闭认证
            authorized_extensions: vec![],
            enable_encryption: true,
            session_timeout: Duration::from_secs(3600), // 1小时
            enable_audit_log: true,
        }
    }
}

impl SecurityConfig {
    /// 从环境变量加载配置
    pub fn load_from_env() -> Result<Self> {
        let mut config = Self::default();

        if let Ok(val) = env::var("NATIVE_MESSAGING_SECURITY_ENABLE_AUTHENTICATION") {
            config.enable_authentication = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_AUTHENTICATION失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_SECURITY_AUTHORIZED_EXTENSIONS") {
            config.authorized_extensions = val
                .split(',')
                .map(|s| s.trim().to_string())
                .filter(|s| !s.is_empty())
                .collect();
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_SECURITY_ENABLE_ENCRYPTION") {
            config.enable_encryption = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_ENCRYPTION失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_SECURITY_SESSION_TIMEOUT") {
            let seconds: u64 = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析SESSION_TIMEOUT失败: {}", e))
            })?;
            config.session_timeout = Duration::from_secs(seconds);
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_SECURITY_ENABLE_AUDIT_LOG") {
            config.enable_audit_log = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_AUDIT_LOG失败: {}", e))
            })?;
        }

        config.validate()?;
        Ok(config)
    }

    /// 合并环境变量覆盖
    pub fn merge_env_overrides(mut self) -> Result<Self> {
        if let Ok(val) = env::var("NATIVE_MESSAGING_SECURITY_ENABLE_AUTHENTICATION") {
            self.enable_authentication = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_AUTHENTICATION失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_SECURITY_AUTHORIZED_EXTENSIONS") {
            self.authorized_extensions = val
                .split(',')
                .map(|s| s.trim().to_string())
                .filter(|s| !s.is_empty())
                .collect();
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_SECURITY_ENABLE_ENCRYPTION") {
            self.enable_encryption = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_ENCRYPTION失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_SECURITY_SESSION_TIMEOUT") {
            let seconds: u64 = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析SESSION_TIMEOUT失败: {}", e))
            })?;
            self.session_timeout = Duration::from_secs(seconds);
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_SECURITY_ENABLE_AUDIT_LOG") {
            self.enable_audit_log = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_AUDIT_LOG失败: {}", e))
            })?;
        }

        self.validate()?;
        Ok(self)
    }

    /// 验证安全配置
    pub fn validate(&self) -> Result<()> {
        if self.enable_authentication && self.authorized_extensions.is_empty() {
            return Err(NativeMessagingError::ConfigError(
                "启用身份验证时必须配置授权扩展列表".to_string(),
            ));
        }

        if self.session_timeout.is_zero() {
            return Err(NativeMessagingError::ConfigError(
                "会话超时时间必须大于0".to_string(),
            ));
        }

        Ok(())
    }
}

/// 监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    /// 是否启用性能监控
    pub enable_metrics: bool,
    /// 是否启用健康检查
    pub enable_health_check: bool,
    /// 指标收集间隔
    #[serde(with = "serde_duration")]
    pub metrics_interval: Duration,
    /// 是否启用告警系统
    pub enable_alerts: bool,
    /// 告警阈值配置
    pub alert_thresholds: AlertThresholds,
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            enable_metrics: true,
            enable_health_check: true,
            metrics_interval: Duration::from_secs(60), // 1分钟
            enable_alerts: true,
            alert_thresholds: AlertThresholds::default(),
        }
    }
}

impl MonitoringConfig {
    /// 从环境变量加载配置
    pub fn load_from_env() -> Result<Self> {
        let mut config = Self::default();

        if let Ok(val) = env::var("NATIVE_MESSAGING_MONITORING_ENABLE_METRICS") {
            config.enable_metrics = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_METRICS失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_MONITORING_ENABLE_HEALTH_CHECK") {
            config.enable_health_check = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_HEALTH_CHECK失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_MONITORING_METRICS_INTERVAL") {
            let seconds: u64 = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析METRICS_INTERVAL失败: {}", e))
            })?;
            config.metrics_interval = Duration::from_secs(seconds);
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_MONITORING_ENABLE_ALERTS") {
            config.enable_alerts = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_ALERTS失败: {}", e))
            })?;
        }

        // 加载告警阈值
        config.alert_thresholds = AlertThresholds::load_from_env()?;

        config.validate()?;
        Ok(config)
    }

    /// 合并环境变量覆盖
    pub fn merge_env_overrides(mut self) -> Result<Self> {
        if let Ok(val) = env::var("NATIVE_MESSAGING_MONITORING_ENABLE_METRICS") {
            self.enable_metrics = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_METRICS失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_MONITORING_ENABLE_HEALTH_CHECK") {
            self.enable_health_check = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_HEALTH_CHECK失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_MONITORING_METRICS_INTERVAL") {
            let seconds: u64 = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析METRICS_INTERVAL失败: {}", e))
            })?;
            self.metrics_interval = Duration::from_secs(seconds);
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_MONITORING_ENABLE_ALERTS") {
            self.enable_alerts = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ENABLE_ALERTS失败: {}", e))
            })?;
        }

        // 合并告警阈值的环境变量覆盖
        self.alert_thresholds = self.alert_thresholds.merge_env_overrides()?;

        self.validate()?;
        Ok(self)
    }

    /// 验证监控配置
    pub fn validate(&self) -> Result<()> {
        if self.metrics_interval.is_zero() {
            return Err(NativeMessagingError::ConfigError(
                "指标收集间隔必须大于0".to_string(),
            ));
        }

        self.alert_thresholds.validate()?;
        Ok(())
    }
}

/// 告警阈值配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertThresholds {
    /// CPU 使用率阈值 (百分比)
    pub cpu_threshold: f64,
    /// 内存使用阈值 (MB)
    pub memory_threshold: u64,
    /// 错误率阈值 (百分比)
    pub error_rate_threshold: f64,
    /// 响应时间阈值 (毫秒)
    pub response_time_threshold: u64,
}

impl Default for AlertThresholds {
    fn default() -> Self {
        Self {
            cpu_threshold: 80.0,           // 80%
            memory_threshold: 100,         // 100MB
            error_rate_threshold: 5.0,     // 5%
            response_time_threshold: 1000, // 1秒
        }
    }
}

impl AlertThresholds {
    /// 从环境变量加载配置
    pub fn load_from_env() -> Result<Self> {
        let mut thresholds = Self::default();

        if let Ok(val) = env::var("NATIVE_MESSAGING_ALERT_CPU_THRESHOLD") {
            thresholds.cpu_threshold = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析CPU_THRESHOLD失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_ALERT_MEMORY_THRESHOLD") {
            thresholds.memory_threshold = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析MEMORY_THRESHOLD失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_ALERT_ERROR_RATE_THRESHOLD") {
            thresholds.error_rate_threshold = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ERROR_RATE_THRESHOLD失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_ALERT_RESPONSE_TIME_THRESHOLD") {
            thresholds.response_time_threshold = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析RESPONSE_TIME_THRESHOLD失败: {}", e))
            })?;
        }

        thresholds.validate()?;
        Ok(thresholds)
    }

    /// 合并环境变量覆盖
    pub fn merge_env_overrides(mut self) -> Result<Self> {
        if let Ok(val) = env::var("NATIVE_MESSAGING_ALERT_CPU_THRESHOLD") {
            self.cpu_threshold = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析CPU_THRESHOLD失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_ALERT_MEMORY_THRESHOLD") {
            self.memory_threshold = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析MEMORY_THRESHOLD失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_ALERT_ERROR_RATE_THRESHOLD") {
            self.error_rate_threshold = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析ERROR_RATE_THRESHOLD失败: {}", e))
            })?;
        }

        if let Ok(val) = env::var("NATIVE_MESSAGING_ALERT_RESPONSE_TIME_THRESHOLD") {
            self.response_time_threshold = val.parse().map_err(|e| {
                NativeMessagingError::ConfigError(format!("解析RESPONSE_TIME_THRESHOLD失败: {}", e))
            })?;
        }

        self.validate()?;
        Ok(self)
    }

    /// 验证告警阈值配置
    pub fn validate(&self) -> Result<()> {
        if self.cpu_threshold <= 0.0 || self.cpu_threshold > 100.0 {
            return Err(NativeMessagingError::ConfigError(
                "CPU阈值必须在0-100之间".to_string(),
            ));
        }

        if self.error_rate_threshold < 0.0 || self.error_rate_threshold > 100.0 {
            return Err(NativeMessagingError::ConfigError(
                "错误率阈值必须在0-100之间".to_string(),
            ));
        }

        Ok(())
    }
}

/// 浏览器配置集合
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserConfigs {
    /// 各浏览器的具体配置
    pub browsers: HashMap<BrowserType, BrowserConfig>,
}

impl Default for BrowserConfigs {
    fn default() -> Self {
        let mut browsers = HashMap::new();
        browsers.insert(BrowserType::Chrome, BrowserConfig::chrome_default());
        browsers.insert(BrowserType::Firefox, BrowserConfig::firefox_default());
        browsers.insert(BrowserType::Edge, BrowserConfig::edge_default());
        browsers.insert(BrowserType::Safari, BrowserConfig::safari_default());

        Self { browsers }
    }
}

impl BrowserConfigs {
    /// 创建启用所有浏览器的配置
    pub fn all_enabled() -> Self {
        let mut browsers = HashMap::new();
        browsers.insert(BrowserType::Chrome, BrowserConfig::chrome_default());
        browsers.insert(BrowserType::Firefox, BrowserConfig::firefox_default());
        browsers.insert(BrowserType::Edge, BrowserConfig::edge_default());

        // Safari 也启用
        let mut safari_config = BrowserConfig::safari_default();
        safari_config.enabled = true;
        browsers.insert(BrowserType::Safari, safari_config);

        Self { browsers }
    }

    /// 从环境变量加载配置
    pub fn load_from_env() -> Result<Self> {
        let mut configs = Self::default();

        // 为每个浏览器加载环境变量配置
        for browser_type in [
            BrowserType::Chrome,
            BrowserType::Firefox,
            BrowserType::Edge,
            BrowserType::Safari,
        ] {
            let browser_name = format!("{:?}", browser_type).to_uppercase();

            if let Some(config) = configs.browsers.get_mut(&browser_type) {
                // 启用/禁用标志
                if let Ok(val) =
                    env::var(format!("NATIVE_MESSAGING_BROWSER_{}_ENABLED", browser_name))
                {
                    config.enabled = val.parse().map_err(|e| {
                        NativeMessagingError::ConfigError(format!(
                            "解析{}_ENABLED失败: {}",
                            browser_name, e
                        ))
                    })?;
                }

                // 连接超时
                if let Ok(val) = env::var(format!(
                    "NATIVE_MESSAGING_BROWSER_{}_CONNECTION_TIMEOUT",
                    browser_name
                )) {
                    let seconds: u64 = val.parse().map_err(|e| {
                        NativeMessagingError::ConfigError(format!(
                            "解析{}_CONNECTION_TIMEOUT失败: {}",
                            browser_name, e
                        ))
                    })?;
                    config.connection_timeout = Duration::from_secs(seconds);
                }

                // 最大重试次数
                if let Ok(val) = env::var(format!(
                    "NATIVE_MESSAGING_BROWSER_{}_MAX_RETRIES",
                    browser_name
                )) {
                    config.max_retries = val.parse().map_err(|e| {
                        NativeMessagingError::ConfigError(format!(
                            "解析{}_MAX_RETRIES失败: {}",
                            browser_name, e
                        ))
                    })?;
                }

                // 重试间隔
                if let Ok(val) = env::var(format!(
                    "NATIVE_MESSAGING_BROWSER_{}_RETRY_INTERVAL",
                    browser_name
                )) {
                    let seconds: u64 = val.parse().map_err(|e| {
                        NativeMessagingError::ConfigError(format!(
                            "解析{}_RETRY_INTERVAL失败: {}",
                            browser_name, e
                        ))
                    })?;
                    config.retry_interval = Duration::from_secs(seconds);
                }
            }
        }

        configs.validate()?;
        Ok(configs)
    }

    /// 合并环境变量覆盖
    pub fn merge_env_overrides(mut self) -> Result<Self> {
        // 为每个浏览器合并环境变量配置
        for browser_type in [
            BrowserType::Chrome,
            BrowserType::Firefox,
            BrowserType::Edge,
            BrowserType::Safari,
        ] {
            let browser_name = format!("{:?}", browser_type).to_uppercase();

            if let Some(config) = self.browsers.get_mut(&browser_type) {
                // 启用/禁用标志
                if let Ok(val) =
                    env::var(format!("NATIVE_MESSAGING_BROWSER_{}_ENABLED", browser_name))
                {
                    config.enabled = val.parse().map_err(|e| {
                        NativeMessagingError::ConfigError(format!(
                            "解析{}_ENABLED失败: {}",
                            browser_name, e
                        ))
                    })?;
                }

                // 连接超时
                if let Ok(val) = env::var(format!(
                    "NATIVE_MESSAGING_BROWSER_{}_CONNECTION_TIMEOUT",
                    browser_name
                )) {
                    let seconds: u64 = val.parse().map_err(|e| {
                        NativeMessagingError::ConfigError(format!(
                            "解析{}_CONNECTION_TIMEOUT失败: {}",
                            browser_name, e
                        ))
                    })?;
                    config.connection_timeout = Duration::from_secs(seconds);
                }

                // 最大重试次数
                if let Ok(val) = env::var(format!(
                    "NATIVE_MESSAGING_BROWSER_{}_MAX_RETRIES",
                    browser_name
                )) {
                    config.max_retries = val.parse().map_err(|e| {
                        NativeMessagingError::ConfigError(format!(
                            "解析{}_MAX_RETRIES失败: {}",
                            browser_name, e
                        ))
                    })?;
                }

                // 重试间隔
                if let Ok(val) = env::var(format!(
                    "NATIVE_MESSAGING_BROWSER_{}_RETRY_INTERVAL",
                    browser_name
                )) {
                    let seconds: u64 = val.parse().map_err(|e| {
                        NativeMessagingError::ConfigError(format!(
                            "解析{}_RETRY_INTERVAL失败: {}",
                            browser_name, e
                        ))
                    })?;
                    config.retry_interval = Duration::from_secs(seconds);
                }
            }
        }

        self.validate()?;
        Ok(self)
    }

    /// 验证浏览器配置
    pub fn validate(&self) -> Result<()> {
        for (browser_type, config) in &self.browsers {
            config.validate(*browser_type)?;
        }
        Ok(())
    }
}

/// 浏览器类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum BrowserType {
    Chrome,
    Firefox,
    Edge,
    Safari,
}

/// 浏览器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserConfig {
    /// 是否启用此浏览器支持
    pub enabled: bool,
    /// 连接超时时间
    #[serde(with = "serde_duration")]
    pub connection_timeout: Duration,
    /// 最大重试次数
    pub max_retries: u32,
    /// 重试间隔
    #[serde(with = "serde_duration")]
    pub retry_interval: Duration,
}

impl BrowserConfig {
    /// Chrome 默认配置
    pub fn chrome_default() -> Self {
        Self {
            enabled: true,
            connection_timeout: Duration::from_secs(10),
            max_retries: 3,
            retry_interval: Duration::from_secs(1),
        }
    }

    /// Firefox 默认配置
    pub fn firefox_default() -> Self {
        Self {
            enabled: true,
            connection_timeout: Duration::from_secs(10),
            max_retries: 3,
            retry_interval: Duration::from_secs(1),
        }
    }

    /// Edge 默认配置
    pub fn edge_default() -> Self {
        Self {
            enabled: true,
            connection_timeout: Duration::from_secs(10),
            max_retries: 3,
            retry_interval: Duration::from_secs(1),
        }
    }

    /// Safari 默认配置
    pub fn safari_default() -> Self {
        Self {
            enabled: false, // 默认关闭，因为 Safari 支持较复杂
            connection_timeout: Duration::from_secs(15),
            max_retries: 2,
            retry_interval: Duration::from_secs(2),
        }
    }

    /// 验证浏览器配置
    pub fn validate(&self, browser_type: BrowserType) -> Result<()> {
        if self.connection_timeout.is_zero() {
            return Err(NativeMessagingError::ConfigError(format!(
                "{:?} 连接超时时间必须大于0",
                browser_type
            )));
        }

        if self.retry_interval.is_zero() {
            return Err(NativeMessagingError::ConfigError(format!(
                "{:?} 重试间隔必须大于0",
                browser_type
            )));
        }

        Ok(())
    }
}

/// Duration 序列化/反序列化辅助模块
mod serde_duration {
    use serde::{Deserialize, Deserializer, Serializer};
    use std::time::Duration;

    pub fn serialize<S>(duration: &Duration, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        serializer.serialize_u64(duration.as_secs())
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Duration, D::Error>
    where
        D: Deserializer<'de>,
    {
        let secs = u64::deserialize(deserializer)?;
        Ok(Duration::from_secs(secs))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config_validation() {
        let config = NativeMessagingConfig::default();
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_listener_config_validation() {
        let mut config = ListenerConfig::default();

        // 测试无效的最大连接数
        config.max_connections = 0;
        assert!(config.validate().is_err());

        // 恢复有效值
        config.max_connections = 100;
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_security_config_validation() {
        let mut config = SecurityConfig::default();
        config.enable_authentication = true;
        config.authorized_extensions.clear();

        // 启用认证但没有授权扩展应该失败
        assert!(config.validate().is_err());

        // 添加授权扩展后应该成功
        config
            .authorized_extensions
            .push("test-extension".to_string());
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_browser_config_validation() {
        let config = BrowserConfig::chrome_default();
        assert!(config.validate(BrowserType::Chrome).is_ok());

        let mut invalid_config = BrowserConfig::chrome_default();
        invalid_config.connection_timeout = Duration::from_secs(0);
        assert!(invalid_config.validate(BrowserType::Chrome).is_err());
    }

    #[test]
    fn test_config_serialization() {
        let config = NativeMessagingConfig::default();
        let json = serde_json::to_string(&config);
        assert!(json.is_ok());

        let deserialized: std::result::Result<NativeMessagingConfig, _> =
            serde_json::from_str(&json.unwrap());
        assert!(deserialized.is_ok());
    }

    #[test]
    fn test_config_builder() {
        let config = NativeMessagingConfig::builder()
            .max_connections(500)
            .message_timeout(Duration::from_secs(60))
            .enable_authentication(true)
            .authorized_extensions(vec!["test-ext".to_string()])
            .enable_metrics(true)
            .enable_all_browsers()
            .build()
            .unwrap();

        assert_eq!(config.listener.max_connections, 500);
        assert_eq!(config.listener.message_timeout, Duration::from_secs(60));
        assert!(config.security.enable_authentication);
        assert_eq!(config.security.authorized_extensions, vec!["test-ext"]);
        assert!(config.monitoring.enable_metrics);

        // 验证所有浏览器都启用
        for browser_config in config.browsers.browsers.values() {
            assert!(browser_config.enabled);
        }
    }

    #[test]
    fn test_env_var_parsing() {
        // 保存当前环境变量状态
        let saved_vars = [
            (
                "NATIVE_MESSAGING_LISTENER_MAX_CONNECTIONS",
                std::env::var("NATIVE_MESSAGING_LISTENER_MAX_CONNECTIONS").ok(),
            ),
            (
                "NATIVE_MESSAGING_SECURITY_ENABLE_AUTHENTICATION",
                std::env::var("NATIVE_MESSAGING_SECURITY_ENABLE_AUTHENTICATION").ok(),
            ),
            (
                "NATIVE_MESSAGING_SECURITY_AUTHORIZED_EXTENSIONS",
                std::env::var("NATIVE_MESSAGING_SECURITY_AUTHORIZED_EXTENSIONS").ok(),
            ),
            (
                "NATIVE_MESSAGING_MONITORING_ENABLE_METRICS",
                std::env::var("NATIVE_MESSAGING_MONITORING_ENABLE_METRICS").ok(),
            ),
            (
                "NATIVE_MESSAGING_ALERT_CPU_THRESHOLD",
                std::env::var("NATIVE_MESSAGING_ALERT_CPU_THRESHOLD").ok(),
            ),
            (
                "NATIVE_MESSAGING_BROWSER_CHROME_ENABLED",
                std::env::var("NATIVE_MESSAGING_BROWSER_CHROME_ENABLED").ok(),
            ),
        ];

        // 清理当前环境变量
        for (var_name, _) in &saved_vars {
            std::env::remove_var(var_name);
        }

        // 设置测试环境变量
        std::env::set_var("NATIVE_MESSAGING_LISTENER_MAX_CONNECTIONS", "1000");
        std::env::set_var("NATIVE_MESSAGING_SECURITY_ENABLE_AUTHENTICATION", "true");
        std::env::set_var(
            "NATIVE_MESSAGING_SECURITY_AUTHORIZED_EXTENSIONS",
            "ext1,ext2,ext3",
        );
        std::env::set_var("NATIVE_MESSAGING_MONITORING_ENABLE_METRICS", "false");
        std::env::set_var("NATIVE_MESSAGING_ALERT_CPU_THRESHOLD", "90.0");
        std::env::set_var("NATIVE_MESSAGING_BROWSER_CHROME_ENABLED", "false");

        // 从环境变量加载配置
        let config = NativeMessagingConfig::load_from_env().unwrap();

        assert_eq!(config.listener.max_connections, 1000);
        assert!(config.security.enable_authentication);
        assert_eq!(
            config.security.authorized_extensions,
            vec!["ext1", "ext2", "ext3"]
        );
        assert!(!config.monitoring.enable_metrics);
        assert_eq!(config.monitoring.alert_thresholds.cpu_threshold, 90.0);
        assert!(!config.browsers.browsers[&BrowserType::Chrome].enabled);

        // 恢复原始环境变量状态
        for (var_name, var_value) in saved_vars {
            if let Some(value) = var_value {
                std::env::set_var(var_name, value);
            } else {
                std::env::remove_var(var_name);
            }
        }
    }

    #[test]
    fn test_env_merge_overrides() {
        // 保存当前环境变量状态
        let saved_vars = [
            (
                "NATIVE_MESSAGING_LISTENER_MAX_CONNECTIONS",
                std::env::var("NATIVE_MESSAGING_LISTENER_MAX_CONNECTIONS").ok(),
            ),
            (
                "NATIVE_MESSAGING_SECURITY_ENABLE_AUTHENTICATION",
                std::env::var("NATIVE_MESSAGING_SECURITY_ENABLE_AUTHENTICATION").ok(),
            ),
            (
                "NATIVE_MESSAGING_SECURITY_AUTHORIZED_EXTENSIONS",
                std::env::var("NATIVE_MESSAGING_SECURITY_AUTHORIZED_EXTENSIONS").ok(),
            ),
        ];

        // 清理当前环境变量
        for (var_name, _) in &saved_vars {
            std::env::remove_var(var_name);
        }

        // 创建基础配置
        let mut config = NativeMessagingConfig::default();
        config.listener.max_connections = 100;
        config.security.enable_authentication = false;

        // 设置环境变量覆盖（启用认证时必须同时设置授权扩展）
        std::env::set_var("NATIVE_MESSAGING_LISTENER_MAX_CONNECTIONS", "500");
        std::env::set_var("NATIVE_MESSAGING_SECURITY_ENABLE_AUTHENTICATION", "true");
        std::env::set_var(
            "NATIVE_MESSAGING_SECURITY_AUTHORIZED_EXTENSIONS",
            "test-ext1,test-ext2",
        );

        // 合并环境变量覆盖
        let merged_config = config.merge_env_overrides().unwrap();

        assert_eq!(merged_config.listener.max_connections, 500);
        assert!(merged_config.security.enable_authentication);
        assert_eq!(
            merged_config.security.authorized_extensions,
            vec!["test-ext1", "test-ext2"]
        );

        // 恢复原始环境变量状态
        for (var_name, var_value) in saved_vars {
            if let Some(value) = var_value {
                std::env::set_var(var_name, value);
            } else {
                std::env::remove_var(var_name);
            }
        }
    }

    #[test]
    fn test_browser_configs_all_enabled() {
        let configs = BrowserConfigs::all_enabled();

        // 验证所有浏览器都启用
        for browser_config in configs.browsers.values() {
            assert!(browser_config.enabled);
        }

        // 验证包含所有支持的浏览器
        assert!(configs.browsers.contains_key(&BrowserType::Chrome));
        assert!(configs.browsers.contains_key(&BrowserType::Firefox));
        assert!(configs.browsers.contains_key(&BrowserType::Edge));
        assert!(configs.browsers.contains_key(&BrowserType::Safari));
    }

    #[test]
    fn test_alert_thresholds_validation() {
        let mut thresholds = AlertThresholds::default();

        // 测试有效阈值
        assert!(thresholds.validate().is_ok());

        // 测试无效CPU阈值
        thresholds.cpu_threshold = 150.0;
        assert!(thresholds.validate().is_err());

        thresholds.cpu_threshold = -10.0;
        assert!(thresholds.validate().is_err());

        // 恢复有效值
        thresholds.cpu_threshold = 80.0;
        assert!(thresholds.validate().is_ok());

        // 测试无效错误率阈值
        thresholds.error_rate_threshold = 150.0;
        assert!(thresholds.validate().is_err());

        thresholds.error_rate_threshold = -5.0;
        assert!(thresholds.validate().is_err());
    }

    #[test]
    fn test_config_validation_comprehensive() {
        // 测试完全有效的配置
        let config = NativeMessagingConfig::builder()
            .max_connections(1000)
            .message_timeout(Duration::from_secs(30))
            .enable_authentication(true)
            .authorized_extensions(vec!["valid-extension".to_string()])
            .build()
            .unwrap();

        assert!(config.validate().is_ok());

        // 测试无效配置 - 启用认证但无授权扩展
        let invalid_config = NativeMessagingConfig::builder()
            .enable_authentication(true)
            .authorized_extensions(vec![])
            .build();

        assert!(invalid_config.is_err());
    }

    #[tokio::test]
    async fn test_config_manager() {
        let config = NativeMessagingConfig::default();
        let manager = ConfigManager::new(config.clone());

        // 测试获取配置
        let retrieved_config = manager.get_config().await;
        assert_eq!(
            retrieved_config.listener.max_connections,
            config.listener.max_connections
        );

        // 测试更新配置
        let new_config = NativeMessagingConfig::builder()
            .max_connections(2000)
            .build()
            .unwrap();

        assert!(manager.update_config(new_config.clone()).await.is_ok());

        let updated_config = manager.get_config().await;
        assert_eq!(updated_config.listener.max_connections, 2000);
    }

    #[test]
    fn test_invalid_env_vars() {
        // 清理可能存在的环境变量（防止测试间干扰）
        std::env::remove_var("NATIVE_MESSAGING_LISTENER_MAX_CONNECTIONS");
        std::env::remove_var("NATIVE_MESSAGING_SECURITY_ENABLE_AUTHENTICATION");

        // 测试无效的环境变量值
        std::env::set_var("NATIVE_MESSAGING_LISTENER_MAX_CONNECTIONS", "invalid");

        let result = ListenerConfig::load_from_env();
        assert!(result.is_err());

        std::env::remove_var("NATIVE_MESSAGING_LISTENER_MAX_CONNECTIONS");

        // 测试无效的布尔值
        std::env::set_var("NATIVE_MESSAGING_SECURITY_ENABLE_AUTHENTICATION", "maybe");

        let result = SecurityConfig::load_from_env();
        assert!(result.is_err());

        std::env::remove_var("NATIVE_MESSAGING_SECURITY_ENABLE_AUTHENTICATION");
    }
}
