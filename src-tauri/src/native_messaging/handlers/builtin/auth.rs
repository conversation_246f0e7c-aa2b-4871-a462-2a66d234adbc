//! Native Messaging 认证处理器
//!
//! 提供用户认证和权限验证功能

use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    handlers::MessageHandler,
    protocol::message::{NativeMessage, OutgoingMessage},
};
use async_trait::async_trait;
use serde_json;
use std::collections::HashSet;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use uuid::Uuid;

/// 认证会话
#[derive(Debug, Clone)]
pub struct AuthSession {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: String,
    /// 权限列表
    pub permissions: HashSet<String>,
    /// 创建时间
    pub created_at: SystemTime,
    /// 过期时间
    pub expires_at: SystemTime,
    /// 是否活跃
    pub is_active: bool,
}

impl AuthSession {
    /// 创建新的认证会话
    pub fn new(user_id: String, permissions: HashSet<String>, duration: Duration) -> Self {
        let now = SystemTime::now();
        Self {
            session_id: Uuid::new_v4().to_string(),
            user_id,
            permissions,
            created_at: now,
            expires_at: now + duration,
            is_active: true,
        }
    }

    /// 检查会话是否有效
    pub fn is_valid(&self) -> bool {
        self.is_active && SystemTime::now() < self.expires_at
    }

    /// 检查权限
    pub fn has_permission(&self, permission: &str) -> bool {
        self.permissions.contains(permission) || self.permissions.contains("*")
    }
}

/// 认证处理器
///
/// 处理用户认证、会话管理和权限验证
pub struct AuthHandler {
    /// 活跃会话
    sessions: std::sync::RwLock<std::collections::HashMap<String, AuthSession>>,
    /// 默认会话持续时间
    default_session_duration: Duration,
}

impl AuthHandler {
    /// 创建新的认证处理器
    pub fn new() -> Self {
        Self {
            sessions: std::sync::RwLock::new(std::collections::HashMap::new()),
            default_session_duration: Duration::from_secs(3600), // 1小时
        }
    }

    /// 创建认证会话
    fn create_session(&self, user_id: String, permissions: HashSet<String>) -> AuthSession {
        let session = AuthSession::new(user_id, permissions, self.default_session_duration);
        
        // 存储会话
        if let Ok(mut sessions) = self.sessions.write() {
            sessions.insert(session.session_id.clone(), session.clone());
        }
        
        session
    }

    /// 验证会话
    fn validate_session(&self, session_id: &str) -> Option<AuthSession> {
        if let Ok(sessions) = self.sessions.read() {
            if let Some(session) = sessions.get(session_id) {
                if session.is_valid() {
                    return Some(session.clone());
                }
            }
        }
        None
    }

    /// 撤销会话
    fn revoke_session(&self, session_id: &str) -> bool {
        if let Ok(mut sessions) = self.sessions.write() {
            sessions.remove(session_id).is_some()
        } else {
            false
        }
    }

    /// 处理登录请求
    async fn handle_login(&self, message: &NativeMessage) -> Result<OutgoingMessage> {
        let username = message.payload.get("username")
            .and_then(|v| v.as_str())
            .ok_or_else(|| NativeMessagingError::HandlerError("缺少用户名".to_string()))?;

        let password = message.payload.get("password")
            .and_then(|v| v.as_str())
            .ok_or_else(|| NativeMessagingError::HandlerError("缺少密码".to_string()))?;

        // 简单的演示认证逻辑（实际应用中应连接真正的认证系统）
        if self.authenticate_user(username, password) {
            let permissions = self.get_user_permissions(username);
            let session = self.create_session(username.to_string(), permissions);

            let response = serde_json::json!({
                "type": "login_success",
                "session_id": session.session_id,
                "user_id": session.user_id,
                "permissions": session.permissions.iter().collect::<Vec<_>>(),
                "expires_at": session.expires_at.duration_since(UNIX_EPOCH).unwrap().as_secs(),
            });

            Ok(OutgoingMessage::success(message.request_id.clone(), response))
        } else {
            Err(NativeMessagingError::SecurityError("认证失败".to_string()))
        }
    }

    /// 处理注销请求
    async fn handle_logout(&self, message: &NativeMessage) -> Result<OutgoingMessage> {
        let session_id = message.payload.get("session_id")
            .and_then(|v| v.as_str())
            .unwrap_or("");

        let success = self.revoke_session(session_id);

        let response = serde_json::json!({
            "type": "logout_result",
            "success": success,
        });

        Ok(OutgoingMessage::success(message.request_id.clone(), response))
    }

    /// 处理会话验证请求
    async fn handle_validate(&self, message: &NativeMessage) -> Result<OutgoingMessage> {
        let session_id = message.payload.get("session_id")
            .and_then(|v| v.as_str())
            .unwrap_or("");

        if let Some(session) = self.validate_session(session_id) {
            let response = serde_json::json!({
                "type": "session_valid",
                "session_id": session.session_id,
                "user_id": session.user_id,
                "permissions": session.permissions.iter().collect::<Vec<_>>(),
                "expires_at": session.expires_at.duration_since(UNIX_EPOCH).unwrap().as_secs(),
            });

            Ok(OutgoingMessage::success(message.request_id.clone(), response))
        } else {
            Err(NativeMessagingError::SecurityError("会话无效".to_string()))
        }
    }

    /// 简单的用户认证（演示用）
    fn authenticate_user(&self, username: &str, password: &str) -> bool {
        // 演示认证逻辑 - 实际应用中应连接真正的认证系统
        match username {
            "admin" => password == "admin123",
            "user" => password == "user123",
            _ => false,
        }
    }

    /// 获取用户权限（演示用）
    fn get_user_permissions(&self, username: &str) -> HashSet<String> {
        let mut permissions = HashSet::new();
        match username {
            "admin" => {
                permissions.insert("*".to_string()); // 管理员有所有权限
            }
            "user" => {
                permissions.insert("read".to_string());
                permissions.insert("write".to_string());
            }
            _ => {
                permissions.insert("read".to_string()); // 默认只读权限
            }
        }
        permissions
    }
}

#[async_trait]
impl MessageHandler for AuthHandler {
    async fn handle(&self, message: NativeMessage) -> Result<OutgoingMessage> {
        let action = message.payload.get("action")
            .and_then(|v| v.as_str())
            .unwrap_or("login");

        match action {
            "login" => self.handle_login(&message).await,
            "logout" => self.handle_logout(&message).await,
            "validate" => self.handle_validate(&message).await,
            _ => Err(NativeMessagingError::HandlerError(
                format!("不支持的认证操作: {}", action)
            )),
        }
    }

    fn message_types(&self) -> Vec<String> {
        vec!["Auth".to_string(), "Authentication".to_string()]
    }

    fn name(&self) -> &str {
        "AuthHandler"
    }
}

impl Default for AuthHandler {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;

    #[tokio::test]
    async fn test_auth_login_success() {
        let handler = AuthHandler::new();
        let message = NativeMessage::new(
            MessageType::Auth,
            "test-login".to_string(),
            serde_json::json!({
                "action": "login",
                "username": "admin",
                "password": "admin123"
            }),
            "test-source".to_string(),
        );

        let result = handler.handle(message).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        let payload = &response.message.payload;
        assert_eq!(payload["type"], "login_success");
        assert!(payload["session_id"].is_string());
        assert_eq!(payload["user_id"], "admin");
    }

    #[tokio::test]
    async fn test_auth_login_failure() {
        let handler = AuthHandler::new();
        let message = NativeMessage::new(
            MessageType::Auth,
            "test-login-fail".to_string(),
            serde_json::json!({
                "action": "login",
                "username": "admin",
                "password": "wrongpassword"
            }),
            "test-source".to_string(),
        );

        let result = handler.handle(message).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_auth_session_validation() {
        let handler = AuthHandler::new();
        
        // 先登录
        let login_message = NativeMessage::new(
            MessageType::Auth,
            "test-login".to_string(),
            serde_json::json!({
                "action": "login",
                "username": "user",
                "password": "user123"
            }),
            "test-source".to_string(),
        );

        let login_result = handler.handle(login_message).await.unwrap();
        let session_id = login_result.message.payload["session_id"].as_str().unwrap();

        // 验证会话
        let validate_message = NativeMessage::new(
            MessageType::Auth,
            "test-validate".to_string(),
            serde_json::json!({
                "action": "validate",
                "session_id": session_id
            }),
            "test-source".to_string(),
        );

        let result = handler.handle(validate_message).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        assert_eq!(response.message.payload["type"], "session_valid");
    }

    #[test]
    fn test_auth_session() {
        let mut permissions = HashSet::new();
        permissions.insert("read".to_string());
        permissions.insert("write".to_string());

        let session = AuthSession::new(
            "test_user".to_string(),
            permissions,
            Duration::from_secs(3600),
        );

        assert!(session.is_valid());
        assert!(session.has_permission("read"));
        assert!(session.has_permission("write"));
        assert!(!session.has_permission("admin"));
    }

    #[test]
    fn test_auth_handler_interface() {
        let handler = AuthHandler::new();
        assert_eq!(handler.name(), "AuthHandler");
        assert_eq!(handler.message_types(), vec!["Auth".to_string(), "Authentication".to_string()]);
    }
} 