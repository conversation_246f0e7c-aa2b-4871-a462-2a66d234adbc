//! Native Messaging 内置处理器
//!
//! 提供常用的内置消息处理器

pub mod auth;
pub mod health;
pub mod ping;
pub mod password;
pub mod version;

// 公共导出
pub use auth::AuthHandler;
pub use health::HealthCheckHandler;
pub use password::PasswordHandler;
pub use ping::Ping<PERSON>andler;
pub use version::VersionHandler;

use crate::native_messaging::handlers::MessageHandler;
use std::sync::Arc;

/// 内置处理器工厂
///
/// 提供创建内置处理器的便捷方法
pub struct BuiltinHandlerFactory;

impl BuiltinHandlerFactory {
    /// 创建健康检查处理器
    pub fn create_health_check_handler() -> HealthCheckHandler {
        HealthCheckHandler::new()
    }

    /// 创建认证处理器
    pub fn create_auth_handler() -> AuthHandler {
        AuthHandler::new()
    }

    /// 创建Ping处理器
    pub fn create_ping_handler() -> PingHandler {
        PingHandler::new()
    }

    /// 创建密码管理处理器
    pub fn create_password_handler() -> PasswordHandler {
        PasswordHandler::new()
    }

    /// 创建版本信息处理器
    pub fn create_version_handler() -> VersionHandler {
        VersionHandler::new()
    }

    /// 创建所有内置处理器
    ///
    /// # 返回
    /// Vec<Arc<dyn MessageHandler>> - 所有内置处理器
    pub fn create_all_builtin_handlers() -> Vec<Arc<dyn MessageHandler>> {
        vec![
            Arc::new(Self::create_health_check_handler()),
            Arc::new(Self::create_auth_handler()),
            Arc::new(Self::create_ping_handler()),
            Arc::new(Self::create_password_handler()),
            Arc::new(Self::create_version_handler()),
        ]
    }

    /// 批量注册所有内置处理器
    ///
    /// # 参数
    /// - `registry`: 处理器注册表
    ///
    /// # 返回
    /// usize - 成功注册的处理器数量
    pub async fn register_all_builtin_handlers(
        registry: &crate::native_messaging::handlers::HandlerRegistry,
    ) -> usize {
        let handlers = Self::create_all_builtin_handlers();
        let mut registered_count = 0;

        for handler in handlers {
            if registry.register(handler).await.is_ok() {
                registered_count += 1;
            }
        }

        registered_count
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::handlers::MessageHandler;

    #[test]
    fn test_builtin_factory() {
        let handler = BuiltinHandlerFactory::create_health_check_handler();
        assert_eq!(handler.name(), "HealthCheckHandler");

        let auth_handler = BuiltinHandlerFactory::create_auth_handler();
        assert_eq!(auth_handler.name(), "AuthHandler");

        let ping_handler = BuiltinHandlerFactory::create_ping_handler();
        assert_eq!(ping_handler.name(), "PingHandler");
    }

    #[test]
    fn test_all_builtin_handlers() {
        let handlers = BuiltinHandlerFactory::create_all_builtin_handlers();
        assert_eq!(handlers.len(), 5);

        let handler_names: Vec<&str> = handlers.iter().map(|h| h.name()).collect();
        assert!(handler_names.contains(&"HealthCheckHandler"));
        assert!(handler_names.contains(&"AuthHandler"));
        assert!(handler_names.contains(&"PingHandler"));
        assert!(handler_names.contains(&"PasswordHandler"));
        assert!(handler_names.contains(&"VersionHandler"));
    }
}
