//! Native Messaging Ping处理器
//!
//! 提供连接测试和响应时间测量功能

use crate::native_messaging::{
    error::Result,
    handlers::MessageHandler,
    protocol::message::{NativeMessage, OutgoingMessage},
};
use async_trait::async_trait;
use serde_json;
use std::time::Instant;

/// Ping处理器
///
/// 处理连接测试请求，返回pong响应和响应时间
pub struct PingHandler {
    /// 创建时间
    created_at: Instant,
}

impl PingHandler {
    /// 创建新的Ping处理器
    pub fn new() -> Self {
        Self {
            created_at: Instant::now(),
        }
    }
}

#[async_trait]
impl MessageHandler for PingHandler {
    async fn handle(&self, message: NativeMessage) -> Result<OutgoingMessage> {
        let start_time = Instant::now();
        
        // 解析ping消息的payload（如果有）
        let ping_data = message.payload.get("data").and_then(|v| v.as_str());
        
        // 计算响应时间
        let response_time_ms = start_time.elapsed().as_millis();
        
        // 返回pong响应
        let pong_info = serde_json::json!({
            "type": "pong",
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "response_time_ms": response_time_ms,
            "uptime_ms": self.created_at.elapsed().as_millis(),
            "echo_data": ping_data,
            "status": "ok"
        });

        Ok(OutgoingMessage::success(message.request_id, pong_info))
    }

    fn message_types(&self) -> Vec<String> {
        vec!["Ping".to_string()]
    }

    fn name(&self) -> &str {
        "PingHandler"
    }
}

impl Default for PingHandler {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;

    #[tokio::test]
    async fn test_ping_handler() {
        let handler = PingHandler::new();
        let message = NativeMessage::new(
            MessageType::Ping,
            "test-ping".to_string(),
            serde_json::json!({"data": "test-echo"}),
            "test-source".to_string(),
        );

        let result = handler.handle(message).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        assert_eq!(response.message.request_id, "test-ping");
        
        let payload = &response.message.payload;
        assert_eq!(payload["type"], "pong");
        assert_eq!(payload["status"], "ok");
        assert_eq!(payload["echo_data"], "test-echo");
        assert!(payload["response_time_ms"].is_number());
        assert!(payload["uptime_ms"].is_number());
    }

    #[tokio::test]
    async fn test_ping_without_data() {
        let handler = PingHandler::new();
        let message = NativeMessage::new(
            MessageType::Ping,
            "test-ping-empty".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );

        let result = handler.handle(message).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        let payload = &response.message.payload;
        assert!(payload["echo_data"].is_null());
    }

    #[test]
    fn test_ping_handler_interface() {
        let handler = PingHandler::new();
        assert_eq!(handler.name(), "PingHandler");
        assert_eq!(handler.message_types(), vec!["Ping".to_string()]);
    }
} 