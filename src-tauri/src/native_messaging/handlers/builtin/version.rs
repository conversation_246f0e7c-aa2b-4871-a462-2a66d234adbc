//! Native Messaging 版本信息处理器
//!
//! 提供应用程序版本和系统信息查询功能

use crate::native_messaging::{
    error::Result,
    handlers::MessageHandler,
    protocol::message::{NativeMessage, OutgoingMessage},
};
use async_trait::async_trait;
use serde_json;
use std::env;

/// 版本信息处理器
///
/// 处理版本信息查询请求，返回应用版本、系统信息等
pub struct VersionHandler;

impl VersionHandler {
    /// 创建新的版本信息处理器
    pub fn new() -> Self {
        Self
    }

    /// 获取系统信息
    fn get_system_info() -> serde_json::Value {
        serde_json::json!({
            "os": env::consts::OS,
            "arch": env::consts::ARCH,
            "family": env::consts::FAMILY,
            "platform": std::env::consts::OS,
            "rust_version": std::env::var("RUSTC_VERSION").unwrap_or_else(|_| "unknown".to_string()),
            "target": std::env::var("TARGET").unwrap_or_else(|_| "unknown".to_string()),
        })
    }

    /// 获取应用信息
    fn get_app_info() -> serde_json::Value {
        serde_json::json!({
            "name": env!("CARGO_PKG_NAME"),
            "version": env!("CARGO_PKG_VERSION"),
            "description": env!("CARGO_PKG_DESCRIPTION"),
            "authors": env!("CARGO_PKG_AUTHORS"),
            "repository": env!("CARGO_PKG_REPOSITORY"),
        })
    }

    /// 获取运行时信息
    fn get_runtime_info() -> serde_json::Value {
        let start_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        serde_json::json!({
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "start_time": start_time,
            "process_id": std::process::id(),
        })
    }
}

#[async_trait]
impl MessageHandler for VersionHandler {
    async fn handle(&self, message: NativeMessage) -> Result<OutgoingMessage> {
        // 检查请求的信息类型
        let info_type = message.payload.get("type")
            .and_then(|v| v.as_str())
            .unwrap_or("all");

        let version_info = match info_type {
            "app" => serde_json::json!({
                "type": "version_info",
                "app": Self::get_app_info(),
            }),
            "system" => serde_json::json!({
                "type": "version_info",
                "system": Self::get_system_info(),
            }),
            "runtime" => serde_json::json!({
                "type": "version_info",
                "runtime": Self::get_runtime_info(),
            }),
            _ => serde_json::json!({
                "type": "version_info",
                "app": Self::get_app_info(),
                "system": Self::get_system_info(),
                "runtime": Self::get_runtime_info(),
            }),
        };

        Ok(OutgoingMessage::success(message.request_id, version_info))
    }

    fn message_types(&self) -> Vec<String> {
        vec!["Version".to_string(), "VersionInfo".to_string()]
    }

    fn name(&self) -> &str {
        "VersionHandler"
    }
}

impl Default for VersionHandler {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;

    #[tokio::test]
    async fn test_version_handler_all_info() {
        let handler = VersionHandler::new();
        let message = NativeMessage::new(
            MessageType::Version,
            "test-version".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );

        let result = handler.handle(message).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        assert_eq!(response.message.request_id, "test-version");
        
        let payload = &response.message.payload;
        assert_eq!(payload["type"], "version_info");
        assert!(payload["app"].is_object());
        assert!(payload["system"].is_object());
        assert!(payload["runtime"].is_object());
    }

    #[tokio::test]
    async fn test_version_handler_app_only() {
        let handler = VersionHandler::new();
        let message = NativeMessage::new(
            MessageType::Version,
            "test-version-app".to_string(),
            serde_json::json!({"type": "app"}),
            "test-source".to_string(),
        );

        let result = handler.handle(message).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        let payload = &response.message.payload;
        assert_eq!(payload["type"], "version_info");
        assert!(payload["app"].is_object());
        assert!(payload["system"].is_null());
        assert!(payload["runtime"].is_null());
    }

    #[tokio::test]
    async fn test_version_handler_system_only() {
        let handler = VersionHandler::new();
        let message = NativeMessage::new(
            MessageType::Version,
            "test-version-system".to_string(),
            serde_json::json!({"type": "system"}),
            "test-source".to_string(),
        );

        let result = handler.handle(message).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        let payload = &response.message.payload;
        assert_eq!(payload["type"], "version_info");
        assert!(payload["system"].is_object());
        assert!(payload["app"].is_null());
        assert!(payload["runtime"].is_null());
    }

    #[test]
    fn test_version_handler_interface() {
        let handler = VersionHandler::new();
        assert_eq!(handler.name(), "VersionHandler");
        assert_eq!(handler.message_types(), vec!["Version".to_string(), "VersionInfo".to_string()]);
    }

    #[test]
    fn test_system_info() {
        let info = VersionHandler::get_system_info();
        assert!(info["os"].is_string());
        assert!(info["arch"].is_string());
        assert!(info["family"].is_string());
    }

    #[test]
    fn test_app_info() {
        let info = VersionHandler::get_app_info();
        assert!(info["name"].is_string());
        assert!(info["version"].is_string());
        assert!(info["description"].is_string());
    }
} 