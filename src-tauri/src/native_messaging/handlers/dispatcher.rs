//! Native Messaging 消息分发器
//!
//! 提供消息分发和处理流程管理功能

use super::{HandlerRegistry, MiddlewareChain};
use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::{NativeMessage, OutgoingMessage},
};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

/// 消息分发器
///
/// 负责将消息分发给合适的处理器，并支持中间件处理
pub struct MessageDispatcher {
    /// 处理器注册表
    registry: Arc<RwLock<HandlerRegistry>>,
    /// 中间件链
    middleware_chain: Arc<RwLock<MiddlewareChain>>,
}

impl MessageDispatcher {
    /// 创建新的消息分发器
    ///
    /// # 参数
    /// - `registry`: 处理器注册表
    ///
    /// # 返回
    /// MessageDispatcher - 分发器实例
    pub fn new(registry: Arc<RwLock<HandlerRegistry>>) -> Self {
        Self {
            registry,
            middleware_chain: Arc::new(RwLock::new(MiddlewareChain::new())),
        }
    }

    /// 创建带中间件的消息分发器
    ///
    /// # 参数
    /// - `registry`: 处理器注册表
    /// - `middleware_chain`: 中间件链
    ///
    /// # 返回
    /// MessageDispatcher - 分发器实例
    pub fn with_middleware(
        registry: Arc<RwLock<HandlerRegistry>>,
        middleware_chain: Arc<RwLock<MiddlewareChain>>,
    ) -> Self {
        Self {
            registry,
            middleware_chain,
        }
    }

    /// 分发消息
    ///
    /// # 参数
    /// - `message`: 待分发的消息
    ///
    /// # 返回
    /// Result<OutgoingMessage> - 处理结果
    pub async fn dispatch(&self, message: NativeMessage) -> Result<OutgoingMessage> {
        let message_type = format!("{:?}", message.message_type);
        debug!("开始分发消息: {}", message_type);

        // 执行前置中间件
        let middleware_chain = self.middleware_chain.read().await;
        let processed_message = middleware_chain.execute_before(message).await?;
        debug!("前置中间件处理完成");

        // 查找处理器
        let registry = self.registry.read().await;
        let handler = registry.get_handler(&message_type).await.ok_or_else(|| {
            warn!("未找到处理器：{}", message_type);
            NativeMessagingError::HandlerError(format!("未找到处理器：{}", message_type))
        })?;

        info!("找到处理器，开始处理消息: {}", message_type);

        // 处理消息
        let response = match handler.handle(processed_message).await {
            Ok(response) => {
                debug!("消息处理成功: {}", message_type);
                response
            }
            Err(e) => {
                error!("消息处理失败: {}, 错误: {}", message_type, e);
                return Err(e);
            }
        };

        // 执行后置中间件
        let final_response = middleware_chain.execute_after(response).await?;
        debug!("后置中间件处理完成");

        info!("消息分发完成: {}", message_type);
        Ok(final_response)
    }

    /// 添加中间件
    ///
    /// # 参数
    /// - `middleware`: 中间件实例
    pub async fn add_middleware(&self, middleware: Box<dyn super::Middleware>) {
        let mut chain = self.middleware_chain.write().await;
        chain.add(middleware);
        info!("添加中间件: {}", chain.len());
    }

    /// 检查是否支持消息类型
    ///
    /// # 参数
    /// - `message_type`: 消息类型
    ///
    /// # 返回
    /// bool - 是否支持
    pub async fn supports(&self, message_type: &str) -> bool {
        let registry = self.registry.read().await;
        registry.supports(message_type).await
    }

    /// 获取支持的消息类型列表
    ///
    /// # 返回
    /// Vec<String> - 支持的消息类型列表
    pub async fn get_supported_types(&self) -> Vec<String> {
        let registry = self.registry.read().await;
        registry.supported_types().await
    }

    /// 获取中间件数量
    ///
    /// # 返回
    /// usize - 中间件数量
    pub async fn middleware_count(&self) -> usize {
        let chain = self.middleware_chain.read().await;
        chain.len()
    }

    /// 获取注册的处理器数量
    ///
    /// # 返回
    /// usize - 处理器数量
    pub async fn handler_count(&self) -> usize {
        let registry = self.registry.read().await;
        registry.supported_types().await.len()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::{
        handlers::{MessageHandler, Middleware},
        protocol::message::MessageType,
    };
    use async_trait::async_trait;
    use std::sync::Arc;

    // 测试处理器
    struct TestHandler;

    #[async_trait]
    impl MessageHandler for TestHandler {
        async fn handle(&self, message: NativeMessage) -> Result<OutgoingMessage> {
            Ok(OutgoingMessage::success(
                message.request_id,
                serde_json::json!({"test": "success"}),
            ))
        }

        fn message_types(&self) -> Vec<String> {
            vec!["Test".to_string()]
        }

        fn name(&self) -> &str {
            "TestHandler"
        }
    }

    // 测试中间件
    struct TestMiddleware;

    #[async_trait]
    impl Middleware for TestMiddleware {
        async fn before_handle(&self, message: NativeMessage) -> Result<NativeMessage> {
            Ok(message)
        }

        async fn after_handle(&self, response: OutgoingMessage) -> Result<OutgoingMessage> {
            Ok(response)
        }

        fn name(&self) -> &str {
            "TestMiddleware"
        }
    }

    #[tokio::test]
    async fn test_dispatcher_creation() {
        let registry = Arc::new(RwLock::new(HandlerRegistry::new()));
        let dispatcher = MessageDispatcher::new(registry);
        assert!(!dispatcher.supports("unknown_type").await);
        assert_eq!(dispatcher.handler_count().await, 0);
        assert_eq!(dispatcher.middleware_count().await, 0);
    }

    #[tokio::test]
    async fn test_message_dispatch() {
        let registry = HandlerRegistry::new();
        let handler = Arc::new(TestHandler);
        registry.register(handler).await.unwrap();

        let registry = Arc::new(RwLock::new(registry));
        let dispatcher = MessageDispatcher::new(registry);

        let message = NativeMessage::new(
            MessageType::Test,
            "test-request".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );

        let result = dispatcher.dispatch(message).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_middleware_integration() {
        let registry = HandlerRegistry::new();
        let handler = Arc::new(TestHandler);
        registry.register(handler).await.unwrap();

        let registry = Arc::new(RwLock::new(registry));
        let dispatcher = MessageDispatcher::new(registry);

        // 添加中间件
        dispatcher.add_middleware(Box::new(TestMiddleware)).await;
        assert_eq!(dispatcher.middleware_count().await, 1);

        let message = NativeMessage::new(
            MessageType::Test,
            "test-request".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );

        let result = dispatcher.dispatch(message).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_unsupported_message_type() {
        let registry = Arc::new(RwLock::new(HandlerRegistry::new()));
        let dispatcher = MessageDispatcher::new(registry);

        let message = NativeMessage::new(
            MessageType::Test,
            "test-request".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );

        let result = dispatcher.dispatch(message).await;
        assert!(result.is_err());
    }
}
