//! Native Messaging 中间件
//!
//! 提供消息处理前后的中间件支持

use crate::native_messaging::{
    error::Result,
    protocol::message::{NativeMessage, OutgoingMessage},
};
use async_trait::async_trait;

/// 中间件接口
///
/// 定义中间件的标准接口
#[async_trait]
pub trait Middleware: Send + Sync {
    /// 消息处理前的中间件处理
    ///
    /// # 参数
    /// - `message`: 输入消息
    ///
    /// # 返回
    /// Result<NativeMessage> - 处理后的消息
    async fn before_handle(&self, message: NativeMessage) -> Result<NativeMessage>;

    /// 消息处理后的中间件处理
    ///
    /// # 参数
    /// - `response`: 响应消息
    ///
    /// # 返回
    /// Result<OutgoingMessage> - 处理后的响应
    async fn after_handle(&self, response: OutgoingMessage) -> Result<OutgoingMessage>;

    /// 获取中间件名称
    ///
    /// # 返回
    /// &str - 中间件名称
    fn name(&self) -> &str;
}

/// 中间件链
///
/// 管理多个中间件的执行顺序
pub struct MiddlewareChain {
    /// 中间件列表
    middlewares: Vec<Box<dyn Middleware>>,
}

impl MiddlewareChain {
    /// 创建新的中间件链
    pub fn new() -> Self {
        Self {
            middlewares: Vec::new(),
        }
    }

    /// 添加中间件
    ///
    /// # 参数
    /// - `middleware`: 中间件实例
    pub fn add(&mut self, middleware: Box<dyn Middleware>) {
        self.middlewares.push(middleware);
    }

    /// 执行前置中间件
    ///
    /// # 参数
    /// - `message`: 输入消息
    ///
    /// # 返回
    /// Result<NativeMessage> - 处理后的消息
    pub async fn execute_before(&self, mut message: NativeMessage) -> Result<NativeMessage> {
        for middleware in &self.middlewares {
            message = middleware.before_handle(message).await?;
        }
        Ok(message)
    }

    /// 执行后置中间件
    ///
    /// # 参数
    /// - `response`: 响应消息
    ///
    /// # 返回
    /// Result<OutgoingMessage> - 处理后的响应
    pub async fn execute_after(&self, mut response: OutgoingMessage) -> Result<OutgoingMessage> {
        // 逆序执行后置中间件
        for middleware in self.middlewares.iter().rev() {
            response = middleware.after_handle(response).await?;
        }
        Ok(response)
    }

    /// 获取中间件数量
    pub fn len(&self) -> usize {
        self.middlewares.len()
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.middlewares.is_empty()
    }
}

impl Default for MiddlewareChain {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    // 测试中间件
    struct TestMiddleware;

    #[async_trait]
    impl Middleware for TestMiddleware {
        async fn before_handle(&self, message: NativeMessage) -> Result<NativeMessage> {
            Ok(message)
        }

        async fn after_handle(&self, response: OutgoingMessage) -> Result<OutgoingMessage> {
            Ok(response)
        }

        fn name(&self) -> &str {
            "TestMiddleware"
        }
    }

    #[test]
    fn test_middleware_chain() {
        let mut chain = MiddlewareChain::new();
        assert!(chain.is_empty());

        chain.add(Box::new(TestMiddleware));
        assert_eq!(chain.len(), 1);
        assert!(!chain.is_empty());
    }
}
