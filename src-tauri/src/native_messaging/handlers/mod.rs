//! Native Messaging 处理器模块
//!
//! 提供消息处理器注册、分发和中间件支持

pub mod builtin;
pub mod dispatcher;
pub mod middleware;
pub mod registry;

// 公共导出
pub use dispatcher::MessageDispatcher;
pub use middleware::{Middleware, MiddlewareChain};
pub use registry::{HandlerRegistry, HandlerStats, RegistryStatus};

use crate::native_messaging::{
    error::Result,
    protocol::message::{NativeMessage, OutgoingMessage},
};
use async_trait::async_trait;

/// 消息处理器接口
///
/// 定义消息处理的标准接口
#[async_trait]
pub trait MessageHandler: Send + Sync {
    /// 处理消息
    ///
    /// # 参数
    /// - `message`: 输入消息
    ///
    /// # 返回
    /// Result<OutgoingMessage> - 处理结果
    async fn handle(&self, message: NativeMessage) -> Result<OutgoingMessage>;

    /// 获取支持的消息类型
    ///
    /// # 返回
    /// Vec<String> - 支持的消息类型列表
    fn message_types(&self) -> Vec<String>;

    /// 获取处理器名称
    ///
    /// # 返回
    /// &str - 处理器名称
    fn name(&self) -> &str;
}

/// 处理器管理器
///
/// 提供处理器注册和管理的高级接口
pub struct HandlerManager {
    /// 处理器注册表
    registry: std::sync::Arc<tokio::sync::RwLock<HandlerRegistry>>,
    /// 消息分发器
    dispatcher: MessageDispatcher,
}

impl HandlerManager {
    /// 创建新的处理器管理器
    pub fn new() -> Self {
        let registry = std::sync::Arc::new(tokio::sync::RwLock::new(HandlerRegistry::new()));
        let dispatcher = MessageDispatcher::new(registry.clone());

        Self {
            registry,
            dispatcher,
        }
    }

    /// 注册处理器
    pub async fn register_handler(&self, handler: std::sync::Arc<dyn MessageHandler>) -> Result<()> {
        let registry = self.registry.write().await;
        registry.register(handler).await
    }

    /// 注册所有内置处理器
    pub async fn register_builtin_handlers(&self) -> Result<usize> {
        let registry = self.registry.read().await;
        let count = builtin::BuiltinHandlerFactory::register_all_builtin_handlers(&registry).await;
        Ok(count)
    }

    /// 分发消息
    pub async fn dispatch_message(&self, message: NativeMessage) -> Result<OutgoingMessage> {
        self.dispatcher.dispatch(message).await
    }

    /// 获取支持的消息类型
    pub async fn get_supported_types(&self) -> Vec<String> {
        self.dispatcher.get_supported_types().await
    }

    /// 获取注册表状态
    pub async fn get_registry_status(&self) -> RegistryStatus {
        let registry = self.registry.read().await;
        registry.get_status().await
    }

    /// 添加中间件
    pub async fn add_middleware(&self, middleware: Box<dyn Middleware>) {
        self.dispatcher.add_middleware(middleware).await
    }
}

impl Default for HandlerManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;
    use async_trait::async_trait;
    use std::sync::Arc;

    // 测试用处理器
    struct TestHandler;

    #[async_trait]
    impl MessageHandler for TestHandler {
        async fn handle(&self, message: NativeMessage) -> Result<OutgoingMessage> {
            Ok(OutgoingMessage::success(
                message.request_id,
                serde_json::json!({"test": "success"}),
            ))
        }

        fn message_types(&self) -> Vec<String> {
            vec!["Test".to_string()]
        }

        fn name(&self) -> &str {
            "TestHandler"
        }
    }

    #[test]
    fn test_message_handler_interface() {
        let handler = TestHandler;
        assert_eq!(handler.name(), "TestHandler");
        assert_eq!(handler.message_types(), vec!["Test".to_string()]);
    }

    #[tokio::test]
    async fn test_handler_manager() {
        let manager = HandlerManager::new();
        
        // 注册内置处理器
        let count = manager.register_builtin_handlers().await.unwrap();
        assert!(count > 0);

        // 检查支持的消息类型
        let types = manager.get_supported_types().await;
        assert!(!types.is_empty());

        // 获取状态
        let status = manager.get_registry_status().await;
        assert!(status.total_handlers > 0);
    }

    #[tokio::test]
    async fn test_custom_handler_registration() {
        let manager = HandlerManager::new();
        let handler = Arc::new(TestHandler);

        // 注册自定义处理器
        assert!(manager.register_handler(handler).await.is_ok());

        // 检查是否支持
        let types = manager.get_supported_types().await;
        assert!(types.contains(&"Test".to_string()));
    }

    #[tokio::test]
    async fn test_message_dispatch() {
        let manager = HandlerManager::new();
        let handler = Arc::new(TestHandler);
        manager.register_handler(handler).await.unwrap();

        let message = NativeMessage::new(
            MessageType::Test,
            "test-request".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );

        let result = manager.dispatch_message(message).await;
        assert!(result.is_ok());
    }
}
