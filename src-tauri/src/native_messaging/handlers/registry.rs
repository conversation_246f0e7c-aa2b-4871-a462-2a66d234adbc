//! Native Messaging 处理器注册表
//!
//! 提供消息处理器的注册、查找和管理功能

use super::MessageHandler;
use crate::native_messaging::error::Result;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

/// 处理器统计信息
#[derive(Debug, Clone)]
pub struct HandlerStats {
    /// 处理器名称
    pub name: String,
    /// 注册时间
    pub registered_at: Instant,
    /// 调用次数
    pub call_count: u64,
    /// 成功次数
    pub success_count: u64,
    /// 失败次数
    pub failure_count: u64,
    /// 平均响应时间
    pub average_response_time: Duration,
    /// 最后调用时间
    pub last_called_at: Option<Instant>,
}

impl HandlerStats {
    fn new(name: String) -> Self {
        Self {
            name,
            registered_at: Instant::now(),
            call_count: 0,
            success_count: 0,
            failure_count: 0,
            average_response_time: Duration::ZERO,
            last_called_at: None,
        }
    }

    /// 记录调用结果
    pub fn record_call(&mut self, success: bool, response_time: Duration) {
        self.call_count += 1;
        self.last_called_at = Some(Instant::now());

        if success {
            self.success_count += 1;
        } else {
            self.failure_count += 1;
        }

        // 更新平均响应时间
        if self.call_count == 1 {
            self.average_response_time = response_time;
        } else {
            let total_time = self.average_response_time * (self.call_count - 1) as u32 + response_time;
            self.average_response_time = total_time / self.call_count as u32;
        }
    }

    /// 获取成功率
    pub fn success_rate(&self) -> f64 {
        if self.call_count == 0 {
            0.0
        } else {
            self.success_count as f64 / self.call_count as f64
        }
    }
}

/// 处理器注册表
///
/// 管理所有已注册的消息处理器，提供线程安全的访问
pub struct HandlerRegistry {
    /// 处理器映射表 (消息类型 -> 处理器)
    handlers: RwLock<HashMap<String, Arc<dyn MessageHandler>>>,
    /// 处理器统计信息
    stats: RwLock<HashMap<String, HandlerStats>>,
    /// 处理器名称映射 (处理器名称 -> 处理器)
    handler_by_name: RwLock<HashMap<String, Arc<dyn MessageHandler>>>,
}

impl HandlerRegistry {
    /// 创建新的处理器注册表
    pub fn new() -> Self {
        Self {
            handlers: RwLock::new(HashMap::new()),
            stats: RwLock::new(HashMap::new()),
            handler_by_name: RwLock::new(HashMap::new()),
        }
    }

    /// 注册消息处理器
    ///
    /// # 参数
    /// - `handler`: 处理器实例
    ///
    /// # 返回
    /// Result<()> - 注册结果
    pub async fn register(&self, handler: Arc<dyn MessageHandler>) -> Result<()> {
        let handler_name = handler.name().to_string();
        let message_types = handler.message_types();

        debug!("注册处理器: {} 支持类型: {:?}", handler_name, message_types);

        // 检查是否已经注册了同名处理器
        {
            let handler_by_name = self.handler_by_name.read().await;
            if handler_by_name.contains_key(&handler_name) {
                warn!("处理器已存在，将覆盖: {}", handler_name);
            }
        }

        // 注册处理器
        {
            let mut handlers = self.handlers.write().await;
            let mut handler_by_name = self.handler_by_name.write().await;
            let mut stats = self.stats.write().await;

            for message_type in &message_types {
                if handlers.contains_key(message_type) {
                    warn!("消息类型 {} 的处理器将被覆盖", message_type);
                }
                handlers.insert(message_type.clone(), handler.clone());
            }

            handler_by_name.insert(handler_name.clone(), handler.clone());
            stats.insert(handler_name.clone(), HandlerStats::new(handler_name.clone()));
        }

        info!("成功注册处理器: {} 支持 {} 种消息类型", handler_name, message_types.len());
        Ok(())
    }

    /// 取消注册处理器
    ///
    /// # 参数
    /// - `handler_name`: 处理器名称
    ///
    /// # 返回
    /// Result<bool> - 是否成功取消注册
    pub async fn unregister(&self, handler_name: &str) -> Result<bool> {
        debug!("取消注册处理器: {}", handler_name);

        let mut handlers = self.handlers.write().await;
        let mut handler_by_name = self.handler_by_name.write().await;
        let mut stats = self.stats.write().await;

        // 查找并移除处理器
        if let Some(handler) = handler_by_name.remove(handler_name) {
            let message_types = handler.message_types();
            
            // 从消息类型映射中移除
            for message_type in &message_types {
                handlers.remove(message_type);
            }

            // 移除统计信息
            stats.remove(handler_name);

            info!("成功取消注册处理器: {}", handler_name);
            Ok(true)
        } else {
            warn!("处理器不存在: {}", handler_name);
            Ok(false)
        }
    }

    /// 获取处理器
    ///
    /// # 参数
    /// - `message_type`: 消息类型
    ///
    /// # 返回
    /// Option<Arc<dyn MessageHandler>> - 处理器实例
    pub async fn get_handler(&self, message_type: &str) -> Option<Arc<dyn MessageHandler>> {
        let handlers = self.handlers.read().await;
        handlers.get(message_type).cloned()
    }

    /// 根据名称获取处理器
    ///
    /// # 参数
    /// - `handler_name`: 处理器名称
    ///
    /// # 返回
    /// Option<Arc<dyn MessageHandler>> - 处理器实例
    pub async fn get_handler_by_name(&self, handler_name: &str) -> Option<Arc<dyn MessageHandler>> {
        let handler_by_name = self.handler_by_name.read().await;
        handler_by_name.get(handler_name).cloned()
    }

    /// 检查是否有处理器支持指定消息类型
    ///
    /// # 参数
    /// - `message_type`: 消息类型
    ///
    /// # 返回
    /// bool - 是否支持
    pub async fn supports(&self, message_type: &str) -> bool {
        let handlers = self.handlers.read().await;
        handlers.contains_key(message_type)
    }

    /// 获取所有支持的消息类型
    ///
    /// # 返回
    /// Vec<String> - 消息类型列表
    pub async fn supported_types(&self) -> Vec<String> {
        let handlers = self.handlers.read().await;
        handlers.keys().cloned().collect()
    }

    /// 获取所有注册的处理器名称
    ///
    /// # 返回
    /// Vec<String> - 处理器名称列表
    pub async fn registered_handlers(&self) -> Vec<String> {
        let handler_by_name = self.handler_by_name.read().await;
        handler_by_name.keys().cloned().collect()
    }

    /// 记录处理器调用结果
    ///
    /// # 参数
    /// - `handler_name`: 处理器名称
    /// - `success`: 是否成功
    /// - `response_time`: 响应时间
    pub async fn record_call(&self, handler_name: &str, success: bool, response_time: Duration) {
        let mut stats = self.stats.write().await;
        if let Some(handler_stats) = stats.get_mut(handler_name) {
            handler_stats.record_call(success, response_time);
        }
    }

    /// 获取处理器统计信息
    ///
    /// # 参数
    /// - `handler_name`: 处理器名称
    ///
    /// # 返回
    /// Option<HandlerStats> - 统计信息
    pub async fn get_stats(&self, handler_name: &str) -> Option<HandlerStats> {
        let stats = self.stats.read().await;
        stats.get(handler_name).cloned()
    }

    /// 获取所有处理器统计信息
    ///
    /// # 返回
    /// HashMap<String, HandlerStats> - 所有统计信息
    pub async fn get_all_stats(&self) -> HashMap<String, HandlerStats> {
        let stats = self.stats.read().await;
        stats.clone()
    }

    /// 清理未使用的处理器
    ///
    /// 移除超过指定时间未被调用的处理器
    ///
    /// # 参数
    /// - `inactive_duration`: 非活跃时间阈值
    ///
    /// # 返回
    /// usize - 清理的处理器数量
    pub async fn cleanup_inactive_handlers(&self, inactive_duration: Duration) -> usize {
        let now = Instant::now();
        let mut cleanup_count = 0;

        let handler_names: Vec<String> = {
            let stats = self.stats.read().await;
            stats.iter()
                .filter(|(_, stats)| {
                    if let Some(last_called) = stats.last_called_at {
                        now.duration_since(last_called) > inactive_duration
                    } else {
                        now.duration_since(stats.registered_at) > inactive_duration
                    }
                })
                .map(|(name, _)| name.clone())
                .collect()
        };

        for handler_name in handler_names {
            if self.unregister(&handler_name).await.unwrap_or(false) {
                cleanup_count += 1;
            }
        }

        if cleanup_count > 0 {
            info!("清理了 {} 个非活跃处理器", cleanup_count);
        }

        cleanup_count
    }

    /// 获取注册表状态信息
    ///
    /// # 返回
    /// RegistryStatus - 状态信息
    pub async fn get_status(&self) -> RegistryStatus {
        let handlers = self.handlers.read().await;
        let stats = self.stats.read().await;

        let total_calls: u64 = stats.values().map(|s| s.call_count).sum();
        let total_successes: u64 = stats.values().map(|s| s.success_count).sum();

        RegistryStatus {
            total_handlers: stats.len(),
            total_message_types: handlers.len(),
            total_calls,
            total_successes,
            success_rate: if total_calls > 0 {
                total_successes as f64 / total_calls as f64
            } else {
                0.0
            },
        }
    }
}

/// 注册表状态信息
#[derive(Debug, Clone)]
pub struct RegistryStatus {
    /// 总处理器数量
    pub total_handlers: usize,
    /// 总消息类型数量
    pub total_message_types: usize,
    /// 总调用次数
    pub total_calls: u64,
    /// 总成功次数
    pub total_successes: u64,
    /// 整体成功率
    pub success_rate: f64,
}

impl Default for HandlerRegistry {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::handlers::MessageHandler;
    use async_trait::async_trait;

    // 测试处理器
    struct TestHandler {
        name: String,
        message_types: Vec<String>,
    }

    impl TestHandler {
        fn new(name: String, message_types: Vec<String>) -> Self {
            Self { name, message_types }
        }
    }

    #[async_trait]
    impl MessageHandler for TestHandler {
        async fn handle(
            &self,
            _message: crate::native_messaging::protocol::message::NativeMessage,
        ) -> Result<crate::native_messaging::protocol::message::OutgoingMessage> {
            Ok(
                crate::native_messaging::protocol::message::OutgoingMessage::success(
                    "test".to_string(),
                    serde_json::json!({}),
                ),
            )
        }

        fn message_types(&self) -> Vec<String> {
            self.message_types.clone()
        }

        fn name(&self) -> &str {
            &self.name
        }
    }

    #[tokio::test]
    async fn test_registry_creation() {
        let registry = HandlerRegistry::new();
        assert_eq!(registry.supported_types().await.len(), 0);
        assert_eq!(registry.registered_handlers().await.len(), 0);
    }

    #[tokio::test]
    async fn test_handler_registration() {
        let registry = HandlerRegistry::new();
        let handler = Arc::new(TestHandler::new(
            "TestHandler".to_string(),
            vec!["test_message".to_string()],
        ));

        assert!(registry.register(handler).await.is_ok());
        assert!(registry.supports("test_message").await);
        assert_eq!(registry.supported_types().await, vec!["test_message"]);
        assert_eq!(registry.registered_handlers().await, vec!["TestHandler"]);
    }

    #[tokio::test]
    async fn test_handler_unregistration() {
        let registry = HandlerRegistry::new();
        let handler = Arc::new(TestHandler::new(
            "TestHandler".to_string(),
            vec!["test_message".to_string()],
        ));

        registry.register(handler).await.unwrap();
        assert!(registry.supports("test_message").await);

        let result = registry.unregister("TestHandler").await.unwrap();
        assert!(result);
        assert!(!registry.supports("test_message").await);
        assert_eq!(registry.registered_handlers().await.len(), 0);
    }

    #[tokio::test]
    async fn test_handler_stats() {
        let registry = HandlerRegistry::new();
        let handler = Arc::new(TestHandler::new(
            "TestHandler".to_string(),
            vec!["test_message".to_string()],
        ));

        registry.register(handler).await.unwrap();

        // 记录一些调用
        registry.record_call("TestHandler", true, Duration::from_millis(100)).await;
        registry.record_call("TestHandler", false, Duration::from_millis(200)).await;

        let stats = registry.get_stats("TestHandler").await.unwrap();
        assert_eq!(stats.call_count, 2);
        assert_eq!(stats.success_count, 1);
        assert_eq!(stats.failure_count, 1);
        assert_eq!(stats.success_rate(), 0.5);
    }

    #[tokio::test]
    async fn test_registry_status() {
        let registry = HandlerRegistry::new();
        let handler = Arc::new(TestHandler::new(
            "TestHandler".to_string(),
            vec!["test_message".to_string()],
        ));

        registry.register(handler).await.unwrap();
        registry.record_call("TestHandler", true, Duration::from_millis(100)).await;

        let status = registry.get_status().await;
        assert_eq!(status.total_handlers, 1);
        assert_eq!(status.total_message_types, 1);
        assert_eq!(status.total_calls, 1);
        assert_eq!(status.total_successes, 1);
        assert_eq!(status.success_rate, 1.0);
    }
}
