//! Native Messaging 后台监听器
//!
//! 提供非阻塞的异步消息处理引擎

use super::{connection::ConnectionManager, pool::ConnectionPool, ListenerStatus};
use crate::native_messaging::{
    config::NativeMessagingConfig,
    error::{NativeMessagingError, Result},
    protocol::{
        message::{ErrorCode, IncomingMessage, OutgoingMessage},
        validator::ProtocolValidator,
    },
};
use std::{
    sync::{
        atomic::{AtomicU64, AtomicUsize, Ordering},
        Arc,
    },
    time::{Duration, Instant},
};
use tokio::{
    io::{AsyncBufReadExt, AsyncWriteExt, BufReader},
    sync::{broadcast, Mutex, RwLock},
    time::sleep,
};
use tracing::{debug, error, info, warn};

/// 性能指标结构
#[derive(Debug, Clone)]
#[derive(Default)]
pub struct PerformanceMetrics {
    /// 总消息数
    pub total_messages: u64,
    /// 成功处理的消息数
    pub successful_messages: u64,
    /// 失败的消息数
    pub failed_messages: u64,
    /// 平均响应时间 (微秒)
    pub average_response_time_us: u64,
    /// 当前活跃连接数
    pub active_connections: usize,
    /// 内存使用量 (字节)
    pub memory_usage_bytes: usize,
}


/// 内部性能计数器
#[derive(Debug)]
struct MetricsCounters {
    total_messages: AtomicU64,
    successful_messages: AtomicU64,
    failed_messages: AtomicU64,
    total_response_time_us: AtomicU64,
    active_connections: AtomicUsize,
}

impl Default for MetricsCounters {
    fn default() -> Self {
        Self {
            total_messages: AtomicU64::new(0),
            successful_messages: AtomicU64::new(0),
            failed_messages: AtomicU64::new(0),
            total_response_time_us: AtomicU64::new(0),
            active_connections: AtomicUsize::new(0),
        }
    }
}

impl MetricsCounters {
    /// 记录消息处理结果
    fn record_message(&self, success: bool, response_time: Duration) {
        self.total_messages.fetch_add(1, Ordering::Relaxed);
        self.total_response_time_us
            .fetch_add(response_time.as_micros() as u64, Ordering::Relaxed);

        if success {
            self.successful_messages.fetch_add(1, Ordering::Relaxed);
        } else {
            self.failed_messages.fetch_add(1, Ordering::Relaxed);
        }
    }

    /// 增加活跃连接数
    fn increment_connections(&self) {
        self.active_connections.fetch_add(1, Ordering::Relaxed);
    }

    /// 减少活跃连接数
    fn decrement_connections(&self) {
        self.active_connections.fetch_sub(1, Ordering::Relaxed);
    }

    /// 获取当前指标
    fn get_metrics(&self) -> PerformanceMetrics {
        let total = self.total_messages.load(Ordering::Relaxed);
        let successful = self.successful_messages.load(Ordering::Relaxed);
        let failed = self.failed_messages.load(Ordering::Relaxed);
        let total_time = self.total_response_time_us.load(Ordering::Relaxed);
        let active = self.active_connections.load(Ordering::Relaxed);

        let average_response_time_us = if total > 0 { total_time / total } else { 0 };

        PerformanceMetrics {
            total_messages: total,
            successful_messages: successful,
            failed_messages: failed,
            average_response_time_us,
            active_connections: active,
            memory_usage_bytes: Self::estimate_memory_usage(),
        }
    }

    /// 估算内存使用量
    fn estimate_memory_usage() -> usize {
        // 简单的内存使用估算
        // 在实际应用中，可以使用更精确的内存监控
        std::mem::size_of::<BackgroundListener>() * 1000 // 基础估算
    }
}

/// 消息处理器 trait
#[async_trait::async_trait]
pub trait MessageHandler: Send + Sync {
    /// 处理消息
    async fn handle(&self, message: IncomingMessage) -> Result<OutgoingMessage>;

    /// 获取支持的消息类型
    fn message_types(&self) -> Vec<String>;
}

/// 后台监听器
///
/// 负责后台消息监听和处理的核心组件
pub struct BackgroundListener {
    /// 配置信息
    config: NativeMessagingConfig,
    /// 监听器状态
    status: Arc<Mutex<ListenerStatus>>,
    /// 连接池
    connection_pool: Arc<RwLock<ConnectionPool>>,
    /// 连接管理器
    connection_manager: Arc<RwLock<ConnectionManager>>,
    /// 协议验证器
    validator: Arc<ProtocolValidator>,
    /// 性能指标计数器
    metrics: Arc<MetricsCounters>,
    /// 关闭信号发送器
    shutdown_tx: Arc<Mutex<Option<broadcast::Sender<()>>>>,
    /// 消息处理器
    message_handlers: Arc<RwLock<std::collections::HashMap<String, Arc<dyn MessageHandler>>>>,
}

impl BackgroundListener {
    /// 启动后台消息监听器
    ///
    /// 创建并启动一个新的后台监听器实例，用于处理浏览器扩展的消息
    ///
    /// # 参数
    /// - `config`: 监听器配置信息
    ///
    /// # 返回
    /// Result<BackgroundListener> - 监听器实例
    ///
    /// # 错误
    /// 当配置无效或初始化失败时返回错误
    pub async fn start(config: NativeMessagingConfig) -> Result<Self> {
        // 验证配置
        config.validate()?;

        let listener_config = &config.listener;
        let connection_pool = ConnectionPool::new(listener_config.max_connections);
        let connection_manager = ConnectionManager::new();
        let validator = ProtocolValidator::new_default();

        let (shutdown_tx, _) = broadcast::channel(1);

        Ok(Self {
            config,
            status: Arc::new(Mutex::new(ListenerStatus::Stopped)),
            connection_pool: Arc::new(RwLock::new(connection_pool)),
            connection_manager: Arc::new(RwLock::new(connection_manager)),
            validator: Arc::new(validator),
            metrics: Arc::new(MetricsCounters::default()),
            shutdown_tx: Arc::new(Mutex::new(Some(shutdown_tx))),
            message_handlers: Arc::new(RwLock::new(std::collections::HashMap::new())),
        })
    }

    /// 注册消息处理器
    ///
    /// 为特定类型的消息注册处理函数
    ///
    /// # 参数
    /// - `handler`: 消息处理器实例
    ///
    /// # 返回
    /// Result<()> - 注册成功或失败
    pub async fn register_message_handler(&self, handler: Arc<dyn MessageHandler>) -> Result<()> {
        let mut handlers = self.message_handlers.write().await;

        for message_type in handler.message_types() {
            if handlers.contains_key(&message_type) {
                warn!("覆盖已存在的消息处理器: {}", message_type);
            }
            handlers.insert(message_type.clone(), handler.clone());
            debug!("注册消息处理器: {}", message_type);
        }

        Ok(())
    }

    /// 开始监听消息
    ///
    /// 启动监听循环，处理来自浏览器扩展的消息
    ///
    /// # 返回
    /// Result<()> - 监听启动结果
    pub async fn listen(&self) -> Result<()> {
        // 更新状态为启动中
        {
            let mut status = self.status.lock().await;
            *status = ListenerStatus::Starting;
        }

        info!("后台监听器启动中...");

        // 获取关闭信号接收器
        let mut shutdown_rx = {
            let shutdown_tx = self.shutdown_tx.lock().await;
            shutdown_tx
                .as_ref()
                .ok_or_else(|| NativeMessagingError::InternalError("监听器已关闭".to_string()))?
                .subscribe()
        };

        // 更新状态为运行中
        {
            let mut status = self.status.lock().await;
            *status = ListenerStatus::Running;
        }

        info!("后台监听器已启动");

        // 启动主监听循环
        tokio::select! {
            result = self.run_listener_loop() => {
                if let Err(e) = result {
                    error!("监听循环错误: {}", e);
                    return Err(e);
                }
            }
            _ = shutdown_rx.recv() => {
                info!("收到关闭信号，停止监听");
            }
        }

        Ok(())
    }

    /// 主监听循环
    async fn run_listener_loop(&self) -> Result<()> {
        let stdin = tokio::io::stdin();
        let mut reader = BufReader::new(stdin);
        let mut buffer = Vec::new();

        loop {
            // 检查监听器状态
            let status = {
                let status = self.status.lock().await;
                *status
            };

            if !status.is_active() {
                break;
            }

            // 非阻塞消息处理
            match self.handle_message_async(&mut reader, &mut buffer).await {
                Ok(_) => continue,
                Err(e) if self.is_recoverable_error(&e) => {
                    warn!("可恢复错误，继续监听: {}", e);
                    self.metrics.record_message(false, Duration::from_millis(0));

                    // 短暂等待后重试
                    sleep(Duration::from_millis(100)).await;
                    continue;
                }
                Err(e) => {
                    error!("不可恢复错误，停止监听: {}", e);
                    self.metrics.record_message(false, Duration::from_millis(0));
                    return Err(e);
                }
            }
        }

        Ok(())
    }

    /// 异步处理单个消息
    async fn handle_message_async(
        &self,
        reader: &mut BufReader<tokio::io::Stdin>,
        buffer: &mut Vec<u8>,
    ) -> Result<()> {
        let start_time = Instant::now();

        // 检查连接池是否可以接受新连接
        let can_accept = {
            let pool = self.connection_pool.read().await;
            pool.can_accept_connection()
        };

        if !can_accept {
            return Err(NativeMessagingError::ConnectionError(
                "连接池已满，无法接受新连接".to_string(),
            ));
        }

        // 增加活跃连接数
        self.metrics.increment_connections();

        // 处理完成后减少连接数
        let _connection_guard = ConnectionGuard::new(self.metrics.clone());

        // 读取消息长度
        buffer.clear();
        reader
            .read_until(b'\n', buffer)
            .await
            .map_err(|e| NativeMessagingError::ConnectionError(format!("读取消息失败: {}", e)))?;

        if buffer.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "收到空消息".to_string(),
            ));
        }

        // 解析消息
        let message_str = String::from_utf8_lossy(buffer);
        let trimmed = message_str.trim();

        let incoming_message: IncomingMessage = serde_json::from_str(trimmed)
            .map_err(|e| NativeMessagingError::ProtocolError(format!("JSON解析失败: {}", e)))?;

        debug!("收到消息: {:?}", incoming_message);

        // 验证消息
        self.validator.validate_message(&incoming_message)?;

        // 处理消息
        let response = self.process_message(incoming_message).await?;

        // 发送响应
        self.send_response(response).await?;

        // 记录性能指标
        let response_time = start_time.elapsed();
        self.metrics.record_message(true, response_time);

        debug!("消息处理完成，耗时: {:?}", response_time);

        Ok(())
    }

    /// 处理消息
    async fn process_message(&self, incoming: IncomingMessage) -> Result<OutgoingMessage> {
        let message_type = &incoming.message_type;

        // 查找对应的处理器
        let handler = {
            let handlers = self.message_handlers.read().await;
            handlers.get(&message_type.as_string()).cloned()
        };

        match handler {
            Some(handler) => {
                debug!("使用处理器处理消息类型: {}", message_type.as_string());
                handler.handle(incoming).await
            }
            None => {
                warn!("未找到消息类型的处理器: {}", message_type.as_string());
                Ok(OutgoingMessage::error(
                    incoming.request_id,
                    ErrorCode::OperationNotSupported,
                    format!("不支持的消息类型: {}", message_type.as_string()),
                ))
            }
        }
    }

    /// 发送响应
    async fn send_response(&self, response: OutgoingMessage) -> Result<()> {
        let mut stdout = tokio::io::stdout();
        let response_json = serde_json::to_string(&response)
            .map_err(|e| NativeMessagingError::ProtocolError(format!("响应序列化失败: {}", e)))?;

        stdout
            .write_all(response_json.as_bytes())
            .await
            .map_err(NativeMessagingError::IoError)?;

        stdout
            .write_all(b"\n")
            .await
            .map_err(NativeMessagingError::IoError)?;

        stdout
            .flush()
            .await
            .map_err(NativeMessagingError::IoError)?;

        debug!("响应已发送: {}", response_json);
        Ok(())
    }

    /// 判断错误是否可恢复
    fn is_recoverable_error(&self, error: &NativeMessagingError) -> bool {
        match error {
            NativeMessagingError::ConnectionError(_) => true,
            NativeMessagingError::ProtocolError(_) => true,
            NativeMessagingError::IoError(_) => true,
            NativeMessagingError::SerializationError(_) => true,
            _ => false,
        }
    }

    /// 获取监听器状态信息
    ///
    /// 返回当前监听器的运行状态和基本信息
    ///
    /// # 返回
    /// ListenerStatus - 监听器状态
    pub async fn get_listener_status(&self) -> ListenerStatus {
        let status = self.status.lock().await;
        *status
    }

    /// 获取性能指标
    ///
    /// 返回监听器的详细性能统计信息
    ///
    /// # 返回
    /// PerformanceMetrics - 性能指标数据
    pub async fn get_performance_metrics(&self) -> PerformanceMetrics {
        self.metrics.get_metrics()
    }

    /// 优雅关闭监听器
    ///
    /// 安全地关闭所有连接并清理资源
    ///
    /// # 返回
    /// Result<()> - 关闭成功或失败
    pub async fn shutdown_gracefully(&self) -> Result<()> {
        // 更新状态为停止中
        {
            let mut status = self.status.lock().await;
            *status = ListenerStatus::Stopping;
        }

        info!("后台监听器正在关闭...");

        // 发送关闭信号
        if let Some(shutdown_tx) = self.shutdown_tx.lock().await.take() {
            let _ = shutdown_tx.send(());
        }

        // 等待所有连接关闭
        let timeout = Duration::from_secs(30); // 默认30秒超时
        let start = Instant::now();

        while start.elapsed() < timeout {
            let active = self.metrics.active_connections.load(Ordering::Relaxed);
            if active == 0 {
                break;
            }
            sleep(Duration::from_millis(100)).await;
        }

        // 更新状态为已停止
        {
            let mut status = self.status.lock().await;
            *status = ListenerStatus::Stopped;
        }

        info!("后台监听器已关闭");
        Ok(())
    }
}

/// 连接守卫，确保连接计数正确
struct ConnectionGuard {
    metrics: Arc<MetricsCounters>,
}

impl ConnectionGuard {
    fn new(metrics: Arc<MetricsCounters>) -> Self {
        Self { metrics }
    }
}

impl Drop for ConnectionGuard {
    fn drop(&mut self) {
        self.metrics.decrement_connections();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::config::NativeMessagingConfig;

    #[tokio::test]
    async fn test_listener_creation() {
        let config = NativeMessagingConfig::default();
        let listener = BackgroundListener::start(config).await;
        assert!(listener.is_ok());
    }

    #[tokio::test]
    async fn test_listener_status_transitions() {
        let config = NativeMessagingConfig::default();
        let listener = BackgroundListener::start(config).await.unwrap();

        // 初始状态应该是Stopped
        assert_eq!(
            listener.get_listener_status().await,
            ListenerStatus::Stopped
        );

        // 关闭监听器
        assert!(listener.shutdown_gracefully().await.is_ok());
        assert_eq!(
            listener.get_listener_status().await,
            ListenerStatus::Stopped
        );
    }

    #[tokio::test]
    async fn test_performance_metrics() {
        let config = NativeMessagingConfig::default();
        let listener = BackgroundListener::start(config).await.unwrap();

        let metrics = listener.get_performance_metrics().await;
        assert_eq!(metrics.total_messages, 0);
        assert_eq!(metrics.successful_messages, 0);
        assert_eq!(metrics.active_connections, 0);
    }

    #[tokio::test]
    async fn test_message_handler_registration() {
        let config = NativeMessagingConfig::default();
        let listener = BackgroundListener::start(config).await.unwrap();

        // 创建测试处理器
        struct TestHandler;

        #[async_trait::async_trait]
        impl MessageHandler for TestHandler {
            async fn handle(&self, message: IncomingMessage) -> Result<OutgoingMessage> {
                Ok(OutgoingMessage::success(
                    message.request_id,
                    serde_json::Value::Null,
                ))
            }

            fn message_types(&self) -> Vec<String> {
                vec!["test".to_string()]
            }
        }

        let handler: Arc<dyn MessageHandler> = Arc::new(TestHandler);
        let result = listener.register_message_handler(handler).await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_metrics_counters() {
        let counters = MetricsCounters::default();

        // 测试记录消息
        counters.record_message(true, Duration::from_millis(10));
        counters.record_message(false, Duration::from_millis(5));

        let metrics = counters.get_metrics();
        assert_eq!(metrics.total_messages, 2);
        assert_eq!(metrics.successful_messages, 1);
        assert_eq!(metrics.failed_messages, 1);
        assert!(metrics.average_response_time_us > 0);
    }

    #[test]
    fn test_connection_guard() {
        let counters = Arc::new(MetricsCounters::default());

        // 先手动增加连接数
        counters.increment_connections();
        assert_eq!(counters.active_connections.load(Ordering::Relaxed), 1);

        // 创建连接守卫
        {
            let _guard = ConnectionGuard::new(counters.clone());
            // 连接数应该保持为1
            assert_eq!(counters.active_connections.load(Ordering::Relaxed), 1);
        }

        // 守卫销毁后，连接数应该减少
        assert_eq!(counters.active_connections.load(Ordering::Relaxed), 0);
    }
}
