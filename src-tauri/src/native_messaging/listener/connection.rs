//! Native Messaging 连接管理
//!
//! 提供连接管理和连接状态跟踪功能

use crate::native_messaging::error::{NativeMessagingError, Result};
use std::collections::HashMap;
use std::sync::{
    atomic::{AtomicUsize, Ordering},
    Arc,
};
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info};
use uuid::Uuid;

/// 连接状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ConnectionState {
    /// 连接中
    Connecting,
    /// 已连接
    Connected,
    /// 空闲状态
    Idle,
    /// 断开连接中
    Disconnecting,
    /// 已断开
    Disconnected,
    /// 错误状态
    Error,
}

impl ConnectionState {
    /// 检查连接是否活跃
    pub fn is_active(&self) -> bool {
        matches!(self, ConnectionState::Connected | ConnectionState::Idle)
    }

    /// 检查连接是否可用
    pub fn is_available(&self) -> bool {
        matches!(self, ConnectionState::Connected | ConnectionState::Idle)
    }
}

/// 连接统计信息
#[derive(Debug, <PERSON>lone)]
pub struct ConnectionStats {
    /// 总消息数
    pub total_messages: u64,
    /// 成功消息数
    pub successful_messages: u64,
    /// 失败消息数
    pub failed_messages: u64,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 最后一次错误
    pub last_error: Option<String>,
}

impl Default for ConnectionStats {
    fn default() -> Self {
        Self {
            total_messages: 0,
            successful_messages: 0,
            failed_messages: 0,
            avg_response_time_ms: 0.0,
            last_error: None,
        }
    }
}

/// 连接信息
#[derive(Debug, Clone)]
pub struct Connection {
    /// 连接ID
    pub id: String,
    /// 连接状态
    pub state: ConnectionState,
    /// 创建时间
    pub created_at: Instant,
    /// 最后活动时间
    pub last_activity: Instant,
    /// 连接来源
    pub source: String,
    /// 协议版本
    pub protocol_version: u32,
    /// 连接优先级
    pub priority: ConnectionPriority,
    /// 连接统计
    pub stats: ConnectionStats,
    /// 连接元数据
    pub metadata: HashMap<String, String>,
}

/// 连接优先级
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
#[derive(Default)]
pub enum ConnectionPriority {
    Low = 1,
    #[default]
    Normal = 2,
    High = 3,
    Critical = 4,
}


impl Connection {
    /// 创建新连接
    ///
    /// # 参数
    /// - `source`: 连接来源标识
    /// - `protocol_version`: 协议版本
    ///
    /// # 返回
    /// Connection - 新连接实例
    pub fn new(source: String, protocol_version: u32) -> Self {
        let now = Instant::now();
        Self {
            id: Uuid::new_v4().to_string(),
            state: ConnectionState::Connecting,
            created_at: now,
            last_activity: now,
            source,
            protocol_version,
            priority: ConnectionPriority::default(),
            stats: ConnectionStats::default(),
            metadata: HashMap::new(),
        }
    }

    /// 创建带ID的连接
    pub fn new_with_id(id: String, source: String, protocol_version: u32) -> Self {
        let now = Instant::now();
        Self {
            id,
            state: ConnectionState::Connecting,
            created_at: now,
            last_activity: now,
            source,
            protocol_version,
            priority: ConnectionPriority::default(),
            stats: ConnectionStats::default(),
            metadata: HashMap::new(),
        }
    }

    /// 设置连接优先级
    pub fn with_priority(mut self, priority: ConnectionPriority) -> Self {
        self.priority = priority;
        self
    }

    /// 添加元数据
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }

    /// 更新连接状态
    pub fn update_state(&mut self, new_state: ConnectionState) {
        debug!(
            "连接 {} 状态变更: {:?} -> {:?}",
            self.id, self.state, new_state
        );
        self.state = new_state;
        self.update_activity();
    }

    /// 更新最后活动时间
    pub fn update_activity(&mut self) {
        self.last_activity = Instant::now();
    }

    /// 记录消息处理结果
    pub fn record_message(&mut self, success: bool, response_time: Duration) {
        self.stats.total_messages += 1;

        if success {
            self.stats.successful_messages += 1;
        } else {
            self.stats.failed_messages += 1;
        }

        // 更新平均响应时间
        let new_time = response_time.as_millis() as f64;
        if self.stats.total_messages == 1 {
            self.stats.avg_response_time_ms = new_time;
        } else {
            let total = self.stats.total_messages as f64;
            self.stats.avg_response_time_ms =
                (self.stats.avg_response_time_ms * (total - 1.0) + new_time) / total;
        }

        self.update_activity();
    }

    /// 记录错误
    pub fn record_error(&mut self, error: String) {
        self.stats.last_error = Some(error);
        self.update_activity();
    }

    /// 检查连接是否空闲超时
    pub fn is_idle(&self, timeout: Duration) -> bool {
        self.last_activity.elapsed() > timeout
    }

    /// 检查连接是否已过期
    pub fn is_expired(&self, max_lifetime: Duration) -> bool {
        self.created_at.elapsed() > max_lifetime
    }

    /// 获取连接存活时间
    pub fn lifetime(&self) -> Duration {
        self.created_at.elapsed()
    }

    /// 获取空闲时间
    pub fn idle_time(&self) -> Duration {
        self.last_activity.elapsed()
    }

    /// 获取成功率
    pub fn success_rate(&self) -> f64 {
        if self.stats.total_messages == 0 {
            0.0
        } else {
            self.stats.successful_messages as f64 / self.stats.total_messages as f64
        }
    }

    /// 检查连接是否健康
    pub fn is_healthy(&self) -> bool {
        self.state.is_active() && self.success_rate() >= 0.8
    }
}

/// 连接管理器配置
#[derive(Debug, Clone)]
pub struct ConnectionManagerConfig {
    /// 最大连接数
    pub max_connections: usize,
    /// 空闲超时时间
    pub idle_timeout: Duration,
    /// 连接最大存活时间
    pub max_lifetime: Duration,
    /// 健康检查间隔
    pub health_check_interval: Duration,
    /// 清理间隔
    pub cleanup_interval: Duration,
}

impl Default for ConnectionManagerConfig {
    fn default() -> Self {
        Self {
            max_connections: 1000,
            idle_timeout: Duration::from_secs(300),  // 5分钟
            max_lifetime: Duration::from_secs(3600), // 1小时
            health_check_interval: Duration::from_secs(30),
            cleanup_interval: Duration::from_secs(60),
        }
    }
}

/// 连接管理器
///
/// 负责管理所有活跃连接的生命周期
pub struct ConnectionManager {
    /// 连接映射表
    connections: Arc<RwLock<HashMap<String, Connection>>>,
    /// 配置信息
    config: ConnectionManagerConfig,
    /// 连接计数器
    connection_count: Arc<AtomicUsize>,
    /// 按优先级排序的连接ID列表
    priority_queue: Arc<RwLock<Vec<String>>>,
}

impl ConnectionManager {
    /// 创建新的连接管理器
    pub fn new() -> Self {
        Self::with_config(ConnectionManagerConfig::default())
    }

    /// 使用指定配置创建连接管理器
    pub fn with_config(config: ConnectionManagerConfig) -> Self {
        Self {
            connections: Arc::new(RwLock::new(HashMap::new())),
            config,
            connection_count: Arc::new(AtomicUsize::new(0)),
            priority_queue: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// 添加新连接
    ///
    /// # 参数
    /// - `connection`: 连接实例
    ///
    /// # 返回
    /// Result<String> - 连接ID或错误
    pub async fn add_connection(&self, mut connection: Connection) -> Result<String> {
        // 检查连接数限制
        if self.connection_count.load(Ordering::Relaxed) >= self.config.max_connections {
            return Err(NativeMessagingError::ConnectionError(
                "连接数已达到上限".to_string(),
            ));
        }

        connection.update_state(ConnectionState::Connected);
        let connection_id = connection.id.clone();

        {
            let mut connections = self.connections.write().await;
            connections.insert(connection_id.clone(), connection.clone());
        }

        // 更新优先级队列
        self.update_priority_queue().await;

        self.connection_count.fetch_add(1, Ordering::Relaxed);
        info!("添加连接: {} (来源: {})", connection_id, connection.source);

        Ok(connection_id)
    }

    /// 移除连接
    ///
    /// # 参数
    /// - `connection_id`: 连接ID
    ///
    /// # 返回
    /// Result<Option<Connection>> - 移除的连接或None
    pub async fn remove_connection(&self, connection_id: &str) -> Result<Option<Connection>> {
        let connection = {
            let mut connections = self.connections.write().await;
            connections.remove(connection_id)
        };

        if connection.is_some() {
            self.connection_count.fetch_sub(1, Ordering::Relaxed);

            // 更新优先级队列
            self.update_priority_queue().await;

            info!("移除连接: {}", connection_id);
        }

        Ok(connection)
    }

    /// 获取连接
    ///
    /// # 参数
    /// - `connection_id`: 连接ID
    ///
    /// # 返回
    /// Option<Connection> - 连接实例或None
    pub async fn get_connection(&self, connection_id: &str) -> Option<Connection> {
        let connections = self.connections.read().await;
        connections.get(connection_id).cloned()
    }

    /// 更新连接状态
    ///
    /// # 参数
    /// - `connection_id`: 连接ID  
    /// - `new_state`: 新状态
    ///
    /// # 返回
    /// Result<()> - 更新结果
    pub async fn update_connection_state(
        &self,
        connection_id: &str,
        new_state: ConnectionState,
    ) -> Result<()> {
        let mut connections = self.connections.write().await;

        if let Some(connection) = connections.get_mut(connection_id) {
            connection.update_state(new_state);
            Ok(())
        } else {
            Err(NativeMessagingError::ConnectionError(format!(
                "连接不存在: {}",
                connection_id
            )))
        }
    }

    /// 记录连接消息处理结果
    pub async fn record_message(
        &self,
        connection_id: &str,
        success: bool,
        response_time: Duration,
    ) -> Result<()> {
        let mut connections = self.connections.write().await;

        if let Some(connection) = connections.get_mut(connection_id) {
            connection.record_message(success, response_time);
            Ok(())
        } else {
            Err(NativeMessagingError::ConnectionError(format!(
                "连接不存在: {}",
                connection_id
            )))
        }
    }

    /// 获取所有活跃连接
    pub async fn get_active_connections(&self) -> Vec<Connection> {
        let connections = self.connections.read().await;
        connections
            .values()
            .filter(|conn| conn.state.is_active())
            .cloned()
            .collect()
    }

    /// 获取可用连接（按优先级排序）
    pub async fn get_available_connections(&self) -> Vec<Connection> {
        let connections = self.connections.read().await;
        let mut available: Vec<_> = connections
            .values()
            .filter(|conn| conn.state.is_available())
            .cloned()
            .collect();

        // 按优先级排序（高优先级在前）
        available.sort_by(|a, b| b.priority.cmp(&a.priority));
        available
    }

    /// 获取最佳连接（负载均衡）
    pub async fn get_best_connection(&self) -> Option<Connection> {
        let available = self.get_available_connections().await;

        if available.is_empty() {
            return None;
        }

        // 简单的负载均衡：选择消息数最少且成功率最高的连接
        available.into_iter().min_by(|a, b| {
            let a_score = a.stats.total_messages as f64 / (a.success_rate() + 0.1);
            let b_score = b.stats.total_messages as f64 / (b.success_rate() + 0.1);
            a_score
                .partial_cmp(&b_score)
                .unwrap_or(std::cmp::Ordering::Equal)
        })
    }

    /// 清理过期和不健康的连接
    pub async fn cleanup_connections(&self) -> usize {
        let mut removed_count = 0;
        let mut to_remove = Vec::new();

        {
            let connections = self.connections.read().await;
            for (id, connection) in connections.iter() {
                let should_remove = connection.is_idle(self.config.idle_timeout)
                    || connection.is_expired(self.config.max_lifetime)
                    || connection.state == ConnectionState::Error
                    || !connection.is_healthy();

                if should_remove {
                    to_remove.push(id.clone());
                }
            }
        }

        for connection_id in to_remove {
            if self.remove_connection(&connection_id).await.is_ok() {
                removed_count += 1;
            }
        }

        if removed_count > 0 {
            info!("清理了 {} 个过期/不健康连接", removed_count);
        }

        removed_count
    }

    /// 获取连接统计信息
    pub async fn get_stats(&self) -> ConnectionManagerStats {
        let connections = self.connections.read().await;
        let total_connections = connections.len();
        let mut active_connections = 0;
        let mut idle_connections = 0;
        let mut total_messages = 0;
        let mut total_successful = 0;

        for connection in connections.values() {
            match connection.state {
                ConnectionState::Connected => active_connections += 1,
                ConnectionState::Idle => idle_connections += 1,
                _ => {}
            }

            total_messages += connection.stats.total_messages;
            total_successful += connection.stats.successful_messages;
        }

        let success_rate = if total_messages > 0 {
            total_successful as f64 / total_messages as f64
        } else {
            0.0
        };

        ConnectionManagerStats {
            total_connections,
            active_connections,
            idle_connections,
            max_connections: self.config.max_connections,
            total_messages,
            successful_messages: total_successful,
            success_rate,
        }
    }

    /// 获取当前连接数
    pub fn connection_count(&self) -> usize {
        self.connection_count.load(Ordering::Relaxed)
    }

    /// 检查是否可以接受新连接
    pub fn can_accept_connection(&self) -> bool {
        self.connection_count() < self.config.max_connections
    }

    /// 更新优先级队列
    async fn update_priority_queue(&self) {
        let connections = self.connections.read().await;
        let mut queue: Vec<_> = connections.keys().cloned().collect();

        // 按优先级排序
        queue.sort_by(|a, b| {
            let conn_a = connections.get(a).unwrap();
            let conn_b = connections.get(b).unwrap();
            conn_b.priority.cmp(&conn_a.priority)
        });

        let mut priority_queue = self.priority_queue.write().await;
        *priority_queue = queue;
    }

    /// 启动后台清理任务
    pub async fn start_cleanup_task(&self) {
        let manager = self.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(manager.config.cleanup_interval);

            loop {
                interval.tick().await;
                let _ = manager.cleanup_connections().await;
            }
        });
    }
}

impl Clone for ConnectionManager {
    fn clone(&self) -> Self {
        Self {
            connections: Arc::clone(&self.connections),
            config: self.config.clone(),
            connection_count: Arc::clone(&self.connection_count),
            priority_queue: Arc::clone(&self.priority_queue),
        }
    }
}

impl Default for ConnectionManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 连接管理器统计信息
#[derive(Debug, Clone)]
pub struct ConnectionManagerStats {
    /// 总连接数
    pub total_connections: usize,
    /// 活跃连接数
    pub active_connections: usize,
    /// 空闲连接数
    pub idle_connections: usize,
    /// 最大连接数限制
    pub max_connections: usize,
    /// 总消息数
    pub total_messages: u64,
    /// 成功消息数
    pub successful_messages: u64,
    /// 成功率
    pub success_rate: f64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::Duration;

    #[test]
    fn test_connection_creation() {
        let connection = Connection::new("test-source".to_string(), 1);
        assert!(connection.id.len() > 0);
        assert_eq!(connection.source, "test-source");
        assert_eq!(connection.protocol_version, 1);
        assert_eq!(connection.state, ConnectionState::Connecting);
    }

    #[test]
    fn test_connection_state_transitions() {
        let mut connection = Connection::new("test".to_string(), 1);

        connection.update_state(ConnectionState::Connected);
        assert_eq!(connection.state, ConnectionState::Connected);
        assert!(connection.state.is_active());

        connection.update_state(ConnectionState::Idle);
        assert_eq!(connection.state, ConnectionState::Idle);
        assert!(connection.state.is_available());
    }

    #[test]
    fn test_connection_stats() {
        let mut connection = Connection::new("test".to_string(), 1);

        connection.record_message(true, Duration::from_millis(10));
        connection.record_message(false, Duration::from_millis(20));

        assert_eq!(connection.stats.total_messages, 2);
        assert_eq!(connection.stats.successful_messages, 1);
        assert_eq!(connection.stats.failed_messages, 1);
        assert_eq!(connection.success_rate(), 0.5);
    }

    #[tokio::test]
    async fn test_connection_manager_basic_operations() {
        let manager = ConnectionManager::new();

        let connection = Connection::new("test-source".to_string(), 1);
        let connection_id = connection.id.clone();

        // 添加连接
        let result = manager.add_connection(connection).await;
        assert!(result.is_ok());
        assert_eq!(manager.connection_count(), 1);

        // 获取连接
        let retrieved = manager.get_connection(&connection_id).await;
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().source, "test-source");

        // 移除连接
        let removed = manager.remove_connection(&connection_id).await;
        assert!(removed.is_ok());
        assert!(removed.unwrap().is_some());
        assert_eq!(manager.connection_count(), 0);
    }

    #[tokio::test]
    async fn test_connection_manager_capacity_limit() {
        let config = ConnectionManagerConfig {
            max_connections: 2,
            ..Default::default()
        };
        let manager = ConnectionManager::with_config(config);

        // 添加两个连接
        let conn1 = Connection::new("source1".to_string(), 1);
        let conn2 = Connection::new("source2".to_string(), 1);

        assert!(manager.add_connection(conn1).await.is_ok());
        assert!(manager.add_connection(conn2).await.is_ok());

        // 第三个连接应该失败
        let conn3 = Connection::new("source3".to_string(), 1);
        let result = manager.add_connection(conn3).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_connection_priority_handling() {
        let manager = ConnectionManager::new();

        let high_priority_conn =
            Connection::new("high".to_string(), 1).with_priority(ConnectionPriority::High);
        let normal_priority_conn =
            Connection::new("normal".to_string(), 1).with_priority(ConnectionPriority::Normal);

        manager.add_connection(normal_priority_conn).await.unwrap();
        manager.add_connection(high_priority_conn).await.unwrap();

        let available = manager.get_available_connections().await;
        assert_eq!(available.len(), 2);

        // 高优先级应该排在前面
        assert_eq!(available[0].priority, ConnectionPriority::High);
    }

    #[test]
    fn test_connection_timeout_checks() {
        let connection = Connection::new("test".to_string(), 1);

        // 新连接不应该超时
        assert!(!connection.is_idle(Duration::from_secs(1)));
        assert!(!connection.is_expired(Duration::from_secs(1)));

        // 模拟时间流逝（在实际测试中需要使用mock时间）
        // 这里只测试逻辑正确性
        assert!(connection.lifetime() >= Duration::from_nanos(0));
        assert!(connection.idle_time() >= Duration::from_nanos(0));
    }

    #[tokio::test]
    async fn test_connection_manager_stats() {
        let manager = ConnectionManager::new();

        let connection = Connection::new("test".to_string(), 1);
        manager.add_connection(connection).await.unwrap();

        let stats = manager.get_stats().await;
        assert_eq!(stats.total_connections, 1);
        assert_eq!(stats.active_connections, 1);
    }
}
