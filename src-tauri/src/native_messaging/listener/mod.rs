//! Native Messaging 监听器模块
//!
//! 提供后台监听引擎、连接管理和连接池功能

pub mod background;
pub mod connection;
pub mod pool;

// 公共导出 - 后台监听器
pub use background::{BackgroundListener, MessageHandler, PerformanceMetrics};

// 公共导出 - 连接管理
pub use connection::{
    Connection, ConnectionManager, ConnectionManagerConfig, ConnectionManagerStats,
    ConnectionPriority, ConnectionState, ConnectionStats,
};

// 公共导出 - 连接池
pub use pool::{ConnectionPool, ConnectionPoolConfig, ConnectionPoolStats, LoadBalancingStrategy};

/// 监听器状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ListenerStatus {
    /// 未启动
    Stopped,
    /// 正在启动
    Starting,
    /// 正在运行
    Running,
    /// 正在停止
    Stopping,
    /// 错误状态
    Error,
}

impl ListenerStatus {
    /// 检查监听器是否处于活跃状态
    ///
    /// # 返回
    /// bool - 是否活跃
    pub fn is_active(&self) -> bool {
        matches!(self, ListenerStatus::Starting | ListenerStatus::Running)
    }

    /// 检查监听器是否可以接收新连接
    ///
    /// # 返回
    /// bool - 是否可以接收连接
    pub fn can_accept_connections(&self) -> bool {
        matches!(self, ListenerStatus::Running)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_listener_status() {
        assert!(ListenerStatus::Running.is_active());
        assert!(ListenerStatus::Starting.is_active());
        assert!(!ListenerStatus::Stopped.is_active());

        assert!(ListenerStatus::Running.can_accept_connections());
        assert!(!ListenerStatus::Starting.can_accept_connections());
    }
}
