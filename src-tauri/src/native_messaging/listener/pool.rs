//! Native Messaging 连接池
//!
//! 提供连接池管理和负载均衡功能

use super::connection::{Connection, ConnectionState};
use crate::native_messaging::error::{NativeMessagingError, Result};
use std::collections::{HashMap, VecDeque};
use std::sync::{
    atomic::{AtomicUsize, Ordering},
    Arc,
};
use std::time::{Duration, Instant};
use tokio::sync::{Mutex, RwLock};
use tracing::{debug, info, warn};

/// 负载均衡策略
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[derive(Default)]
pub enum LoadBalancingStrategy {
    /// 轮询
    RoundRobin,
    /// 最少连接数
    #[default]
    LeastConnections,
    /// 加权轮询
    WeightedRoundRobin,
    /// 响应时间优先
    ResponseTimeBased,
    /// 资源使用率优先
    ResourceBased,
}


/// 连接池配置
#[derive(Debug, Clone)]
pub struct ConnectionPoolConfig {
    /// 最大连接数
    pub max_connections: usize,
    /// 最小空闲连接数
    pub min_idle_connections: usize,
    /// 连接空闲超时
    pub idle_timeout: Duration,
    /// 连接获取超时
    pub acquire_timeout: Duration,
    /// 连接验证间隔
    pub validation_interval: Duration,
    /// 负载均衡策略
    pub load_balancing_strategy: LoadBalancingStrategy,
    /// 是否启用连接预热
    pub enable_warmup: bool,
    /// 预热连接数
    pub warmup_connections: usize,
    /// 是否启用健康检查
    pub enable_health_check: bool,
    /// 健康检查间隔
    pub health_check_interval: Duration,
}

impl Default for ConnectionPoolConfig {
    fn default() -> Self {
        Self {
            max_connections: 1000,
            min_idle_connections: 10,
            idle_timeout: Duration::from_secs(300), // 5分钟
            acquire_timeout: Duration::from_secs(30),
            validation_interval: Duration::from_secs(60),
            load_balancing_strategy: LoadBalancingStrategy::default(),
            enable_warmup: true,
            warmup_connections: 5,
            enable_health_check: true,
            health_check_interval: Duration::from_secs(30),
        }
    }
}

/// 连接池统计信息
#[derive(Debug, Clone)]
pub struct ConnectionPoolStats {
    /// 总连接数
    pub total_connections: usize,
    /// 活跃连接数
    pub active_connections: usize,
    /// 空闲连接数
    pub idle_connections: usize,
    /// 最大连接数限制
    pub max_connections: usize,
    /// 平均获取连接时间（毫秒）
    pub avg_acquire_time_ms: f64,
    /// 连接池使用率
    pub utilization_rate: f64,
    /// 总请求数
    pub total_requests: u64,
    /// 成功分配连接数
    pub successful_allocations: u64,
    /// 失败分配连接数
    pub failed_allocations: u64,
    /// 当前等待队列长度
    pub waiting_queue_length: usize,
}

/// 连接池
///
/// 提供高性能的连接池管理和负载均衡功能
pub struct ConnectionPool {
    /// 配置信息
    config: ConnectionPoolConfig,
    /// 所有连接的映射表
    connections: Arc<RwLock<HashMap<String, Connection>>>,
    /// 空闲连接队列
    idle_connections: Arc<Mutex<VecDeque<String>>>,
    /// 活跃连接集合
    active_connections: Arc<RwLock<HashMap<String, Instant>>>,
    /// 当前连接数计数器
    current_connections: Arc<AtomicUsize>,
    /// 轮询计数器（用于轮询负载均衡）
    round_robin_counter: Arc<AtomicUsize>,
    /// 等待队列
    waiting_queue: Arc<Mutex<VecDeque<tokio::sync::oneshot::Sender<Result<String>>>>>,
    /// 统计数据
    stats: Arc<Mutex<PoolStatistics>>,
}

/// 内部统计数据
#[derive(Debug, Default)]
struct PoolStatistics {
    total_requests: u64,
    successful_allocations: u64,
    failed_allocations: u64,
    total_acquire_time_ms: u64,
    acquire_count: u64,
}

impl PoolStatistics {
    fn record_allocation(&mut self, success: bool, acquire_time: Duration) {
        self.total_requests += 1;
        if success {
            self.successful_allocations += 1;
            self.total_acquire_time_ms += acquire_time.as_millis() as u64;
            self.acquire_count += 1;
        } else {
            self.failed_allocations += 1;
        }
    }

    fn avg_acquire_time_ms(&self) -> f64 {
        if self.acquire_count > 0 {
            self.total_acquire_time_ms as f64 / self.acquire_count as f64
        } else {
            0.0
        }
    }
}

impl ConnectionPool {
    /// 创建新的连接池
    ///
    /// # 参数
    /// - `max_connections`: 最大连接数
    ///
    /// # 返回
    /// ConnectionPool - 连接池实例
    pub fn new(max_connections: usize) -> Self {
        let config = ConnectionPoolConfig {
            max_connections,
            ..Default::default()
        };
        Self::with_config(config)
    }

    /// 使用指定配置创建连接池
    pub fn with_config(config: ConnectionPoolConfig) -> Self {
        Self {
            config,
            connections: Arc::new(RwLock::new(HashMap::new())),
            idle_connections: Arc::new(Mutex::new(VecDeque::new())),
            active_connections: Arc::new(RwLock::new(HashMap::new())),
            current_connections: Arc::new(AtomicUsize::new(0)),
            round_robin_counter: Arc::new(AtomicUsize::new(0)),
            waiting_queue: Arc::new(Mutex::new(VecDeque::new())),
            stats: Arc::new(Mutex::new(PoolStatistics::default())),
        }
    }

    /// 添加连接到池中
    ///
    /// # 参数
    /// - `connection`: 要添加的连接
    ///
    /// # 返回
    /// Result<()> - 添加结果
    pub async fn add_connection(&self, mut connection: Connection) -> Result<()> {
        let current_count = self.current_connections.load(Ordering::Relaxed);
        if current_count >= self.config.max_connections {
            return Err(NativeMessagingError::ConnectionError(
                "连接池已满".to_string(),
            ));
        }

        connection.update_state(ConnectionState::Idle);
        let connection_id = connection.id.clone();

        // 添加到连接映射表
        {
            let mut connections = self.connections.write().await;
            connections.insert(connection_id.clone(), connection);
        }

        // 添加到空闲队列
        {
            let mut idle_connections = self.idle_connections.lock().await;
            idle_connections.push_back(connection_id.clone());
        }

        self.current_connections.fetch_add(1, Ordering::Relaxed);
        debug!("连接已添加到池中: {}", connection_id);

        // 检查是否有等待的请求
        self.process_waiting_queue().await;

        Ok(())
    }

    /// 从池中获取连接
    ///
    /// # 返回
    /// Result<String> - 连接ID或错误
    pub async fn acquire_connection(&self) -> Result<String> {
        let start_time = Instant::now();

        // 首先尝试从空闲连接中获取
        if let Some(connection_id) = self.get_idle_connection().await {
            let acquire_time = start_time.elapsed();
            self.record_allocation(true, acquire_time).await;
            return Ok(connection_id);
        }

        // 如果没有空闲连接，检查是否可以创建新连接
        let current_count = self.current_connections.load(Ordering::Relaxed);
        if current_count < self.config.max_connections {
            // 返回占位符，调用者需要创建新连接
            return Err(NativeMessagingError::ConnectionError(
                "需要创建新连接".to_string(),
            ));
        }

        // 连接池已满，加入等待队列
        self.wait_for_connection().await
    }

    /// 释放连接回池中
    ///
    /// # 参数
    /// - `connection_id`: 要释放的连接ID
    ///
    /// # 返回
    /// Result<()> - 释放结果
    pub async fn release_connection(&self, connection_id: &str) -> Result<()> {
        // 从活跃连接中移除
        {
            let mut active_connections = self.active_connections.write().await;
            active_connections.remove(connection_id);
        }

        // 更新连接状态为空闲
        {
            let mut connections = self.connections.write().await;
            if let Some(connection) = connections.get_mut(connection_id) {
                connection.update_state(ConnectionState::Idle);
            } else {
                return Err(NativeMessagingError::ConnectionError(format!(
                    "连接不存在: {}",
                    connection_id
                )));
            }
        }

        // 添加到空闲队列
        {
            let mut idle_connections = self.idle_connections.lock().await;
            idle_connections.push_back(connection_id.to_string());
        }

        debug!("连接已释放回池中: {}", connection_id);

        // 处理等待队列
        self.process_waiting_queue().await;

        Ok(())
    }

    /// 移除连接
    ///
    /// # 参数
    /// - `connection_id`: 要移除的连接ID
    ///
    /// # 返回
    /// Result<()> - 移除结果
    pub async fn remove_connection(&self, connection_id: &str) -> Result<()> {
        // 从所有数据结构中移除连接
        {
            let mut connections = self.connections.write().await;
            connections.remove(connection_id);
        }

        {
            let mut idle_connections = self.idle_connections.lock().await;
            idle_connections.retain(|id| id != connection_id);
        }

        {
            let mut active_connections = self.active_connections.write().await;
            active_connections.remove(connection_id);
        }

        self.current_connections.fetch_sub(1, Ordering::Relaxed);
        info!("连接已从池中移除: {}", connection_id);

        Ok(())
    }

    /// 获取当前连接数
    pub fn current_connections(&self) -> usize {
        self.current_connections.load(Ordering::Relaxed)
    }

    /// 获取最大连接数
    pub fn max_connections(&self) -> usize {
        self.config.max_connections
    }

    /// 检查是否可以创建新连接
    pub fn can_accept_connection(&self) -> bool {
        self.current_connections() < self.config.max_connections
    }

    /// 获取连接池统计信息
    pub async fn get_stats(&self) -> ConnectionPoolStats {
        let connections = self.connections.read().await;
        let idle_connections = self.idle_connections.lock().await;
        let active_connections = self.active_connections.read().await;
        let waiting_queue = self.waiting_queue.lock().await;
        let stats = self.stats.lock().await;

        let total_connections = connections.len();
        let idle_count = idle_connections.len();
        let active_count = active_connections.len();
        let utilization_rate = if self.config.max_connections > 0 {
            total_connections as f64 / self.config.max_connections as f64
        } else {
            0.0
        };

        ConnectionPoolStats {
            total_connections,
            active_connections: active_count,
            idle_connections: idle_count,
            max_connections: self.config.max_connections,
            avg_acquire_time_ms: stats.avg_acquire_time_ms(),
            utilization_rate,
            total_requests: stats.total_requests,
            successful_allocations: stats.successful_allocations,
            failed_allocations: stats.failed_allocations,
            waiting_queue_length: waiting_queue.len(),
        }
    }

    /// 清理过期连接
    pub async fn cleanup_expired_connections(&self) -> usize {
        let mut expired_connections = Vec::new();

        {
            let connections = self.connections.read().await;
            for (id, connection) in connections.iter() {
                if connection.is_idle(self.config.idle_timeout) {
                    expired_connections.push(id.clone());
                }
            }
        }

        let mut removed_count = 0;
        for connection_id in expired_connections {
            if self.remove_connection(&connection_id).await.is_ok() {
                removed_count += 1;
            }
        }

        if removed_count > 0 {
            info!("清理了 {} 个过期连接", removed_count);
        }

        removed_count
    }

    /// 预热连接池
    pub async fn warmup(&self) -> Result<()> {
        if !self.config.enable_warmup {
            return Ok(());
        }

        info!(
            "开始预热连接池，目标连接数: {}",
            self.config.warmup_connections
        );

        for i in 0..self.config.warmup_connections {
            if self.current_connections() >= self.config.max_connections {
                break;
            }

            let connection = Connection::new(
                format!("warmup-{}", i),
                crate::native_messaging::protocol::CURRENT_PROTOCOL_VERSION,
            );

            if let Err(e) = self.add_connection(connection).await {
                warn!("预热连接创建失败: {}", e);
                break;
            }
        }

        info!("连接池预热完成，当前连接数: {}", self.current_connections());
        Ok(())
    }

    /// 启动后台维护任务
    pub async fn start_maintenance_task(&self) {
        let pool = self.clone();
        tokio::spawn(async move {
            let mut cleanup_interval = tokio::time::interval(pool.config.validation_interval);

            loop {
                cleanup_interval.tick().await;
                let _ = pool.cleanup_expired_connections().await;
                let _ = pool.validate_connections().await;
            }
        });
    }

    /// 验证连接有效性
    async fn validate_connections(&self) -> Result<()> {
        let mut invalid_connections = Vec::new();

        {
            let connections = self.connections.read().await;
            for (id, connection) in connections.iter() {
                if !connection.is_healthy() {
                    invalid_connections.push(id.clone());
                }
            }
        }

        for connection_id in invalid_connections {
            warn!("发现无效连接，正在移除: {}", connection_id);
            let _ = self.remove_connection(&connection_id).await;
        }

        Ok(())
    }

    /// 从空闲连接中获取连接（应用负载均衡策略）
    async fn get_idle_connection(&self) -> Option<String> {
        match self.config.load_balancing_strategy {
            LoadBalancingStrategy::RoundRobin => self.get_connection_round_robin().await,
            LoadBalancingStrategy::LeastConnections => {
                self.get_connection_least_connections().await
            }
            LoadBalancingStrategy::ResponseTimeBased => {
                self.get_connection_response_time_based().await
            }
            _ => {
                // 默认策略：简单的先进先出
                let mut idle_connections = self.idle_connections.lock().await;
                idle_connections.pop_front()
            }
        }
    }

    /// 轮询策略获取连接
    async fn get_connection_round_robin(&self) -> Option<String> {
        let mut idle_connections = self.idle_connections.lock().await;
        if idle_connections.is_empty() {
            return None;
        }

        let index =
            self.round_robin_counter.fetch_add(1, Ordering::Relaxed) % idle_connections.len();
        if let Some(connection_id) = idle_connections.get(index).cloned() {
            idle_connections.retain(|id| id != &connection_id);
            return Some(connection_id);
        }

        None
    }

    /// 最少连接数策略获取连接
    async fn get_connection_least_connections(&self) -> Option<String> {
        let connections = self.connections.read().await;
        let mut idle_connections = self.idle_connections.lock().await;

        if idle_connections.is_empty() {
            return None;
        }

        // 找到消息数最少的连接
        let best_connection = idle_connections
            .iter()
            .min_by(|&a, &b| {
                let conn_a = connections.get(a).unwrap();
                let conn_b = connections.get(b).unwrap();
                conn_a
                    .stats
                    .total_messages
                    .cmp(&conn_b.stats.total_messages)
            })
            .cloned();

        if let Some(connection_id) = best_connection {
            idle_connections.retain(|id| id != &connection_id);
            Some(connection_id)
        } else {
            None
        }
    }

    /// 响应时间优先策略获取连接
    async fn get_connection_response_time_based(&self) -> Option<String> {
        let connections = self.connections.read().await;
        let mut idle_connections = self.idle_connections.lock().await;

        if idle_connections.is_empty() {
            return None;
        }

        // 找到平均响应时间最短的连接
        let best_connection = idle_connections
            .iter()
            .min_by(|&a, &b| {
                let conn_a = connections.get(a).unwrap();
                let conn_b = connections.get(b).unwrap();
                conn_a
                    .stats
                    .avg_response_time_ms
                    .partial_cmp(&conn_b.stats.avg_response_time_ms)
                    .unwrap_or(std::cmp::Ordering::Equal)
            })
            .cloned();

        if let Some(connection_id) = best_connection {
            idle_connections.retain(|id| id != &connection_id);
            Some(connection_id)
        } else {
            None
        }
    }

    /// 等待连接可用
    async fn wait_for_connection(&self) -> Result<String> {
        let (tx, rx) = tokio::sync::oneshot::channel();

        {
            let mut waiting_queue = self.waiting_queue.lock().await;
            waiting_queue.push_back(tx);
        }

        // 设置超时
        match tokio::time::timeout(self.config.acquire_timeout, rx).await {
            Ok(Ok(result)) => result,
            Ok(Err(_)) => Err(NativeMessagingError::ConnectionError(
                "等待连接时发生内部错误".to_string(),
            )),
            Err(_) => {
                self.record_allocation(false, self.config.acquire_timeout)
                    .await;
                Err(NativeMessagingError::ConnectionError(
                    "获取连接超时".to_string(),
                ))
            }
        }
    }

    /// 处理等待队列
    async fn process_waiting_queue(&self) {
        let mut waiting_queue = self.waiting_queue.lock().await;

        while let Some(sender) = waiting_queue.pop_front() {
            if let Some(connection_id) = self.get_idle_connection().await {
                // 移动连接到活跃状态
                {
                    let mut active_connections = self.active_connections.write().await;
                    active_connections.insert(connection_id.clone(), Instant::now());
                }

                // 发送连接给等待者
                let _ = sender.send(Ok(connection_id));
            } else {
                // 没有可用连接，放回队列
                waiting_queue.push_front(sender);
                break;
            }
        }
    }

    /// 记录分配统计
    async fn record_allocation(&self, success: bool, acquire_time: Duration) {
        let mut stats = self.stats.lock().await;
        stats.record_allocation(success, acquire_time);
    }
}

impl Clone for ConnectionPool {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            connections: Arc::clone(&self.connections),
            idle_connections: Arc::clone(&self.idle_connections),
            active_connections: Arc::clone(&self.active_connections),
            current_connections: Arc::clone(&self.current_connections),
            round_robin_counter: Arc::clone(&self.round_robin_counter),
            waiting_queue: Arc::clone(&self.waiting_queue),
            stats: Arc::clone(&self.stats),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::Duration;

    #[test]
    fn test_connection_pool_creation() {
        let pool = ConnectionPool::new(100);
        assert_eq!(pool.max_connections(), 100);
        assert_eq!(pool.current_connections(), 0);
        assert!(pool.can_accept_connection());
    }

    #[tokio::test]
    async fn test_connection_pool_basic_operations() {
        let pool = ConnectionPool::new(10);

        // 添加连接
        let connection = Connection::new("test-source".to_string(), 1);
        let connection_id = connection.id.clone();

        assert!(pool.add_connection(connection).await.is_ok());
        assert_eq!(pool.current_connections(), 1);

        // 获取连接
        let acquired_id = pool.acquire_connection().await.unwrap();
        assert_eq!(acquired_id, connection_id);

        // 释放连接
        assert!(pool.release_connection(&acquired_id).await.is_ok());

        // 移除连接
        assert!(pool.remove_connection(&connection_id).await.is_ok());
        assert_eq!(pool.current_connections(), 0);
    }

    #[tokio::test]
    async fn test_connection_pool_capacity_limit() {
        let pool = ConnectionPool::new(2);

        // 添加两个连接
        let conn1 = Connection::new("source1".to_string(), 1);
        let conn2 = Connection::new("source2".to_string(), 1);

        assert!(pool.add_connection(conn1).await.is_ok());
        assert!(pool.add_connection(conn2).await.is_ok());
        assert_eq!(pool.current_connections(), 2);
        assert!(!pool.can_accept_connection());

        // 第三个连接应该失败
        let conn3 = Connection::new("source3".to_string(), 1);
        let result = pool.add_connection(conn3).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_load_balancing_strategies() {
        let config = ConnectionPoolConfig {
            max_connections: 5,
            load_balancing_strategy: LoadBalancingStrategy::LeastConnections,
            ..Default::default()
        };
        let pool = ConnectionPool::with_config(config);

        // 添加多个连接
        for i in 0..3 {
            let connection = Connection::new(format!("source{}", i), 1);
            pool.add_connection(connection).await.unwrap();
        }

        // 测试负载均衡
        let connection_id = pool.acquire_connection().await.unwrap();
        assert!(!connection_id.is_empty());

        pool.release_connection(&connection_id).await.unwrap();
    }

    #[tokio::test]
    async fn test_connection_pool_stats() {
        let pool = ConnectionPool::new(10);

        let connection = Connection::new("test".to_string(), 1);
        pool.add_connection(connection).await.unwrap();

        let stats = pool.get_stats().await;
        assert_eq!(stats.total_connections, 1);
        assert_eq!(stats.idle_connections, 1);
        assert_eq!(stats.active_connections, 0);
        assert_eq!(stats.max_connections, 10);
    }

    #[tokio::test]
    async fn test_connection_pool_cleanup() {
        let config = ConnectionPoolConfig {
            max_connections: 5,
            idle_timeout: Duration::from_millis(1), // 很短的超时时间用于测试
            ..Default::default()
        };
        let pool = ConnectionPool::with_config(config);

        let connection = Connection::new("test".to_string(), 1);
        pool.add_connection(connection).await.unwrap();

        // 等待连接超时
        tokio::time::sleep(Duration::from_millis(10)).await;

        let removed_count = pool.cleanup_expired_connections().await;
        assert_eq!(removed_count, 1);
        assert_eq!(pool.current_connections(), 0);
    }

    #[tokio::test]
    async fn test_connection_pool_warmup() {
        let config = ConnectionPoolConfig {
            max_connections: 10,
            enable_warmup: true,
            warmup_connections: 3,
            ..Default::default()
        };
        let pool = ConnectionPool::with_config(config);

        // 预热应该创建连接（但在这个测试中我们没有实际的连接创建逻辑）
        // 这里只测试接口调用不会出错
        assert!(pool.warmup().await.is_ok());
    }
}
