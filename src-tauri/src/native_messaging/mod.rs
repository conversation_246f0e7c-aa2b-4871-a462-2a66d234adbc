//! Native Messaging 跨平台后台监听模块
//!
//! 提供企业级的浏览器扩展通信系统，支持高性能的后台消息监听能力，
//! 多浏览器、多平台兼容，具备完善的错误处理、性能监控和安全验证机制。

pub mod browser;
pub mod config;
pub mod error;
pub mod handlers;
pub mod listener;
pub mod monitoring;
pub mod protocol;
pub mod security;

// 公共导出
pub use config::NativeMessagingConfig;
pub use error::{NativeMessagingError, Result};

/// Native Messaging 模块版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// 支持的协议版本列表
pub const SUPPORTED_PROTOCOL_VERSIONS: &[u32] = &[1, 2];

/// 设置 Native Messaging 后台监听
///
/// 初始化并启动 Native Messaging 后台监听器
///
/// # 参数
/// - `app_handle`: Tauri 应用句柄
///
/// # 返回
/// Result<()> - 设置结果
pub fn setup_native_messaging(_app_handle: tauri::AppHandle) -> Result<()> {
    // TODO: 在Module 5中实现完整的设置逻辑
    tracing::info!("Native Messaging 模块初始化 (Module 1 基础版本)");

    // 创建默认配置
    let _config = NativeMessagingConfig::default();

    // 验证配置
    _config.validate()?;

    tracing::info!("Native Messaging 配置验证通过");
    Ok(())
}

/// 默认配置常量
pub mod defaults {
    use std::time::Duration;

    /// 默认最大连接数
    pub const MAX_CONNECTIONS: usize = 1000;

    /// 默认消息超时时间
    pub const MESSAGE_TIMEOUT: Duration = Duration::from_secs(30);

    /// 默认缓冲区大小
    pub const BUFFER_SIZE: usize = 8192;

    /// 默认工作线程数
    pub const WORKER_THREADS: usize = 4;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version_info() {
        assert!(!VERSION.is_empty());
        assert!(!SUPPORTED_PROTOCOL_VERSIONS.is_empty());
    }

    #[test]
    fn test_default_constants() {
        assert!(defaults::MAX_CONNECTIONS > 0);
        assert!(defaults::MESSAGE_TIMEOUT.as_secs() > 0);
        assert!(defaults::BUFFER_SIZE > 0);
        assert!(defaults::WORKER_THREADS > 0);
    }
}
