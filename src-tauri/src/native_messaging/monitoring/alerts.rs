//! Native Messaging 告警系统模块
//!
//! 提供告警规则配置、告警触发和通知功能

use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use serde::{Deserialize, Serialize};
use tokio::sync::mpsc;

use super::health::{HealthReport, HealthStatus, HealthCheckResult};
use super::metrics::MetricsSnapshot;

/// 告警级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum AlertLevel {
    /// 信息
    Info = 0,
    /// 警告
    Warning = 1,
    /// 严重
    Critical = 2,
    /// 紧急
    Emergency = 3,
}

impl AlertLevel {
    /// 从健康状态转换为告警级别
    pub fn from_health_status(status: HealthStatus) -> Self {
        match status {
            HealthStatus::Healthy => AlertLevel::Info,
            HealthStatus::Unknown => AlertLevel::Info,
            HealthStatus::Warning => AlertLevel::Warning,
            HealthStatus::Critical => AlertLevel::Critical,
        }
    }

    /// 获取级别名称
    pub fn name(&self) -> &'static str {
        match self {
            AlertLevel::Info => "信息",
            AlertLevel::Warning => "警告",
            AlertLevel::Critical => "严重",
            AlertLevel::Emergency => "紧急",
        }
    }

    /// 获取级别颜色（用于UI显示）
    pub fn color(&self) -> &'static str {
        match self {
            AlertLevel::Info => "#108ee9",     // 蓝色
            AlertLevel::Warning => "#faad14",  // 橙色
            AlertLevel::Critical => "#f5222d", // 红色
            AlertLevel::Emergency => "#722ed1", // 紫色
        }
    }
}

/// 告警规则类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertRuleType {
    /// 性能指标阈值告警
    MetricThreshold {
        /// 指标名称
        metric_name: String,
        /// 阈值
        threshold: f64,
        /// 比较操作符
        operator: ComparisonOperator,
        /// 持续时间（秒）
        duration_seconds: u64,
    },
    /// 健康检查失败告警
    HealthCheck {
        /// 检查项名称
        check_name: String,
        /// 失败状态
        status: HealthStatus,
    },
    /// 连接数异常告警
    ConnectionAnomaly {
        /// 最大连接数
        max_connections: usize,
        /// 增长率阈值（百分比）
        growth_rate_threshold: f64,
    },
    /// 响应时间异常告警
    ResponseTimeAnomaly {
        /// 最大响应时间（毫秒）
        max_response_time_ms: f64,
        /// P95阈值
        p95_threshold_ms: f64,
    },
}

/// 比较操作符
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComparisonOperator {
    /// 大于
    GreaterThan,
    /// 大于等于
    GreaterThanOrEqual,
    /// 小于
    LessThan,
    /// 小于等于
    LessThanOrEqual,
    /// 等于
    Equal,
    /// 不等于
    NotEqual,
}

impl ComparisonOperator {
    /// 执行比较操作
    pub fn compare(&self, left: f64, right: f64) -> bool {
        match self {
            ComparisonOperator::GreaterThan => left > right,
            ComparisonOperator::GreaterThanOrEqual => left >= right,
            ComparisonOperator::LessThan => left < right,
            ComparisonOperator::LessThanOrEqual => left <= right,
            ComparisonOperator::Equal => (left - right).abs() < f64::EPSILON,
            ComparisonOperator::NotEqual => (left - right).abs() >= f64::EPSILON,
        }
    }
}

/// 告警规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertRule {
    /// 规则ID
    pub id: String,
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: String,
    /// 规则类型
    pub rule_type: AlertRuleType,
    /// 告警级别
    pub level: AlertLevel,
    /// 是否启用
    pub enabled: bool,
    /// 静默时间（秒）
    pub silence_duration_seconds: u64,
    /// 通知渠道ID列表
    pub notification_channels: Vec<String>,
    /// 创建时间戳
    pub created_at: u64,
    /// 最后更新时间戳
    pub updated_at: u64,
}

impl AlertRule {
    /// 创建新的告警规则
    pub fn new(
        id: String,
        name: String,
        description: String,
        rule_type: AlertRuleType,
        level: AlertLevel,
    ) -> Self {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        Self {
            id,
            name,
            description,
            rule_type,
            level,
            enabled: true,
            silence_duration_seconds: 300, // 默认5分钟静默时间
            notification_channels: Vec::new(),
            created_at: now,
            updated_at: now,
        }
    }

    /// 添加通知渠道
    pub fn add_notification_channel(mut self, channel_id: String) -> Self {
        self.notification_channels.push(channel_id);
        self
    }

    /// 设置静默时间
    pub fn with_silence_duration(mut self, duration: Duration) -> Self {
        self.silence_duration_seconds = duration.as_secs();
        self
    }

    /// 检查规则是否匹配给定的指标
    pub fn matches_metrics(&self, metrics: &MetricsSnapshot) -> bool {
        if !self.enabled {
            return false;
        }

        match &self.rule_type {
            AlertRuleType::MetricThreshold {
                metric_name,
                threshold,
                operator,
                duration_seconds: _,
            } => {
                let value = match metric_name.as_str() {
                    "success_rate" => metrics.success_rate,
                    "avg_response_time_ms" => metrics.avg_response_time_ms,
                    "cpu_usage_percent" => metrics.cpu_usage_percent,
                    "memory_usage_mb" => metrics.memory_usage_mb,
                    "active_connections" => metrics.active_connections as f64,
                    "messages_per_second" => metrics.messages_per_second,
                    _ => return false,
                };
                operator.compare(value, *threshold)
            }
            AlertRuleType::ConnectionAnomaly {
                max_connections,
                growth_rate_threshold: _,
            } => metrics.active_connections > *max_connections,
            AlertRuleType::ResponseTimeAnomaly {
                max_response_time_ms,
                p95_threshold_ms,
            } => {
                metrics.avg_response_time_ms > *max_response_time_ms
                    || metrics.p95_response_time_ms > *p95_threshold_ms
            }
            AlertRuleType::HealthCheck { .. } => false, // 需要健康检查数据
        }
    }

    /// 检查规则是否匹配给定的健康检查结果
    pub fn matches_health_check(&self, health_check: &HealthCheckResult) -> bool {
        if !self.enabled {
            return false;
        }

        if let AlertRuleType::HealthCheck { check_name, status } = &self.rule_type {
            health_check.name == *check_name && health_check.status == *status
        } else {
            false
        }
    }
}

/// 告警事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertEvent {
    /// 事件ID
    pub id: String,
    /// 规则ID
    pub rule_id: String,
    /// 告警级别
    pub level: AlertLevel,
    /// 告警标题
    pub title: String,
    /// 告警消息
    pub message: String,
    /// 事件详情
    pub details: HashMap<String, String>,
    /// 触发时间戳
    pub triggered_at: u64,
    /// 是否已解决
    pub resolved: bool,
    /// 解决时间戳
    pub resolved_at: Option<u64>,
    /// 通知状态
    pub notification_status: NotificationStatus,
}

/// 通知状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationStatus {
    /// 已发送的通知渠道
    pub sent_channels: Vec<String>,
    /// 发送失败的通知渠道
    pub failed_channels: Vec<String>,
    /// 最后发送时间戳
    pub last_sent_at: Option<u64>,
}

impl AlertEvent {
    /// 创建新的告警事件
    pub fn new(rule: &AlertRule, title: String, message: String) -> Self {
        let id = format!("alert_{}", uuid::Uuid::new_v4());
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        Self {
            id,
            rule_id: rule.id.clone(),
            level: rule.level,
            title,
            message,
            details: HashMap::new(),
            triggered_at: now,
            resolved: false,
            resolved_at: None,
            notification_status: NotificationStatus {
                sent_channels: Vec::new(),
                failed_channels: Vec::new(),
                last_sent_at: None,
            },
        }
    }

    /// 添加事件详情
    pub fn with_detail(mut self, key: &str, value: &str) -> Self {
        self.details.insert(key.to_string(), value.to_string());
        self
    }

    /// 标记为已解决
    pub fn resolve(&mut self) {
        self.resolved = true;
        self.resolved_at = Some(
            SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        );
    }

    /// 获取事件持续时间（秒）
    pub fn duration_seconds(&self) -> u64 {
        let end_time = self.resolved_at.unwrap_or_else(|| {
            SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs()
        });
        end_time.saturating_sub(self.triggered_at)
    }
}

/// 通知渠道特征
#[async_trait::async_trait]
pub trait NotificationChannel: Send + Sync {
    /// 渠道ID
    fn id(&self) -> &str;

    /// 渠道名称
    fn name(&self) -> &str;

    /// 发送通知
    async fn send_notification(&self, event: &AlertEvent) -> Result<(), String>;

    /// 检查渠道是否健康
    async fn health_check(&self) -> bool;
}

/// 日志通知渠道
pub struct LogNotificationChannel {
    id: String,
    name: String,
}

impl LogNotificationChannel {
    pub fn new(id: String, name: String) -> Self {
        Self { id, name }
    }
}

#[async_trait::async_trait]
impl NotificationChannel for LogNotificationChannel {
    fn id(&self) -> &str {
        &self.id
    }

    fn name(&self) -> &str {
        &self.name
    }

    async fn send_notification(&self, event: &AlertEvent) -> Result<(), String> {
        match event.level {
            AlertLevel::Info => {
                log::info!("[告警] {}: {}", event.title, event.message);
            }
            AlertLevel::Warning => {
                log::warn!("[告警] {}: {}", event.title, event.message);
            }
            AlertLevel::Critical | AlertLevel::Emergency => {
                log::error!("[告警] {}: {}", event.title, event.message);
            }
        }
        Ok(())
    }

    async fn health_check(&self) -> bool {
        true // 日志渠道总是健康的
    }
}

/// 控制台通知渠道
pub struct ConsoleNotificationChannel {
    id: String,
    name: String,
}

impl ConsoleNotificationChannel {
    pub fn new(id: String, name: String) -> Self {
        Self { id, name }
    }
}

#[async_trait::async_trait]
impl NotificationChannel for ConsoleNotificationChannel {
    fn id(&self) -> &str {
        &self.id
    }

    fn name(&self) -> &str {
        &self.name
    }

    async fn send_notification(&self, event: &AlertEvent) -> Result<(), String> {
        println!(
            "[{}] [{}] {}: {}",
            event.level.name(),
            chrono::DateTime::from_timestamp(event.triggered_at as i64, 0)
                .map(|dt| dt.format("%Y-%m-%d %H:%M:%S").to_string())
                .unwrap_or_else(|| "Unknown".to_string()),
            event.title,
            event.message
        );
        Ok(())
    }

    async fn health_check(&self) -> bool {
        true // 控制台渠道总是健康的
    }
}

/// 告警管理器
pub struct AlertManager {
    /// 告警规则
    rules: Arc<RwLock<HashMap<String, AlertRule>>>,
    /// 活跃告警事件
    active_events: Arc<RwLock<HashMap<String, AlertEvent>>>,
    /// 告警历史（最近1000个）
    event_history: Arc<RwLock<VecDeque<AlertEvent>>>,
    /// 通知渠道
    notification_channels: Arc<RwLock<HashMap<String, Box<dyn NotificationChannel>>>>,
    /// 事件发送器
    event_sender: mpsc::UnboundedSender<AlertEvent>,
    /// 静默状态（规则ID -> 静默结束时间戳）
    silenced_rules: Arc<RwLock<HashMap<String, u64>>>,
}

impl AlertManager {
    /// 创建新的告警管理器
    pub fn new() -> (Self, mpsc::UnboundedReceiver<AlertEvent>) {
        let (event_sender, event_receiver) = mpsc::unbounded_channel();

        let manager = Self {
            rules: Arc::new(RwLock::new(HashMap::new())),
            active_events: Arc::new(RwLock::new(HashMap::new())),
            event_history: Arc::new(RwLock::new(VecDeque::with_capacity(1000))),
            notification_channels: Arc::new(RwLock::new(HashMap::new())),
            event_sender,
            silenced_rules: Arc::new(RwLock::new(HashMap::new())),
        };

        (manager, event_receiver)
    }

    /// 添加告警规则
    pub async fn add_rule(&self, rule: AlertRule) -> Result<(), String> {
        let mut rules = self.rules.write().map_err(|_| "获取写锁失败")?;
        rules.insert(rule.id.clone(), rule);
        Ok(())
    }

    /// 移除告警规则
    pub async fn remove_rule(&self, rule_id: &str) -> Result<(), String> {
        let mut rules = self.rules.write().map_err(|_| "获取写锁失败")?;
        rules.remove(rule_id);
        Ok(())
    }

    /// 启用/禁用告警规则
    pub async fn toggle_rule(&self, rule_id: &str, enabled: bool) -> Result<(), String> {
        let mut rules = self.rules.write().map_err(|_| "获取写锁失败")?;
        if let Some(rule) = rules.get_mut(rule_id) {
            rule.enabled = enabled;
            rule.updated_at = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();
            Ok(())
        } else {
            Err("规则不存在".to_string())
        }
    }

    /// 添加通知渠道
    pub async fn add_notification_channel(
        &self,
        channel: Box<dyn NotificationChannel>,
    ) -> Result<(), String> {
        let mut channels = self
            .notification_channels
            .write()
            .map_err(|_| "获取写锁失败")?;
        let channel_id = channel.id().to_string();
        channels.insert(channel_id, channel);
        Ok(())
    }

    /// 评估性能指标告警
    pub async fn evaluate_metrics(&self, metrics: &MetricsSnapshot) -> Result<(), String> {
        let rules = self.rules.read().map_err(|_| "获取读锁失败")?;

        for rule in rules.values() {
            if self.is_rule_silenced(&rule.id).await? {
                continue;
            }

            if rule.matches_metrics(metrics) {
                let event = AlertEvent::new(
                    rule,
                    format!("性能指标告警: {}", rule.name),
                    format!("规则 '{}' 触发告警", rule.description),
                )
                .with_detail("trigger_time", &chrono::Utc::now().to_rfc3339())
                .with_detail("success_rate", &format!("{:.2}%", metrics.success_rate * 100.0))
                .with_detail("avg_response_time", &format!("{:.2}ms", metrics.avg_response_time_ms))
                .with_detail("active_connections", &metrics.active_connections.to_string())
                .with_detail("cpu_usage", &format!("{:.2}%", metrics.cpu_usage_percent))
                .with_detail("memory_usage", &format!("{:.2}MB", metrics.memory_usage_mb));

                self.trigger_alert(event).await?;
            }
        }

        Ok(())
    }

    /// 评估健康检查告警
    pub async fn evaluate_health_report(&self, report: &HealthReport) -> Result<(), String> {
        let rules = self.rules.read().map_err(|_| "获取读锁失败")?;

        for check in &report.checks {
            for rule in rules.values() {
                if self.is_rule_silenced(&rule.id).await? {
                    continue;
                }

                if rule.matches_health_check(check) {
                    let event = AlertEvent::new(
                        rule,
                        format!("健康检查告警: {}", check.name),
                        format!("检查项 '{}' 状态异常: {}", check.name, check.message),
                    )
                    .with_detail("check_name", &check.name)
                    .with_detail("status", &format!("{:?}", check.status))
                    .with_detail("duration_ms", &check.duration_ms.to_string())
                    .with_detail("timestamp", &check.timestamp.to_string());

                    self.trigger_alert(event).await?;
                }
            }
        }

        Ok(())
    }

    /// 触发告警
    async fn trigger_alert(&self, mut event: AlertEvent) -> Result<(), String> {
        // 检查是否存在相同的活跃告警
        {
            let active_events = self.active_events.read().map_err(|_| "获取读锁失败")?;
            if active_events.contains_key(&event.rule_id) {
                return Ok(()); // 已有活跃告警，跳过
            }
        }

        // 发送通知
        self.send_notifications(&mut event).await?;

        // 添加到活跃告警
        {
            let mut active_events = self.active_events.write().map_err(|_| "获取写锁失败")?;
            active_events.insert(event.rule_id.clone(), event.clone());
        }

        // 添加到历史记录
        {
            let mut history = self.event_history.write().map_err(|_| "获取写锁失败")?;
            history.push_back(event.clone());
            if history.len() > 1000 {
                history.pop_front();
            }
        }

        // 发送事件到通道
        self.event_sender
            .send(event)
            .map_err(|_| "发送事件失败")?;

        Ok(())
    }

    /// 解决告警
    pub async fn resolve_alert(&self, rule_id: &str) -> Result<(), String> {
        let mut active_events = self.active_events.write().map_err(|_| "获取写锁失败")?;

        if let Some(mut event) = active_events.remove(rule_id) {
            event.resolve();

            // 更新历史记录
            let mut history = self.event_history.write().map_err(|_| "获取写锁失败")?;
            if let Some(history_event) = history.iter_mut().rev().find(|e| e.rule_id == rule_id) {
                history_event.resolve();
            }
        }

        Ok(())
    }

    /// 静默告警规则
    pub async fn silence_rule(&self, rule_id: &str, duration: Duration) -> Result<(), String> {
        let silence_until = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
            + duration.as_secs();

        let mut silenced = self.silenced_rules.write().map_err(|_| "获取写锁失败")?;
        silenced.insert(rule_id.to_string(), silence_until);

        Ok(())
    }

    /// 检查规则是否被静默
    async fn is_rule_silenced(&self, rule_id: &str) -> Result<bool, String> {
        let silenced = self.silenced_rules.read().map_err(|_| "获取读锁失败")?;
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        if let Some(&silence_until) = silenced.get(rule_id) {
            Ok(now < silence_until)
        } else {
            Ok(false)
        }
    }

    /// 发送通知
    async fn send_notifications(&self, event: &mut AlertEvent) -> Result<(), String> {
        let rules = self.rules.read().map_err(|_| "获取读锁失败")?;
        let channels = self
            .notification_channels
            .read()
            .map_err(|_| "获取读锁失败")?;

        if let Some(rule) = rules.get(&event.rule_id) {
            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();

            for channel_id in &rule.notification_channels {
                if let Some(channel) = channels.get(channel_id) {
                    match channel.send_notification(event).await {
                        Ok(_) => {
                            event.notification_status.sent_channels.push(channel_id.clone());
                        }
                        Err(e) => {
                            log::error!("发送通知失败 [{}]: {}", channel_id, e);
                            event
                                .notification_status
                                .failed_channels
                                .push(channel_id.clone());
                        }
                    }
                }
            }

            event.notification_status.last_sent_at = Some(now);
        }

        Ok(())
    }

    /// 获取活跃告警
    pub async fn get_active_alerts(&self) -> Result<Vec<AlertEvent>, String> {
        let active_events = self.active_events.read().map_err(|_| "获取读锁失败")?;
        Ok(active_events.values().cloned().collect())
    }

    /// 获取告警历史
    pub async fn get_alert_history(&self, limit: Option<usize>) -> Result<Vec<AlertEvent>, String> {
        let history = self.event_history.read().map_err(|_| "获取读锁失败")?;
        let events: Vec<AlertEvent> = history.iter().cloned().collect();

        if let Some(limit) = limit {
            Ok(events.into_iter().rev().take(limit).collect())
        } else {
            Ok(events.into_iter().rev().collect())
        }
    }

    /// 获取告警统计
    pub async fn get_alert_statistics(&self) -> Result<AlertStatistics, String> {
        let active_events = self.active_events.read().map_err(|_| "获取读锁失败")?;
        let history = self.event_history.read().map_err(|_| "获取读锁失败")?;

        let mut stats = AlertStatistics {
            total_active_alerts: active_events.len(),
            total_alerts_today: 0,
            alerts_by_level: HashMap::new(),
            top_alert_rules: HashMap::new(),
        };

        let today_start = chrono::Utc::now()
            .date_naive()
            .and_hms_opt(0, 0, 0)
            .unwrap()
            .and_utc()
            .timestamp() as u64;

        for event in history.iter() {
            if event.triggered_at >= today_start {
                stats.total_alerts_today += 1;
            }

            *stats.alerts_by_level.entry(event.level).or_insert(0) += 1;
            *stats
                .top_alert_rules
                .entry(event.rule_id.clone())
                .or_insert(0) += 1;
        }

        Ok(stats)
    }
}

impl Default for AlertManager {
    fn default() -> Self {
        Self::new().0
    }
}

/// 告警统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertStatistics {
    /// 当前活跃告警数
    pub total_active_alerts: usize,
    /// 今日告警总数
    pub total_alerts_today: usize,
    /// 按级别分组的告警数
    pub alerts_by_level: HashMap<AlertLevel, usize>,
    /// 触发次数最多的规则
    pub top_alert_rules: HashMap<String, usize>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration as StdDuration;

    #[test]
    fn test_alert_level() {
        assert_eq!(AlertLevel::from_health_status(HealthStatus::Healthy), AlertLevel::Info);
        assert_eq!(AlertLevel::from_health_status(HealthStatus::Warning), AlertLevel::Warning);
        assert_eq!(AlertLevel::from_health_status(HealthStatus::Critical), AlertLevel::Critical);

        assert_eq!(AlertLevel::Info.name(), "信息");
        assert_eq!(AlertLevel::Warning.name(), "警告");
        assert_eq!(AlertLevel::Critical.name(), "严重");
        assert_eq!(AlertLevel::Emergency.name(), "紧急");

        assert_eq!(AlertLevel::Info.color(), "#108ee9");
        assert_eq!(AlertLevel::Warning.color(), "#faad14");
        assert_eq!(AlertLevel::Critical.color(), "#f5222d");
        assert_eq!(AlertLevel::Emergency.color(), "#722ed1");
    }

    #[test]
    fn test_comparison_operator() {
        assert!(ComparisonOperator::GreaterThan.compare(10.0, 5.0));
        assert!(!ComparisonOperator::GreaterThan.compare(5.0, 10.0));

        assert!(ComparisonOperator::GreaterThanOrEqual.compare(10.0, 10.0));
        assert!(ComparisonOperator::GreaterThanOrEqual.compare(10.0, 5.0));

        assert!(ComparisonOperator::LessThan.compare(5.0, 10.0));
        assert!(!ComparisonOperator::LessThan.compare(10.0, 5.0));

        assert!(ComparisonOperator::LessThanOrEqual.compare(5.0, 5.0));
        assert!(ComparisonOperator::LessThanOrEqual.compare(5.0, 10.0));

        assert!(ComparisonOperator::Equal.compare(5.0, 5.0));
        assert!(!ComparisonOperator::Equal.compare(5.0, 10.0));

        assert!(ComparisonOperator::NotEqual.compare(5.0, 10.0));
        assert!(!ComparisonOperator::NotEqual.compare(5.0, 5.0));
    }

    #[test]
    fn test_alert_rule() {
        let rule = AlertRule::new(
            "test_rule".to_string(),
            "测试规则".to_string(),
            "测试用的告警规则".to_string(),
            AlertRuleType::MetricThreshold {
                metric_name: "cpu_usage_percent".to_string(),
                threshold: 80.0,
                operator: ComparisonOperator::GreaterThan,
                duration_seconds: 300,
            },
            AlertLevel::Warning,
        )
        .add_notification_channel("console".to_string())
        .with_silence_duration(StdDuration::from_secs(600));

        assert_eq!(rule.id, "test_rule");
        assert_eq!(rule.name, "测试规则");
        assert_eq!(rule.level, AlertLevel::Warning);
        assert!(rule.enabled);
        assert_eq!(rule.silence_duration_seconds, 600);
        assert_eq!(rule.notification_channels, vec!["console"]);
    }

    #[test]
    fn test_alert_rule_matches_metrics() {
        let rule = AlertRule::new(
            "cpu_rule".to_string(),
            "CPU 使用率告警".to_string(),
            "CPU 使用率过高".to_string(),
            AlertRuleType::MetricThreshold {
                metric_name: "cpu_usage_percent".to_string(),
                threshold: 80.0,
                operator: ComparisonOperator::GreaterThan,
                duration_seconds: 300,
            },
            AlertLevel::Warning,
        );

        let metrics = MetricsSnapshot {
            total_messages: 1000,
            successful_messages: 950,
            failed_messages: 50,
            success_rate: 0.95,
            avg_response_time_ms: 25.0,
            min_response_time_ms: 5.0,
            max_response_time_ms: 100.0,
            p95_response_time_ms: 45.0,
            p99_response_time_ms: 80.0,
            active_connections: 500,
            max_connections: 800,
            memory_usage_mb: 256.0,
            cpu_usage_percent: 85.0, // 超过阈值
            messages_per_second: 100.0,
            uptime_seconds: 3600,
            timestamp: 1234567890,
        };

        assert!(rule.matches_metrics(&metrics));

        // 测试禁用的规则
        let mut disabled_rule = rule.clone();
        disabled_rule.enabled = false;
        assert!(!disabled_rule.matches_metrics(&metrics));
    }

    #[test]
    fn test_alert_event() {
        let rule = AlertRule::new(
            "test_rule".to_string(),
            "测试规则".to_string(),
            "测试".to_string(),
            AlertRuleType::MetricThreshold {
                metric_name: "cpu_usage_percent".to_string(),
                threshold: 80.0,
                operator: ComparisonOperator::GreaterThan,
                duration_seconds: 300,
            },
            AlertLevel::Critical,
        );

        let mut event = AlertEvent::new(&rule, "CPU告警".to_string(), "CPU使用率过高".to_string())
            .with_detail("cpu_usage", "85%");

        assert_eq!(event.rule_id, "test_rule");
        assert_eq!(event.level, AlertLevel::Critical);
        assert_eq!(event.title, "CPU告警");
        assert!(!event.resolved);
        assert!(event.details.contains_key("cpu_usage"));

        let start_duration = event.duration_seconds();
        std::thread::sleep(StdDuration::from_millis(10));

        event.resolve();
        assert!(event.resolved);
        assert!(event.resolved_at.is_some());
        assert!(event.duration_seconds() >= start_duration);
    }

    #[tokio::test]
    async fn test_log_notification_channel() {
        let channel = LogNotificationChannel::new("log".to_string(), "日志通知".to_string());

        assert_eq!(channel.id(), "log");
        assert_eq!(channel.name(), "日志通知");
        assert!(channel.health_check().await);

        let rule = AlertRule::new(
            "test".to_string(),
            "测试".to_string(),
            "测试".to_string(),
            AlertRuleType::MetricThreshold {
                metric_name: "cpu_usage_percent".to_string(),
                threshold: 80.0,
                operator: ComparisonOperator::GreaterThan,
                duration_seconds: 300,
            },
            AlertLevel::Info,
        );

        let event = AlertEvent::new(&rule, "测试告警".to_string(), "这是一个测试".to_string());

        assert!(channel.send_notification(&event).await.is_ok());
    }

    #[tokio::test]
    async fn test_console_notification_channel() {
        let channel = ConsoleNotificationChannel::new("console".to_string(), "控制台通知".to_string());

        assert_eq!(channel.id(), "console");
        assert_eq!(channel.name(), "控制台通知");
        assert!(channel.health_check().await);

        let rule = AlertRule::new(
            "test".to_string(),
            "测试".to_string(),
            "测试".to_string(),
            AlertRuleType::MetricThreshold {
                metric_name: "cpu_usage_percent".to_string(),
                threshold: 80.0,
                operator: ComparisonOperator::GreaterThan,
                duration_seconds: 300,
            },
            AlertLevel::Warning,
        );

        let event = AlertEvent::new(&rule, "测试告警".to_string(), "这是一个测试".to_string());

        assert!(channel.send_notification(&event).await.is_ok());
    }

    #[tokio::test]
    async fn test_alert_manager() {
        let (manager, _receiver) = AlertManager::new();

        // 添加通知渠道
        let log_channel = Box::new(LogNotificationChannel::new(
            "log".to_string(),
            "日志通知".to_string(),
        ));
        assert!(manager.add_notification_channel(log_channel).await.is_ok());

        // 添加告警规则
        let rule = AlertRule::new(
            "cpu_rule".to_string(),
            "CPU 告警".to_string(),
            "CPU 使用率过高".to_string(),
            AlertRuleType::MetricThreshold {
                metric_name: "cpu_usage_percent".to_string(),
                threshold: 80.0,
                operator: ComparisonOperator::GreaterThan,
                duration_seconds: 300,
            },
            AlertLevel::Warning,
        )
        .add_notification_channel("log".to_string());

        assert!(manager.add_rule(rule).await.is_ok());

        // 测试指标评估
        let metrics = MetricsSnapshot {
            total_messages: 1000,
            successful_messages: 950,
            failed_messages: 50,
            success_rate: 0.95,
            avg_response_time_ms: 25.0,
            min_response_time_ms: 5.0,
            max_response_time_ms: 100.0,
            p95_response_time_ms: 45.0,
            p99_response_time_ms: 80.0,
            active_connections: 500,
            max_connections: 800,
            memory_usage_mb: 256.0,
            cpu_usage_percent: 85.0, // 超过阈值
            messages_per_second: 100.0,
            uptime_seconds: 3600,
            timestamp: 1234567890,
        };

        assert!(manager.evaluate_metrics(&metrics).await.is_ok());

        // 检查活跃告警
        let active_alerts = manager.get_active_alerts().await.unwrap();
        assert_eq!(active_alerts.len(), 1);

        // 解决告警
        assert!(manager.resolve_alert("cpu_rule").await.is_ok());

        let active_alerts = manager.get_active_alerts().await.unwrap();
        assert_eq!(active_alerts.len(), 0);

        // 获取历史记录
        let history = manager.get_alert_history(Some(10)).await.unwrap();
        assert_eq!(history.len(), 1);

        // 获取统计信息
        let stats = manager.get_alert_statistics().await.unwrap();
        assert_eq!(stats.total_active_alerts, 0);
    }

    #[tokio::test]
    async fn test_alert_manager_silence() {
        let (manager, _receiver) = AlertManager::new();

        let rule = AlertRule::new(
            "test_rule".to_string(),
            "测试规则".to_string(),
            "测试".to_string(),
            AlertRuleType::MetricThreshold {
                metric_name: "cpu_usage_percent".to_string(),
                threshold: 80.0,
                operator: ComparisonOperator::GreaterThan,
                duration_seconds: 300,
            },
            AlertLevel::Warning,
        );

        assert!(manager.add_rule(rule).await.is_ok());

        // 静默规则
        assert!(manager
            .silence_rule("test_rule", StdDuration::from_secs(300))
            .await
            .is_ok());

        // 创建触发条件的指标
        let metrics = MetricsSnapshot {
            total_messages: 1000,
            successful_messages: 950,
            failed_messages: 50,
            success_rate: 0.95,
            avg_response_time_ms: 25.0,
            min_response_time_ms: 5.0,
            max_response_time_ms: 100.0,
            p95_response_time_ms: 45.0,
            p99_response_time_ms: 80.0,
            active_connections: 500,
            max_connections: 800,
            memory_usage_mb: 256.0,
            cpu_usage_percent: 85.0, // 超过阈值
            messages_per_second: 100.0,
            uptime_seconds: 3600,
            timestamp: 1234567890,
        };

        assert!(manager.evaluate_metrics(&metrics).await.is_ok());

        // 由于规则被静默，不应该产生活跃告警
        let active_alerts = manager.get_active_alerts().await.unwrap();
        assert_eq!(active_alerts.len(), 0);
    }

    #[tokio::test]
    async fn test_alert_manager_toggle_rule() {
        let (manager, _receiver) = AlertManager::new();

        let rule = AlertRule::new(
            "toggle_rule".to_string(),
            "切换规则".to_string(),
            "测试切换".to_string(),
            AlertRuleType::MetricThreshold {
                metric_name: "cpu_usage_percent".to_string(),
                threshold: 80.0,
                operator: ComparisonOperator::GreaterThan,
                duration_seconds: 300,
            },
            AlertLevel::Warning,
        );

        assert!(manager.add_rule(rule).await.is_ok());

        // 禁用规则
        assert!(manager.toggle_rule("toggle_rule", false).await.is_ok());

        // 尝试切换不存在的规则
        assert!(manager.toggle_rule("nonexistent", true).await.is_err());
    }
}
