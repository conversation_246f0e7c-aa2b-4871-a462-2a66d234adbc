//! Native Messaging 健康检查模块
//!
//! 提供系统健康监控、检查器和诊断功能

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use serde::{Deserialize, Serialize};
use tokio::time::timeout;

use super::metrics::{PerformanceMetrics, PerformanceThresholds};

/// 健康状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum HealthStatus {
    /// 健康
    Healthy,
    /// 警告
    Warning,
    /// 严重问题
    Critical,
    /// 未知状态
    Unknown,
}

impl HealthStatus {
    /// 判断状态是否正常
    pub fn is_healthy(&self) -> bool {
        matches!(self, HealthStatus::Healthy)
    }

    /// 判断状态是否需要关注
    pub fn needs_attention(&self) -> bool {
        matches!(self, HealthStatus::Warning | HealthStatus::Critical)
    }

    /// 获取状态优先级（数值越高优先级越高）
    pub fn priority(&self) -> u8 {
        match self {
            HealthStatus::Healthy => 0,
            HealthStatus::Unknown => 1,
            HealthStatus::Warning => 2,
            HealthStatus::Critical => 3,
        }
    }
}

/// 健康检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckResult {
    /// 检查项名称
    pub name: String,
    /// 健康状态
    pub status: HealthStatus,
    /// 状态消息
    pub message: String,
    /// 详细信息
    pub details: HashMap<String, String>,
    /// 检查时间戳
    pub timestamp: u64,
    /// 检查耗时（毫秒）
    pub duration_ms: u64,
}

impl HealthCheckResult {
    /// 创建健康状态结果
    pub fn healthy(name: String, message: String) -> Self {
        Self {
            name,
            status: HealthStatus::Healthy,
            message,
            details: HashMap::new(),
            timestamp: Self::current_timestamp(),
            duration_ms: 0,
        }
    }

    /// 创建警告状态结果
    pub fn warning(name: String, message: String) -> Self {
        Self {
            name,
            status: HealthStatus::Warning,
            message,
            details: HashMap::new(),
            timestamp: Self::current_timestamp(),
            duration_ms: 0,
        }
    }

    /// 创建严重问题状态结果
    pub fn critical(name: String, message: String) -> Self {
        Self {
            name,
            status: HealthStatus::Critical,
            message,
            details: HashMap::new(),
            timestamp: Self::current_timestamp(),
            duration_ms: 0,
        }
    }

    /// 创建未知状态结果
    pub fn unknown(name: String, message: String) -> Self {
        Self {
            name,
            status: HealthStatus::Unknown,
            message,
            details: HashMap::new(),
            timestamp: Self::current_timestamp(),
            duration_ms: 0,
        }
    }

    /// 添加详细信息
    pub fn with_detail(mut self, key: &str, value: &str) -> Self {
        self.details.insert(key.to_string(), value.to_string());
        self
    }

    /// 设置检查耗时
    pub fn with_duration(mut self, duration: Duration) -> Self {
        self.duration_ms = duration.as_millis() as u64;
        self
    }

    /// 获取当前时间戳
    fn current_timestamp() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    }
}

/// 系统健康报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthReport {
    /// 整体健康状态
    pub overall_status: HealthStatus,
    /// 各项检查结果
    pub checks: Vec<HealthCheckResult>,
    /// 报告生成时间戳
    pub timestamp: u64,
    /// 检查总耗时（毫秒）
    pub total_duration_ms: u64,
    /// 系统运行时间（秒）
    pub uptime_seconds: u64,
}

impl HealthReport {
    /// 创建新的健康报告
    pub fn new(checks: Vec<HealthCheckResult>, uptime_seconds: u64) -> Self {
        let total_duration_ms = checks.iter().map(|c| c.duration_ms).sum();
        let overall_status = Self::calculate_overall_status(&checks);

        Self {
            overall_status,
            checks,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            total_duration_ms,
            uptime_seconds,
        }
    }

    /// 计算整体健康状态
    fn calculate_overall_status(checks: &[HealthCheckResult]) -> HealthStatus {
        if checks.is_empty() {
            return HealthStatus::Unknown;
        }

        let mut max_priority = 0;
        for check in checks {
            let priority = check.status.priority();
            if priority > max_priority {
                max_priority = priority;
            }
        }

        match max_priority {
            0 => HealthStatus::Healthy,
            1 => HealthStatus::Unknown,
            2 => HealthStatus::Warning,
            _ => HealthStatus::Critical,
        }
    }

    /// 获取警告和严重问题的数量
    pub fn get_issue_count(&self) -> (usize, usize) {
        let mut warnings = 0;
        let mut criticals = 0;

        for check in &self.checks {
            match check.status {
                HealthStatus::Warning => warnings += 1,
                HealthStatus::Critical => criticals += 1,
                _ => {}
            }
        }

        (warnings, criticals)
    }

    /// 获取失败的检查项
    pub fn get_failed_checks(&self) -> Vec<&HealthCheckResult> {
        self.checks
            .iter()
            .filter(|c| c.status.needs_attention())
            .collect()
    }
}

/// 健康检查器特征
#[async_trait::async_trait]
pub trait HealthChecker: Send + Sync {
    /// 检查器名称
    fn name(&self) -> &str;

    /// 执行健康检查
    async fn check(&self) -> HealthCheckResult;

    /// 检查超时时间（默认30秒）
    fn timeout(&self) -> Duration {
        Duration::from_secs(30)
    }
}

/// 性能指标健康检查器
pub struct MetricsHealthChecker {
    metrics: Arc<PerformanceMetrics>,
    thresholds: PerformanceThresholds,
}

impl MetricsHealthChecker {
    /// 创建性能指标检查器
    pub fn new(metrics: Arc<PerformanceMetrics>, thresholds: PerformanceThresholds) -> Self {
        Self { metrics, thresholds }
    }
}

#[async_trait::async_trait]
impl HealthChecker for MetricsHealthChecker {
    fn name(&self) -> &str {
        "performance_metrics"
    }

    async fn check(&self) -> HealthCheckResult {
        let start_time = std::time::Instant::now();
        let snapshot = self.metrics.get_snapshot();

        let violations = super::metrics::check_performance_thresholds(&snapshot, &self.thresholds);

        let duration = start_time.elapsed();

        if violations.is_empty() {
            HealthCheckResult::healthy(
                self.name().to_string(),
                "性能指标正常".to_string(),
            )
            .with_detail("success_rate", &format!("{:.2}%", snapshot.success_rate * 100.0))
            .with_detail("avg_response_time", &format!("{:.2}ms", snapshot.avg_response_time_ms))
            .with_detail("active_connections", &snapshot.active_connections.to_string())
            .with_detail("cpu_usage", &format!("{:.2}%", snapshot.cpu_usage_percent))
            .with_detail("memory_usage", &format!("{:.2}MB", snapshot.memory_usage_mb))
            .with_duration(duration)
        } else if violations.len() <= 2 {
            HealthCheckResult::warning(
                self.name().to_string(),
                format!("检测到 {} 个性能警告", violations.len()),
            )
            .with_detail("violations", &violations.join("; "))
            .with_detail("success_rate", &format!("{:.2}%", snapshot.success_rate * 100.0))
            .with_detail("avg_response_time", &format!("{:.2}ms", snapshot.avg_response_time_ms))
            .with_duration(duration)
        } else {
            HealthCheckResult::critical(
                self.name().to_string(),
                format!("检测到 {} 个严重性能问题", violations.len()),
            )
            .with_detail("violations", &violations.join("; "))
            .with_detail("success_rate", &format!("{:.2}%", snapshot.success_rate * 100.0))
            .with_detail("avg_response_time", &format!("{:.2}ms", snapshot.avg_response_time_ms))
            .with_duration(duration)
        }
    }
}

/// 内存健康检查器
pub struct MemoryHealthChecker {
    max_memory_mb: f64,
}

impl MemoryHealthChecker {
    /// 创建内存检查器
    pub fn new(max_memory_mb: f64) -> Self {
        Self { max_memory_mb }
    }

    /// 获取当前内存使用量（MB）
    async fn get_memory_usage(&self) -> f64 {
        // 模拟获取内存使用量
        // 在实际实现中，这里应该调用系统API获取真实的内存使用情况
        #[cfg(target_os = "macos")]
        {
            self.get_memory_usage_macos().await
        }
        #[cfg(target_os = "linux")]
        {
            self.get_memory_usage_linux().await
        }
        #[cfg(target_os = "windows")]
        {
            self.get_memory_usage_windows().await
        }
        #[cfg(not(any(target_os = "macos", target_os = "linux", target_os = "windows")))]
        {
            0.0 // 其他平台返回0
        }
    }

    #[cfg(target_os = "macos")]
    async fn get_memory_usage_macos(&self) -> f64 {
        // 在实际实现中，这里会调用 macOS 的系统API
        // 目前返回模拟值
        50.0
    }

    #[cfg(target_os = "linux")]
    async fn get_memory_usage_linux(&self) -> f64 {
        // 在实际实现中，这里会读取 /proc/meminfo
        // 目前返回模拟值
        50.0
    }

    #[cfg(target_os = "windows")]
    async fn get_memory_usage_windows(&self) -> f64 {
        // 在实际实现中，这里会调用 Windows API
        // 目前返回模拟值
        50.0
    }
}

#[async_trait::async_trait]
impl HealthChecker for MemoryHealthChecker {
    fn name(&self) -> &str {
        "memory_usage"
    }

    async fn check(&self) -> HealthCheckResult {
        let start_time = std::time::Instant::now();
        let memory_mb = self.get_memory_usage().await;
        let duration = start_time.elapsed();

        let usage_percent = (memory_mb / self.max_memory_mb) * 100.0;

        if usage_percent < 70.0 {
            HealthCheckResult::healthy(
                self.name().to_string(),
                format!("内存使用正常: {:.1}MB ({:.1}%)", memory_mb, usage_percent),
            )
            .with_detail("memory_mb", &format!("{:.1}", memory_mb))
            .with_detail("usage_percent", &format!("{:.1}%", usage_percent))
            .with_detail("max_memory_mb", &format!("{:.1}", self.max_memory_mb))
            .with_duration(duration)
        } else if usage_percent < 90.0 {
            HealthCheckResult::warning(
                self.name().to_string(),
                format!("内存使用较高: {:.1}MB ({:.1}%)", memory_mb, usage_percent),
            )
            .with_detail("memory_mb", &format!("{:.1}", memory_mb))
            .with_detail("usage_percent", &format!("{:.1}%", usage_percent))
            .with_duration(duration)
        } else {
            HealthCheckResult::critical(
                self.name().to_string(),
                format!("内存使用过高: {:.1}MB ({:.1}%)", memory_mb, usage_percent),
            )
            .with_detail("memory_mb", &format!("{:.1}", memory_mb))
            .with_detail("usage_percent", &format!("{:.1}%", usage_percent))
            .with_duration(duration)
        }
    }
}

/// 连接健康检查器
pub struct ConnectionHealthChecker {
    max_connections: usize,
    metrics: Arc<PerformanceMetrics>,
}

impl ConnectionHealthChecker {
    /// 创建连接检查器
    pub fn new(max_connections: usize, metrics: Arc<PerformanceMetrics>) -> Self {
        Self {
            max_connections,
            metrics,
        }
    }
}

#[async_trait::async_trait]
impl HealthChecker for ConnectionHealthChecker {
    fn name(&self) -> &str {
        "connections"
    }

    async fn check(&self) -> HealthCheckResult {
        let start_time = std::time::Instant::now();
        let snapshot = self.metrics.get_snapshot();
        let duration = start_time.elapsed();

        let active_connections = snapshot.active_connections;
        let usage_percent = (active_connections as f64 / self.max_connections as f64) * 100.0;

        if usage_percent < 70.0 {
            HealthCheckResult::healthy(
                self.name().to_string(),
                format!("连接数正常: {} ({:.1}%)", active_connections, usage_percent),
            )
            .with_detail("active_connections", &active_connections.to_string())
            .with_detail("max_connections", &self.max_connections.to_string())
            .with_detail("usage_percent", &format!("{:.1}%", usage_percent))
            .with_duration(duration)
        } else if usage_percent < 90.0 {
            HealthCheckResult::warning(
                self.name().to_string(),
                format!("连接数较高: {} ({:.1}%)", active_connections, usage_percent),
            )
            .with_detail("active_connections", &active_connections.to_string())
            .with_detail("usage_percent", &format!("{:.1}%", usage_percent))
            .with_duration(duration)
        } else {
            HealthCheckResult::critical(
                self.name().to_string(),
                format!("连接数过高: {} ({:.1}%)", active_connections, usage_percent),
            )
            .with_detail("active_connections", &active_connections.to_string())
            .with_detail("usage_percent", &format!("{:.1}%", usage_percent))
            .with_duration(duration)
        }
    }
}

/// 健康监控管理器
pub struct HealthMonitor {
    checkers: Vec<Box<dyn HealthChecker>>,
    check_timeout: Duration,
}

impl HealthMonitor {
    /// 创建健康监控管理器
    pub fn new() -> Self {
        Self {
            checkers: Vec::new(),
            check_timeout: Duration::from_secs(30),
        }
    }

    /// 设置检查超时时间
    pub fn with_timeout(mut self, timeout: Duration) -> Self {
        self.check_timeout = timeout;
        self
    }

    /// 添加健康检查器
    pub fn add_checker(&mut self, checker: Box<dyn HealthChecker>) {
        self.checkers.push(checker);
    }

    /// 执行所有健康检查
    pub async fn check_all(&self) -> HealthReport {
        let uptime_seconds = self.get_uptime_seconds();
        let mut results = Vec::new();

        for checker in &self.checkers {
            let result = match timeout(self.check_timeout, checker.check()).await {
                Ok(result) => result,
                Err(_) => HealthCheckResult::critical(
                    checker.name().to_string(),
                    "健康检查超时".to_string(),
                ).with_detail("timeout", &format!("{}s", self.check_timeout.as_secs())),
            };
            results.push(result);
        }

        HealthReport::new(results, uptime_seconds)
    }

    /// 执行单个健康检查
    pub async fn check_single(&self, name: &str) -> Option<HealthCheckResult> {
        for checker in &self.checkers {
            if checker.name() == name {
                return match timeout(self.check_timeout, checker.check()).await {
                    Ok(result) => Some(result),
                    Err(_) => Some(HealthCheckResult::critical(
                        name.to_string(),
                        "健康检查超时".to_string(),
                    )),
                };
            }
        }
        None
    }

    /// 获取系统运行时间（秒）
    fn get_uptime_seconds(&self) -> u64 {
        // 在实际实现中，这里应该获取系统启动时间
        // 目前返回模拟值
        3600 // 1小时
    }
}

impl Default for HealthMonitor {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;

    #[test]
    fn test_health_status() {
        assert!(HealthStatus::Healthy.is_healthy());
        assert!(!HealthStatus::Warning.is_healthy());
        assert!(!HealthStatus::Critical.is_healthy());
        assert!(!HealthStatus::Unknown.is_healthy());

        assert!(!HealthStatus::Healthy.needs_attention());
        assert!(HealthStatus::Warning.needs_attention());
        assert!(HealthStatus::Critical.needs_attention());
        assert!(!HealthStatus::Unknown.needs_attention());

        assert_eq!(HealthStatus::Healthy.priority(), 0);
        assert_eq!(HealthStatus::Unknown.priority(), 1);
        assert_eq!(HealthStatus::Warning.priority(), 2);
        assert_eq!(HealthStatus::Critical.priority(), 3);
    }

    #[test]
    fn test_health_check_result() {
        let result = HealthCheckResult::healthy(
            "test".to_string(),
            "All good".to_string(),
        )
        .with_detail("key1", "value1")
        .with_detail("key2", "value2")
        .with_duration(Duration::from_millis(100));

        assert_eq!(result.name, "test");
        assert_eq!(result.status, HealthStatus::Healthy);
        assert_eq!(result.message, "All good");
        assert_eq!(result.details.get("key1"), Some(&"value1".to_string()));
        assert_eq!(result.details.get("key2"), Some(&"value2".to_string()));
        assert_eq!(result.duration_ms, 100);
    }

    #[test]
    fn test_health_report() {
        let checks = vec![
            HealthCheckResult::healthy("check1".to_string(), "OK".to_string()),
            HealthCheckResult::warning("check2".to_string(), "Warning".to_string()),
            HealthCheckResult::critical("check3".to_string(), "Critical".to_string()),
        ];

        let report = HealthReport::new(checks, 3600);

        assert_eq!(report.overall_status, HealthStatus::Critical);
        assert_eq!(report.checks.len(), 3);
        assert_eq!(report.uptime_seconds, 3600);

        let (warnings, criticals) = report.get_issue_count();
        assert_eq!(warnings, 1);
        assert_eq!(criticals, 1);

        let failed_checks = report.get_failed_checks();
        assert_eq!(failed_checks.len(), 2);
    }

    #[test]
    fn test_health_report_empty() {
        let report = HealthReport::new(vec![], 0);
        assert_eq!(report.overall_status, HealthStatus::Unknown);
        assert_eq!(report.checks.len(), 0);

        let (warnings, criticals) = report.get_issue_count();
        assert_eq!(warnings, 0);
        assert_eq!(criticals, 0);
    }

    #[tokio::test]
    async fn test_metrics_health_checker_healthy() {
        let metrics = Arc::new(PerformanceMetrics::new());
        let thresholds = PerformanceThresholds::default();
        
        // 模拟正常指标
        metrics.record_message(true, Duration::from_millis(10));
        metrics.update_connections(100);
        metrics.update_memory_usage(1024 * 1024 * 100); // 100MB
        metrics.update_cpu_usage(50.0);

        let checker = MetricsHealthChecker::new(metrics, thresholds);
        let result = checker.check().await;

        assert_eq!(result.status, HealthStatus::Healthy);
        assert!(result.message.contains("正常"));
    }

    #[tokio::test]
    async fn test_metrics_health_checker_critical() {
        let metrics = Arc::new(PerformanceMetrics::new());
        let thresholds = PerformanceThresholds::default();
        
        // 模拟严重性能问题
        metrics.record_message(false, Duration::from_millis(500)); // 高响应时间
        metrics.record_message(false, Duration::from_millis(500)); // 高错误率
        metrics.update_connections(1500); // 连接数过多
        metrics.update_memory_usage(1024 * 1024 * 600); // 600MB，超过阈值
        metrics.update_cpu_usage(95.0); // CPU 使用率过高

        let checker = MetricsHealthChecker::new(metrics, thresholds);
        let result = checker.check().await;

        assert_eq!(result.status, HealthStatus::Critical);
        assert!(result.message.contains("严重"));
    }

    #[tokio::test]
    async fn test_memory_health_checker() {
        let checker = MemoryHealthChecker::new(512.0);
        let result = checker.check().await;

        // 由于使用模拟数据，结果应该是健康状态
        assert_eq!(result.status, HealthStatus::Healthy);
        assert!(result.details.contains_key("memory_mb"));
        assert!(result.details.contains_key("usage_percent"));
    }

    #[tokio::test]
    async fn test_connection_health_checker() {
        let metrics = Arc::new(PerformanceMetrics::new());
        metrics.update_connections(500);

        let checker = ConnectionHealthChecker::new(1000, metrics);
        let result = checker.check().await;

        assert_eq!(result.status, HealthStatus::Healthy);
        assert!(result.details.contains_key("active_connections"));
        assert!(result.details.contains_key("usage_percent"));
    }

    #[tokio::test]
    async fn test_health_monitor() {
        let mut monitor = HealthMonitor::new();
        
        let metrics = Arc::new(PerformanceMetrics::new());
        let thresholds = PerformanceThresholds::default();
        
        monitor.add_checker(Box::new(MetricsHealthChecker::new(metrics.clone(), thresholds)));
        monitor.add_checker(Box::new(MemoryHealthChecker::new(512.0)));
        monitor.add_checker(Box::new(ConnectionHealthChecker::new(1000, metrics)));

        let report = monitor.check_all().await;

        assert_eq!(report.checks.len(), 3);
        assert!(report.total_duration_ms >= 0); // 允许为0，因为检查可能很快
        assert_eq!(report.uptime_seconds, 3600);
    }

    #[tokio::test]
    async fn test_health_monitor_single_check() {
        let mut monitor = HealthMonitor::new();
        
        let metrics = Arc::new(PerformanceMetrics::new());
        let thresholds = PerformanceThresholds::default();
        
        monitor.add_checker(Box::new(MetricsHealthChecker::new(metrics, thresholds)));

        let result = monitor.check_single("performance_metrics").await;
        assert!(result.is_some());
        assert_eq!(result.unwrap().name, "performance_metrics");

        let result = monitor.check_single("nonexistent").await;
        assert!(result.is_none());
    }

    #[tokio::test]
    async fn test_health_monitor_with_timeout() {
        let monitor = HealthMonitor::new().with_timeout(Duration::from_millis(1));
        
        assert_eq!(monitor.check_timeout, Duration::from_millis(1));
    }

    #[test]
    fn test_calculate_overall_status() {
        // 测试空检查列表
        assert_eq!(
            HealthReport::calculate_overall_status(&[]),
            HealthStatus::Unknown
        );

        // 测试全部健康
        let healthy_checks = vec![
            HealthCheckResult::healthy("test1".to_string(), "OK".to_string()),
            HealthCheckResult::healthy("test2".to_string(), "OK".to_string()),
        ];
        assert_eq!(
            HealthReport::calculate_overall_status(&healthy_checks),
            HealthStatus::Healthy
        );

        // 测试包含警告
        let warning_checks = vec![
            HealthCheckResult::healthy("test1".to_string(), "OK".to_string()),
            HealthCheckResult::warning("test2".to_string(), "Warning".to_string()),
        ];
        assert_eq!(
            HealthReport::calculate_overall_status(&warning_checks),
            HealthStatus::Warning
        );

        // 测试包含严重问题
        let critical_checks = vec![
            HealthCheckResult::healthy("test1".to_string(), "OK".to_string()),
            HealthCheckResult::warning("test2".to_string(), "Warning".to_string()),
            HealthCheckResult::critical("test3".to_string(), "Critical".to_string()),
        ];
        assert_eq!(
            HealthReport::calculate_overall_status(&critical_checks),
            HealthStatus::Critical
        );
    }
}
