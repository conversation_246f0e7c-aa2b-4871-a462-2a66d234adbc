//! Native Messaging 性能指标模块
//!
//! 提供实时性能监控、指标收集和分析功能

use std::sync::atomic::{AtomicU64, AtomicUsize, Ordering};
use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use std::collections::VecDeque;
use serde::{Deserialize, Serialize};

/// 性能指标收集器
#[derive(Debug)]
pub struct PerformanceMetrics {
    /// 总消息数
    pub total_messages: AtomicU64,
    /// 成功消息数  
    pub successful_messages: AtomicU64,
    /// 失败消息数
    pub failed_messages: AtomicU64,
    /// 平均响应时间（微秒）
    pub average_response_time: AtomicU64,
    /// 活跃连接数
    pub active_connections: AtomicUsize,
    /// 最大连接数记录
    pub max_connections: AtomicUsize,
    /// 内存使用量（字节）
    pub memory_usage: AtomicUsize,
    /// CPU 使用率（百分比 * 100）
    pub cpu_usage: AtomicU64,
    /// 历史响应时间（用于计算移动平均）
    response_times: Arc<RwLock<VecDeque<u64>>>,
    /// 创建时间戳
    pub created_at: u64,
}

/// 性能指标快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsSnapshot {
    /// 总消息数
    pub total_messages: u64,
    /// 成功消息数
    pub successful_messages: u64,
    /// 失败消息数
    pub failed_messages: u64,
    /// 成功率（0.0-1.0）
    pub success_rate: f64,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 最小响应时间（毫秒）
    pub min_response_time_ms: f64,
    /// 最大响应时间（毫秒）
    pub max_response_time_ms: f64,
    /// 95分位数响应时间（毫秒）
    pub p95_response_time_ms: f64,
    /// 99分位数响应时间（毫秒）
    pub p99_response_time_ms: f64,
    /// 活跃连接数
    pub active_connections: usize,
    /// 最大连接数记录
    pub max_connections: usize,
    /// 内存使用量（MB）
    pub memory_usage_mb: f64,
    /// CPU 使用率（百分比）
    pub cpu_usage_percent: f64,
    /// 消息处理速率（消息/秒）
    pub messages_per_second: f64,
    /// 运行时间（秒）
    pub uptime_seconds: u64,
    /// 快照时间戳
    pub timestamp: u64,
}

/// 实时性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RealTimeMetrics {
    /// 最近 1 分钟消息数
    pub messages_last_minute: u64,
    /// 最近 5 分钟消息数  
    pub messages_last_5_minutes: u64,
    /// 最近 1 分钟平均响应时间
    pub avg_response_last_minute: f64,
    /// 当前连接数变化趋势
    pub connection_trend: ConnectionTrend,
    /// 错误率趋势
    pub error_rate_trend: ErrorTrend,
}

/// 连接数趋势
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ConnectionTrend {
    /// 上升
    Increasing,
    /// 下降
    Decreasing,
    /// 稳定
    Stable,
}

/// 错误率趋势
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ErrorTrend {
    /// 上升
    Increasing,
    /// 下降
    Decreasing,
    /// 稳定
    Stable,
}

impl PerformanceMetrics {
    /// 创建新的性能指标实例
    ///
    /// # 返回
    /// PerformanceMetrics - 新的指标实例
    pub fn new() -> Self {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        Self {
            total_messages: AtomicU64::new(0),
            successful_messages: AtomicU64::new(0),
            failed_messages: AtomicU64::new(0),
            average_response_time: AtomicU64::new(0),
            active_connections: AtomicUsize::new(0),
            max_connections: AtomicUsize::new(0),
            memory_usage: AtomicUsize::new(0),
            cpu_usage: AtomicU64::new(0),
            response_times: Arc::new(RwLock::new(VecDeque::with_capacity(1000))),
            created_at: now,
        }
    }

    /// 记录消息处理结果
    ///
    /// # 参数
    /// - `success`: 是否成功处理
    /// - `response_time`: 响应时间
    pub fn record_message(&self, success: bool, response_time: Duration) {
        self.total_messages.fetch_add(1, Ordering::Relaxed);
        
        if success {
            self.successful_messages.fetch_add(1, Ordering::Relaxed);
        } else {
            self.failed_messages.fetch_add(1, Ordering::Relaxed);
        }

        let response_micros = response_time.as_micros() as u64;
        
        // 更新响应时间历史记录
        if let Ok(mut times) = self.response_times.write() {
            times.push_back(response_micros);
            // 保持最近 1000 个响应时间记录
            if times.len() > 1000 {
                times.pop_front();
            }
        }

        // 更新移动平均响应时间
        self.update_average_response_time();
    }

    /// 更新连接数
    ///
    /// # 参数
    /// - `count`: 当前活跃连接数
    pub fn update_connections(&self, count: usize) {
        self.active_connections.store(count, Ordering::Relaxed);
        
        // 更新最大连接数记录
        let current_max = self.max_connections.load(Ordering::Relaxed);
        if count > current_max {
            self.max_connections.store(count, Ordering::Relaxed);
        }
    }

    /// 更新内存使用量
    ///
    /// # 参数
    /// - `bytes`: 内存使用量（字节）
    pub fn update_memory_usage(&self, bytes: usize) {
        self.memory_usage.store(bytes, Ordering::Relaxed);
    }

    /// 更新 CPU 使用率
    ///
    /// # 参数
    /// - `percent`: CPU 使用率百分比（0.0-100.0）
    pub fn update_cpu_usage(&self, percent: f64) {
        let percent_int = (percent * 100.0) as u64;
        self.cpu_usage.store(percent_int, Ordering::Relaxed);
    }

    /// 获取成功率
    ///
    /// # 返回
    /// f64 - 成功率（0.0-1.0）
    pub fn get_success_rate(&self) -> f64 {
        let total = self.total_messages.load(Ordering::Relaxed);
        if total == 0 {
            return 1.0;
        }
        let successful = self.successful_messages.load(Ordering::Relaxed);
        successful as f64 / total as f64
    }

    /// 获取错误率
    ///
    /// # 返回  
    /// f64 - 错误率（0.0-1.0）
    pub fn get_error_rate(&self) -> f64 {
        1.0 - self.get_success_rate()
    }

    /// 获取消息处理速率
    ///
    /// # 返回
    /// f64 - 消息/秒
    pub fn get_messages_per_second(&self) -> f64 {
        let uptime = self.get_uptime_seconds();
        if uptime == 0 {
            return 0.0;
        }
        let total = self.total_messages.load(Ordering::Relaxed);
        total as f64 / uptime as f64
    }

    /// 获取运行时间
    ///
    /// # 返回
    /// u64 - 运行时间（秒）
    pub fn get_uptime_seconds(&self) -> u64 {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        now.saturating_sub(self.created_at)
    }

    /// 获取响应时间统计
    ///
    /// # 返回
    /// (min, max, avg, p95, p99) - 响应时间统计（毫秒）
    pub fn get_response_time_stats(&self) -> (f64, f64, f64, f64, f64) {
        if let Ok(times) = self.response_times.read() {
            if times.is_empty() {
                return (0.0, 0.0, 0.0, 0.0, 0.0);
            }

            let mut sorted_times: Vec<u64> = times.iter().cloned().collect();
            sorted_times.sort_unstable();

            let min = *sorted_times.first().unwrap() as f64 / 1000.0;
            let max = *sorted_times.last().unwrap() as f64 / 1000.0;
            let avg = sorted_times.iter().sum::<u64>() as f64 / sorted_times.len() as f64 / 1000.0;
            
            let p95_index = (sorted_times.len() as f64 * 0.95) as usize;
            let p99_index = (sorted_times.len() as f64 * 0.99) as usize;
            
            let p95 = sorted_times.get(p95_index.saturating_sub(1))
                .unwrap_or(&0) as &u64;
            let p99 = sorted_times.get(p99_index.saturating_sub(1))
                .unwrap_or(&0) as &u64;

            (min, max, avg, *p95 as f64 / 1000.0, *p99 as f64 / 1000.0)
        } else {
            (0.0, 0.0, 0.0, 0.0, 0.0)
        }
    }

    /// 获取指标快照
    ///
    /// # 返回
    /// MetricsSnapshot - 当前指标快照
    pub fn get_snapshot(&self) -> MetricsSnapshot {
        let (min_rt, max_rt, avg_rt, p95_rt, p99_rt) = self.get_response_time_stats();
        
        MetricsSnapshot {
            total_messages: self.total_messages.load(Ordering::Relaxed),
            successful_messages: self.successful_messages.load(Ordering::Relaxed),
            failed_messages: self.failed_messages.load(Ordering::Relaxed),
            success_rate: self.get_success_rate(),
            avg_response_time_ms: avg_rt,
            min_response_time_ms: min_rt,
            max_response_time_ms: max_rt,
            p95_response_time_ms: p95_rt,
            p99_response_time_ms: p99_rt,
            active_connections: self.active_connections.load(Ordering::Relaxed),
            max_connections: self.max_connections.load(Ordering::Relaxed),
            memory_usage_mb: self.memory_usage.load(Ordering::Relaxed) as f64 / 1024.0 / 1024.0,
            cpu_usage_percent: self.cpu_usage.load(Ordering::Relaxed) as f64 / 100.0,
            messages_per_second: self.get_messages_per_second(),
            uptime_seconds: self.get_uptime_seconds(),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }
    }

    /// 重置所有指标
    pub fn reset(&self) {
        self.total_messages.store(0, Ordering::Relaxed);
        self.successful_messages.store(0, Ordering::Relaxed);
        self.failed_messages.store(0, Ordering::Relaxed);
        self.average_response_time.store(0, Ordering::Relaxed);
        self.active_connections.store(0, Ordering::Relaxed);
        self.max_connections.store(0, Ordering::Relaxed);
        self.memory_usage.store(0, Ordering::Relaxed);
        self.cpu_usage.store(0, Ordering::Relaxed);
        
        if let Ok(mut times) = self.response_times.write() {
            times.clear();
        }
    }

    /// 更新移动平均响应时间
    fn update_average_response_time(&self) {
        if let Ok(times) = self.response_times.read() {
            if !times.is_empty() {
                let avg = times.iter().sum::<u64>() / times.len() as u64;
                self.average_response_time.store(avg, Ordering::Relaxed);
            }
        }
    }
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self::new()
    }
}

/// 性能阈值配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceThresholds {
    /// 最大平均响应时间（毫秒）
    pub max_avg_response_time_ms: f64,
    /// 最大错误率
    pub max_error_rate: f64,
    /// 最大 CPU 使用率（百分比）
    pub max_cpu_usage: f64,
    /// 最大内存使用量（MB）
    pub max_memory_usage_mb: f64,
    /// 最大连接数
    pub max_connections: usize,
}

impl Default for PerformanceThresholds {
    fn default() -> Self {
        Self {
            max_avg_response_time_ms: 100.0,  // 100ms
            max_error_rate: 0.05,             // 5%
            max_cpu_usage: 80.0,              // 80%
            max_memory_usage_mb: 512.0,       // 512MB
            max_connections: 1000,            // 1000连接
        }
    }
}

/// 检查性能是否超过阈值
///
/// # 参数
/// - `metrics`: 性能指标
/// - `thresholds`: 阈值配置
///
/// # 返回
/// Vec<String> - 超过阈值的指标列表
pub fn check_performance_thresholds(
    metrics: &MetricsSnapshot,
    thresholds: &PerformanceThresholds,
) -> Vec<String> {
    let mut violations = Vec::new();

    if metrics.avg_response_time_ms > thresholds.max_avg_response_time_ms {
        violations.push(format!(
            "平均响应时间超过阈值: {:.2}ms > {:.2}ms",
            metrics.avg_response_time_ms,
            thresholds.max_avg_response_time_ms
        ));
    }

    if metrics.success_rate < (1.0 - thresholds.max_error_rate) {
        violations.push(format!(
            "错误率超过阈值: {:.2}% > {:.2}%",
            (1.0 - metrics.success_rate) * 100.0,
            thresholds.max_error_rate * 100.0
        ));
    }

    if metrics.cpu_usage_percent > thresholds.max_cpu_usage {
        violations.push(format!(
            "CPU使用率超过阈值: {:.2}% > {:.2}%",
            metrics.cpu_usage_percent,
            thresholds.max_cpu_usage
        ));
    }

    if metrics.memory_usage_mb > thresholds.max_memory_usage_mb {
        violations.push(format!(
            "内存使用量超过阈值: {:.2}MB > {:.2}MB",
            metrics.memory_usage_mb,
            thresholds.max_memory_usage_mb
        ));
    }

    if metrics.active_connections > thresholds.max_connections {
        violations.push(format!(
            "连接数超过阈值: {} > {}",
            metrics.active_connections,
            thresholds.max_connections
        ));
    }

    violations
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;
    use std::time::Duration as StdDuration;

    #[test]
    fn test_new_metrics() {
        let metrics = PerformanceMetrics::new();
        
        assert_eq!(metrics.total_messages.load(Ordering::Relaxed), 0);
        assert_eq!(metrics.successful_messages.load(Ordering::Relaxed), 0);
        assert_eq!(metrics.failed_messages.load(Ordering::Relaxed), 0);
        assert_eq!(metrics.active_connections.load(Ordering::Relaxed), 0);
        assert_eq!(metrics.get_success_rate(), 1.0);
    }

    #[test]
    fn test_record_message_success() {
        let metrics = PerformanceMetrics::new();
        
        metrics.record_message(true, Duration::from_millis(10));
        metrics.record_message(true, Duration::from_millis(20));
        metrics.record_message(false, Duration::from_millis(30));
        
        assert_eq!(metrics.total_messages.load(Ordering::Relaxed), 3);
        assert_eq!(metrics.successful_messages.load(Ordering::Relaxed), 2);
        assert_eq!(metrics.failed_messages.load(Ordering::Relaxed), 1);
        assert!((metrics.get_success_rate() - 2.0/3.0).abs() < 0.001);
        assert!((metrics.get_error_rate() - 1.0/3.0).abs() < 0.001);
    }

    #[test]
    fn test_update_connections() {
        let metrics = PerformanceMetrics::new();
        
        metrics.update_connections(10);
        assert_eq!(metrics.active_connections.load(Ordering::Relaxed), 10);
        assert_eq!(metrics.max_connections.load(Ordering::Relaxed), 10);
        
        metrics.update_connections(5);
        assert_eq!(metrics.active_connections.load(Ordering::Relaxed), 5);
        assert_eq!(metrics.max_connections.load(Ordering::Relaxed), 10);
        
        metrics.update_connections(15);
        assert_eq!(metrics.active_connections.load(Ordering::Relaxed), 15);
        assert_eq!(metrics.max_connections.load(Ordering::Relaxed), 15);
    }

    #[test]
    fn test_update_memory_and_cpu() {
        let metrics = PerformanceMetrics::new();
        
        metrics.update_memory_usage(1024 * 1024 * 100); // 100MB
        metrics.update_cpu_usage(75.5);
        
        assert_eq!(metrics.memory_usage.load(Ordering::Relaxed), 1024 * 1024 * 100);
        assert_eq!(metrics.cpu_usage.load(Ordering::Relaxed), 7550);
    }

    #[test]
    fn test_response_time_stats() {
        let metrics = PerformanceMetrics::new();
        
        // 记录一些响应时间
        metrics.record_message(true, Duration::from_millis(10));
        metrics.record_message(true, Duration::from_millis(20));
        metrics.record_message(true, Duration::from_millis(30));
        metrics.record_message(true, Duration::from_millis(40));
        metrics.record_message(true, Duration::from_millis(50));
        
        let (min, max, avg, _p95, _p99) = metrics.get_response_time_stats();
        
        assert_eq!(min, 10.0);
        assert_eq!(max, 50.0);
        assert_eq!(avg, 30.0);
    }

    #[test]
    fn test_messages_per_second() {
        let metrics = PerformanceMetrics::new();
        
        // 模拟处理消息
        for _ in 0..10 {
            metrics.record_message(true, Duration::from_millis(1));
        }
        
        // 等待一小段时间以获得有效的运行时间
        thread::sleep(StdDuration::from_millis(100));
        
        let mps = metrics.get_messages_per_second();
        // 允许为0，因为在测试环境中时间计算可能不准确
        assert!(mps >= 0.0);
    }

    #[test]
    fn test_get_snapshot() {
        let metrics = PerformanceMetrics::new();
        
        metrics.record_message(true, Duration::from_millis(10));
        metrics.record_message(false, Duration::from_millis(20));
        metrics.update_connections(5);
        metrics.update_memory_usage(1024 * 1024 * 50); // 50MB
        metrics.update_cpu_usage(25.0);
        
        let snapshot = metrics.get_snapshot();
        
        assert_eq!(snapshot.total_messages, 2);
        assert_eq!(snapshot.successful_messages, 1);
        assert_eq!(snapshot.failed_messages, 1);
        assert_eq!(snapshot.success_rate, 0.5);
        assert_eq!(snapshot.active_connections, 5);
        assert_eq!(snapshot.memory_usage_mb, 50.0);
        assert_eq!(snapshot.cpu_usage_percent, 25.0);
    }

    #[test]
    fn test_reset_metrics() {
        let metrics = PerformanceMetrics::new();
        
        metrics.record_message(true, Duration::from_millis(10));
        metrics.update_connections(10);
        metrics.update_memory_usage(1024 * 1024);
        metrics.update_cpu_usage(50.0);
        
        metrics.reset();
        
        assert_eq!(metrics.total_messages.load(Ordering::Relaxed), 0);
        assert_eq!(metrics.successful_messages.load(Ordering::Relaxed), 0);
        assert_eq!(metrics.failed_messages.load(Ordering::Relaxed), 0);
        assert_eq!(metrics.active_connections.load(Ordering::Relaxed), 0);
        assert_eq!(metrics.max_connections.load(Ordering::Relaxed), 0);
        assert_eq!(metrics.memory_usage.load(Ordering::Relaxed), 0);
        assert_eq!(metrics.cpu_usage.load(Ordering::Relaxed), 0);
    }

    #[test]
    fn test_performance_thresholds() {
        let thresholds = PerformanceThresholds::default();
        
        assert_eq!(thresholds.max_avg_response_time_ms, 100.0);
        assert_eq!(thresholds.max_error_rate, 0.05);
        assert_eq!(thresholds.max_cpu_usage, 80.0);
        assert_eq!(thresholds.max_memory_usage_mb, 512.0);
        assert_eq!(thresholds.max_connections, 1000);
    }

    #[test]
    fn test_check_performance_thresholds_normal() {
        let metrics = PerformanceMetrics::new();
        metrics.record_message(true, Duration::from_millis(10));
        metrics.update_connections(100);
        metrics.update_memory_usage(1024 * 1024 * 100); // 100MB
        metrics.update_cpu_usage(50.0);
        
        let snapshot = metrics.get_snapshot();
        let thresholds = PerformanceThresholds::default();
        let violations = check_performance_thresholds(&snapshot, &thresholds);
        
        assert!(violations.is_empty());
    }

    #[test]
    fn test_check_performance_thresholds_violations() {
        let metrics = PerformanceMetrics::new();
        
        // 创建违反阈值的指标
        metrics.record_message(false, Duration::from_millis(200)); // 高响应时间
        metrics.record_message(false, Duration::from_millis(200)); // 高错误率
        metrics.update_connections(1500); // 连接数过多
        metrics.update_memory_usage(1024 * 1024 * 600); // 600MB，超过阈值
        metrics.update_cpu_usage(90.0); // CPU 使用率过高
        
        let snapshot = metrics.get_snapshot();
        let thresholds = PerformanceThresholds::default();
        let violations = check_performance_thresholds(&snapshot, &thresholds);
        
        assert!(!violations.is_empty());
        assert!(violations.len() >= 4); // 至少有4个违规项
        
        // 检查是否包含预期的违规信息
        let violations_text = violations.join(" ");
        assert!(violations_text.contains("响应时间"));
        assert!(violations_text.contains("错误率"));
        assert!(violations_text.contains("CPU"));
        assert!(violations_text.contains("内存"));
        assert!(violations_text.contains("连接数"));
    }

    #[test]
    fn test_response_time_percentiles() {
        let metrics = PerformanceMetrics::new();
        
        // 添加100个响应时间数据点
        for i in 1..=100 {
            metrics.record_message(true, Duration::from_millis(i));
        }
        
        let (_min, _max, _avg, p95, p99) = metrics.get_response_time_stats();
        
        // P95 应该大约是 95ms，P99 应该大约是 99ms
        assert!(p95 >= 90.0 && p95 <= 100.0);
        assert!(p99 >= 95.0 && p99 <= 100.0);
    }

    #[test]
    fn test_concurrent_access() {
        let metrics = Arc::new(PerformanceMetrics::new());
        let mut handles = vec![];
        
        // 启动多个线程并发记录指标
        for i in 0..10 {
            let metrics_clone = metrics.clone();
            let handle = thread::spawn(move || {
                for j in 0..100 {
                    metrics_clone.record_message(
                        j % 10 != 0, // 10% 失败率
                        Duration::from_millis((i * 10 + j) % 50)
                    );
                    metrics_clone.update_connections((i + j) as usize);
                }
            });
            handles.push(handle);
        }
        
        // 等待所有线程完成
        for handle in handles {
            handle.join().unwrap();
        }
        
        // 验证数据一致性
        assert_eq!(metrics.total_messages.load(Ordering::Relaxed), 1000);
        assert_eq!(metrics.successful_messages.load(Ordering::Relaxed), 900);
        assert_eq!(metrics.failed_messages.load(Ordering::Relaxed), 100);
        assert!((metrics.get_success_rate() - 0.9).abs() < 0.001);
    }

    #[test]
    fn test_connection_trend() {
        // 测试连接趋势枚举
        assert_eq!(ConnectionTrend::Increasing, ConnectionTrend::Increasing);
        assert_ne!(ConnectionTrend::Increasing, ConnectionTrend::Decreasing);
    }

    #[test]
    fn test_error_trend() {
        // 测试错误率趋势枚举
        assert_eq!(ErrorTrend::Stable, ErrorTrend::Stable);
        assert_ne!(ErrorTrend::Stable, ErrorTrend::Increasing);
    }
} 