//! Native Messaging 监控模块
//!
//! 提供性能监控、健康检查和告警功能

pub mod metrics;
pub mod health;
pub mod alerts;

// 重新导出主要类型和结构体
pub use metrics::{
    PerformanceMetrics, 
    MetricsSnapshot, 
    PerformanceThresholds,
    check_performance_thresholds,
    RealTimeMetrics,
    ConnectionTrend,
    ErrorTrend,
};

pub use health::{
    HealthStatus,
    HealthCheckResult,
    HealthReport,
    HealthChecker,
    HealthMonitor,
    MetricsHealthChecker,
    MemoryHealthChecker,
    ConnectionHealthChecker,
};

pub use alerts::{
    AlertLevel,
    AlertRule,
    AlertRuleType,
    AlertEvent,
    AlertManager,
    AlertStatistics,
    NotificationChannel,
    LogNotificationChannel,
    ConsoleNotificationChannel,
    ComparisonOperator,
};

use std::sync::Arc;
use std::time::Duration;
use tokio::sync::mpsc;

/// 完整的监控系统
pub struct MonitoringSystem {
    /// 性能指标收集器
    pub metrics: Arc<PerformanceMetrics>,
    /// 健康监控管理器
    pub health_monitor: HealthMonitor,
    /// 告警管理器
    pub alert_manager: AlertManager,
    /// 告警事件接收器
    pub alert_receiver: Option<mpsc::UnboundedReceiver<AlertEvent>>,
}

impl MonitoringSystem {
    /// 创建新的监控系统
    ///
    /// # 返回
    /// MonitoringSystem - 完整的监控系统实例
    pub fn new() -> Self {
        let metrics = Arc::new(PerformanceMetrics::new());
        let health_monitor = HealthMonitor::new();
        let (alert_manager, alert_receiver) = AlertManager::new();

        Self {
            metrics,
            health_monitor,
            alert_manager,
            alert_receiver: Some(alert_receiver),
        }
    }

    /// 使用自定义配置创建监控系统
    ///
    /// # 参数
    /// - `check_timeout`: 健康检查超时时间
    ///
    /// # 返回
    /// MonitoringSystem - 监控系统实例
    pub fn with_config(check_timeout: Duration) -> Self {
        let metrics = Arc::new(PerformanceMetrics::new());
        let health_monitor = HealthMonitor::new().with_timeout(check_timeout);
        let (alert_manager, alert_receiver) = AlertManager::new();

        Self {
            metrics,
            health_monitor,
            alert_manager,
            alert_receiver: Some(alert_receiver),
        }
    }

    /// 初始化默认的健康检查器和告警规则
    ///
    /// # 参数
    /// - `thresholds`: 性能阈值配置
    ///
    /// # 返回
    /// Result<()> - 初始化结果
    pub async fn initialize_defaults(&mut self, thresholds: PerformanceThresholds) -> Result<(), String> {
        // 添加默认健康检查器
        self.health_monitor.add_checker(Box::new(MetricsHealthChecker::new(
            self.metrics.clone(),
            thresholds.clone(),
        )));

        self.health_monitor.add_checker(Box::new(MemoryHealthChecker::new(
            thresholds.max_memory_usage_mb,
        )));

        self.health_monitor.add_checker(Box::new(ConnectionHealthChecker::new(
            thresholds.max_connections,
            self.metrics.clone(),
        )));

        // 添加默认通知渠道
        self.alert_manager.add_notification_channel(Box::new(
            LogNotificationChannel::new("log".to_string(), "日志通知".to_string())
        )).await?;

        self.alert_manager.add_notification_channel(Box::new(
            ConsoleNotificationChannel::new("console".to_string(), "控制台通知".to_string())
        )).await?;

        // 添加默认告警规则
        self.add_default_alert_rules(&thresholds).await?;

        Ok(())
    }

    /// 添加默认告警规则
    async fn add_default_alert_rules(&self, thresholds: &PerformanceThresholds) -> Result<(), String> {
        // CPU 使用率告警
        let cpu_rule = AlertRule::new(
            "cpu_usage_high".to_string(),
            "CPU 使用率过高".to_string(),
            format!("CPU 使用率超过 {}%", thresholds.max_cpu_usage),
            AlertRuleType::MetricThreshold {
                metric_name: "cpu_usage_percent".to_string(),
                threshold: thresholds.max_cpu_usage,
                operator: ComparisonOperator::GreaterThan,
                duration_seconds: 300,
            },
            AlertLevel::Warning,
        )
        .add_notification_channel("log".to_string())
        .add_notification_channel("console".to_string());

        self.alert_manager.add_rule(cpu_rule).await?;

        // 内存使用量告警
        let memory_rule = AlertRule::new(
            "memory_usage_high".to_string(),
            "内存使用量过高".to_string(),
            format!("内存使用量超过 {}MB", thresholds.max_memory_usage_mb),
            AlertRuleType::MetricThreshold {
                metric_name: "memory_usage_mb".to_string(),
                threshold: thresholds.max_memory_usage_mb,
                operator: ComparisonOperator::GreaterThan,
                duration_seconds: 300,
            },
            AlertLevel::Critical,
        )
        .add_notification_channel("log".to_string())
        .add_notification_channel("console".to_string());

        self.alert_manager.add_rule(memory_rule).await?;

        // 错误率告警
        let error_rate_rule = AlertRule::new(
            "error_rate_high".to_string(),
            "错误率过高".to_string(),
            format!("错误率超过 {}%", thresholds.max_error_rate * 100.0),
            AlertRuleType::MetricThreshold {
                metric_name: "success_rate".to_string(),
                threshold: 1.0 - thresholds.max_error_rate,
                operator: ComparisonOperator::LessThan,
                duration_seconds: 300,
            },
            AlertLevel::Critical,
        )
        .add_notification_channel("log".to_string())
        .add_notification_channel("console".to_string());

        self.alert_manager.add_rule(error_rate_rule).await?;

        // 响应时间告警
        let response_time_rule = AlertRule::new(
            "response_time_high".to_string(),
            "响应时间过高".to_string(),
            format!("平均响应时间超过 {}ms", thresholds.max_avg_response_time_ms),
            AlertRuleType::MetricThreshold {
                metric_name: "avg_response_time_ms".to_string(),
                threshold: thresholds.max_avg_response_time_ms,
                operator: ComparisonOperator::GreaterThan,
                duration_seconds: 300,
            },
            AlertLevel::Warning,
        )
        .add_notification_channel("log".to_string())
        .add_notification_channel("console".to_string());

        self.alert_manager.add_rule(response_time_rule).await?;

        // 连接数告警
        let connection_rule = AlertRule::new(
            "connection_count_high".to_string(),
            "连接数过多".to_string(),
            format!("活跃连接数超过 {}", thresholds.max_connections),
            AlertRuleType::ConnectionAnomaly {
                max_connections: thresholds.max_connections,
                growth_rate_threshold: 50.0,
            },
            AlertLevel::Warning,
        )
        .add_notification_channel("log".to_string())
        .add_notification_channel("console".to_string());

        self.alert_manager.add_rule(connection_rule).await?;

        Ok(())
    }

    /// 运行完整的监控检查
    ///
    /// # 返回
    /// Result<MonitoringReport> - 监控报告
    pub async fn run_monitoring_check(&self) -> Result<MonitoringReport, String> {
        // 获取性能指标快照
        let metrics_snapshot = self.metrics.get_snapshot();

        // 执行健康检查
        let health_report = self.health_monitor.check_all().await;

        // 评估告警
        self.alert_manager.evaluate_metrics(&metrics_snapshot).await?;
        self.alert_manager.evaluate_health_report(&health_report).await?;

        // 获取活跃告警
        let active_alerts = self.alert_manager.get_active_alerts().await?;

        // 获取告警统计
        let alert_statistics = self.alert_manager.get_alert_statistics().await?;

        Ok(MonitoringReport {
            metrics: metrics_snapshot,
            health: health_report,
            active_alerts,
            alert_statistics,
            check_timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        })
    }

    /// 获取告警事件接收器
    ///
    /// # 返回
    /// Option<mpsc::UnboundedReceiver<AlertEvent>> - 告警事件接收器
    pub fn take_alert_receiver(&mut self) -> Option<mpsc::UnboundedReceiver<AlertEvent>> {
        self.alert_receiver.take()
    }
}

impl Default for MonitoringSystem {
    fn default() -> Self {
        Self::new()
    }
}

/// 完整监控报告
#[derive(Debug, Clone)]
pub struct MonitoringReport {
    /// 性能指标快照
    pub metrics: MetricsSnapshot,
    /// 健康检查报告
    pub health: HealthReport,
    /// 活跃告警列表
    pub active_alerts: Vec<AlertEvent>,
    /// 告警统计
    pub alert_statistics: AlertStatistics,
    /// 检查时间戳
    pub check_timestamp: u64,
}

impl MonitoringReport {
    /// 获取整体健康状态
    pub fn overall_health_status(&self) -> HealthStatus {
        if !self.active_alerts.is_empty() {
            // 如果有告警，根据最高级别确定状态
            let max_alert_level = self.active_alerts
                .iter()
                .map(|alert| alert.level)
                .max()
                .unwrap_or(AlertLevel::Info);

            match max_alert_level {
                AlertLevel::Info => HealthStatus::Healthy,
                AlertLevel::Warning => HealthStatus::Warning,
                AlertLevel::Critical | AlertLevel::Emergency => HealthStatus::Critical,
            }
        } else {
            self.health.overall_status
        }
    }

    /// 判断是否需要关注
    pub fn needs_attention(&self) -> bool {
        self.overall_health_status().needs_attention() || !self.active_alerts.is_empty()
    }

    /// 获取摘要信息
    pub fn summary(&self) -> String {
        let health_status = self.overall_health_status();
        let (warnings, criticals) = self.health.get_issue_count();
        let active_alerts_count = self.active_alerts.len();

        format!(
            "整体状态: {:?}, 健康检查: {} 警告 + {} 严重, 活跃告警: {}",
            health_status,
            warnings,
            criticals,
            active_alerts_count
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_monitoring_system_creation() {
        let system = MonitoringSystem::new();
        assert!(system.alert_receiver.is_some());
    }

    #[test]
    fn test_monitoring_system_with_config() {
        let system = MonitoringSystem::with_config(Duration::from_secs(10));
        assert!(system.alert_receiver.is_some());
    }

    #[tokio::test]
    async fn test_monitoring_system_initialize_defaults() {
        let mut system = MonitoringSystem::new();
        let thresholds = PerformanceThresholds::default();

        let result = system.initialize_defaults(thresholds).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_monitoring_system_run_check() {
        let mut system = MonitoringSystem::new();
        let thresholds = PerformanceThresholds::default();

        system.initialize_defaults(thresholds).await.unwrap();

        // 记录一些指标
        system.metrics.record_message(true, Duration::from_millis(10));
        system.metrics.update_connections(100);
        system.metrics.update_memory_usage(1024 * 1024 * 50); // 50MB
        system.metrics.update_cpu_usage(25.0);

        let report = system.run_monitoring_check().await.unwrap();

        assert_eq!(report.metrics.total_messages, 1);
        assert_eq!(report.metrics.active_connections, 100);
        assert!(report.check_timestamp > 0);
    }

    #[test]
    fn test_monitoring_report_overall_health() {
        let metrics = MetricsSnapshot {
            total_messages: 100,
            successful_messages: 95,
            failed_messages: 5,
            success_rate: 0.95,
            avg_response_time_ms: 25.0,
            min_response_time_ms: 5.0,
            max_response_time_ms: 100.0,
            p95_response_time_ms: 45.0,
            p99_response_time_ms: 80.0,
            active_connections: 50,
            max_connections: 100,
            memory_usage_mb: 64.0,
            cpu_usage_percent: 25.0,
            messages_per_second: 10.0,
            uptime_seconds: 3600,
            timestamp: **********,
        };

        let health = HealthReport::new(vec![
            HealthCheckResult::healthy("test1".to_string(), "OK".to_string()),
        ], 3600);

        let report = MonitoringReport {
            metrics,
            health,
            active_alerts: vec![],
            alert_statistics: AlertStatistics {
                total_active_alerts: 0,
                total_alerts_today: 0,
                alerts_by_level: std::collections::HashMap::new(),
                top_alert_rules: std::collections::HashMap::new(),
            },
            check_timestamp: **********,
        };

        assert_eq!(report.overall_health_status(), HealthStatus::Healthy);
        assert!(!report.needs_attention());
        assert!(report.summary().contains("整体状态"));
    }

    #[test]
    fn test_monitoring_report_with_alerts() {
        let metrics = MetricsSnapshot {
            total_messages: 100,
            successful_messages: 95,
            failed_messages: 5,
            success_rate: 0.95,
            avg_response_time_ms: 25.0,
            min_response_time_ms: 5.0,
            max_response_time_ms: 100.0,
            p95_response_time_ms: 45.0,
            p99_response_time_ms: 80.0,
            active_connections: 50,
            max_connections: 100,
            memory_usage_mb: 64.0,
            cpu_usage_percent: 25.0,
            messages_per_second: 10.0,
            uptime_seconds: 3600,
            timestamp: **********,
        };

        let health = HealthReport::new(vec![
            HealthCheckResult::healthy("test1".to_string(), "OK".to_string()),
        ], 3600);

        let rule = AlertRule::new(
            "test_rule".to_string(),
            "测试告警".to_string(),
            "测试".to_string(),
            AlertRuleType::MetricThreshold {
                metric_name: "cpu_usage_percent".to_string(),
                threshold: 80.0,
                operator: ComparisonOperator::GreaterThan,
                duration_seconds: 300,
            },
            AlertLevel::Critical,
        );

        let alert = AlertEvent::new(&rule, "测试告警".to_string(), "这是一个测试告警".to_string());

        let report = MonitoringReport {
            metrics,
            health,
            active_alerts: vec![alert],
            alert_statistics: AlertStatistics {
                total_active_alerts: 1,
                total_alerts_today: 1,
                alerts_by_level: std::collections::HashMap::new(),
                top_alert_rules: std::collections::HashMap::new(),
            },
            check_timestamp: **********,
        };

        assert_eq!(report.overall_health_status(), HealthStatus::Critical);
        assert!(report.needs_attention());
        assert!(report.summary().contains("活跃告警: 1"));
    }
}
