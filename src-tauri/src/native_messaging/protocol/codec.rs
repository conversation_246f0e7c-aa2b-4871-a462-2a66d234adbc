//! Native Messaging 编解码器模块
//!
//! 提供消息序列化、反序列化和格式转换功能

use super::message::{
    BatchMessage, NativeMessage, OutgoingMessage, ProtocolCompatibility, StreamMessage,
};
use crate::native_messaging::error::{NativeMessagingError, Result};
use serde_json;
use std::collections::HashMap;

use std::time::Instant;

/// 消息编解码器接口
///
/// 定义消息编码和解码的标准接口
pub trait MessageCodec {
    /// 编码消息为字节数组
    ///
    /// # 参数
    /// - `message`: 待编码的消息
    ///
    /// # 返回
    /// Result<Vec<u8>> - 编码后的字节数组
    fn encode(&self, message: &NativeMessage) -> Result<Vec<u8>>;

    /// 解码字节数组为消息
    ///
    /// # 参数
    /// - `data`: 待解码的字节数组
    ///
    /// # 返回
    /// Result<NativeMessage> - 解码后的消息
    fn decode(&self, data: &[u8]) -> Result<NativeMessage>;
}

/// 流式编解码器接口
///
/// 支持大数据流式处理
pub trait StreamCodec {
    /// 开始编码流
    ///
    /// # 返回
    /// Result<StreamEncoder> - 流编码器
    fn start_encode_stream(&self) -> Result<StreamEncoder>;

    /// 开始解码流
    ///
    /// # 返回
    /// Result<StreamDecoder> - 流解码器
    fn start_decode_stream(&self) -> Result<StreamDecoder>;
}

/// 批量编解码器接口
///
/// 支持批量消息处理
pub trait BatchCodec {
    /// 批量编码消息
    ///
    /// # 参数
    /// - `messages`: 待编码的消息列表
    ///
    /// # 返回
    /// Result<Vec<u8>> - 编码后的字节数组
    fn encode_batch(&self, messages: &[NativeMessage]) -> Result<Vec<u8>>;

    /// 批量解码消息
    ///
    /// # 参数
    /// - `data`: 待解码的字节数组
    ///
    /// # 返回
    /// Result<Vec<NativeMessage>> - 解码后的消息列表
    fn decode_batch(&self, data: &[u8]) -> Result<Vec<NativeMessage>>;
}

/// 压缩算法枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CompressionAlgorithm {
    /// 无压缩
    None,
    /// Gzip 压缩
    Gzip,
    /// LZ4 压缩（快速）
    Lz4,
    /// Zstd 压缩（高效）
    Zstd,
}

/// 编解码性能配置
#[derive(Debug, Clone)]
pub struct CodecConfig {
    /// 压缩算法
    pub compression: CompressionAlgorithm,
    /// 压缩级别 (0-9)
    pub compression_level: u8,
    /// 是否启用性能监控
    pub enable_performance_monitoring: bool,
    /// 最大消息大小
    pub max_message_size: usize,
    /// 缓冲区大小
    pub buffer_size: usize,
    /// 是否启用版本兼容性检查
    pub enable_version_compatibility: bool,
    /// 支持的协议版本
    pub supported_versions: Vec<u32>,
}

impl Default for CodecConfig {
    fn default() -> Self {
        Self {
            compression: CompressionAlgorithm::None,
            compression_level: 6,
            enable_performance_monitoring: false,
            max_message_size: 1024 * 1024, // 1MB
            buffer_size: 8192,             // 8KB
            enable_version_compatibility: true,
            supported_versions: vec![1, 2],
        }
    }
}

/// 编解码性能指标
#[derive(Debug, Clone)]
pub struct CodecMetrics {
    /// 编码次数
    pub encode_count: u64,
    /// 解码次数
    pub decode_count: u64,
    /// 总编码时间（微秒）
    pub total_encode_time_micros: u64,
    /// 总解码时间（微秒）
    pub total_decode_time_micros: u64,
    /// 平均编码时间（微秒）
    pub avg_encode_time_micros: f64,
    /// 平均解码时间（微秒）
    pub avg_decode_time_micros: f64,
    /// 总原始数据大小
    pub total_raw_bytes: u64,
    /// 总压缩数据大小
    pub total_compressed_bytes: u64,
    /// 压缩比
    pub compression_ratio: f64,
}

impl Default for CodecMetrics {
    fn default() -> Self {
        Self {
            encode_count: 0,
            decode_count: 0,
            total_encode_time_micros: 0,
            total_decode_time_micros: 0,
            avg_encode_time_micros: 0.0,
            avg_decode_time_micros: 0.0,
            total_raw_bytes: 0,
            total_compressed_bytes: 0,
            compression_ratio: 1.0,
        }
    }
}

/// 流编码器
pub struct StreamEncoder {
    #[allow(dead_code)]
    buffer: Vec<u8>,
    #[allow(dead_code)]
    compression: CompressionAlgorithm,
    sequence: u64,
}

impl StreamEncoder {
    /// 创建新的流编码器
    pub fn new(compression: CompressionAlgorithm) -> Self {
        Self {
            buffer: Vec::new(),
            compression,
            sequence: 0,
        }
    }

    /// 编码流数据块
    ///
    /// # 参数
    /// - `data`: 数据块
    /// - `is_final`: 是否为最后一块
    ///
    /// # 返回
    /// Result<StreamMessage> - 流消息
    pub fn encode_chunk(&mut self, data: &[u8], is_final: bool) -> Result<StreamMessage> {
        let stream_message = StreamMessage {
            stream_id: format!("stream_{}", std::process::id()),
            sequence: self.sequence,
            is_final,
            data: data.to_vec(),
            checksum: Some(self.calculate_checksum(data)),
        };

        self.sequence += 1;
        Ok(stream_message)
    }

    /// 计算校验和
    fn calculate_checksum(&self, data: &[u8]) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        data.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }
}

/// 流解码器
pub struct StreamDecoder {
    received_chunks: HashMap<u64, StreamMessage>,
    #[allow(dead_code)]
    expected_sequence: u64,
    stream_id: Option<String>,
}

impl Default for StreamDecoder {
    fn default() -> Self {
        Self::new()
    }
}

impl StreamDecoder {
    /// 创建新的流解码器
    pub fn new() -> Self {
        Self {
            received_chunks: HashMap::new(),
            expected_sequence: 0,
            stream_id: None,
        }
    }

    /// 处理接收到的流消息
    ///
    /// # 参数
    /// - `message`: 流消息
    ///
    /// # 返回
    /// Result<Option<Vec<u8>>> - 完整数据（如果流结束）
    pub fn process_chunk(&mut self, message: StreamMessage) -> Result<Option<Vec<u8>>> {
        // 验证流ID
        if let Some(ref expected_id) = self.stream_id {
            if *expected_id != message.stream_id {
                return Err(NativeMessagingError::ProtocolError(
                    "流ID不匹配".to_string(),
                ));
            }
        } else {
            self.stream_id = Some(message.stream_id.clone());
        }

        // 验证校验和
        if let Some(ref checksum) = message.checksum {
            let calculated = self.calculate_checksum(&message.data);
            if *checksum != calculated {
                return Err(NativeMessagingError::ProtocolError(
                    "数据校验和不匹配".to_string(),
                ));
            }
        }

        // 存储消息块
        self.received_chunks
            .insert(message.sequence, message.clone());

        // 检查是否为最后一块
        if message.is_final {
            // 重组完整数据
            let mut complete_data = Vec::new();
            for seq in 0..=message.sequence {
                if let Some(chunk) = self.received_chunks.get(&seq) {
                    complete_data.extend_from_slice(&chunk.data);
                } else {
                    return Err(NativeMessagingError::ProtocolError(format!(
                        "缺少序列号 {} 的数据块",
                        seq
                    )));
                }
            }
            return Ok(Some(complete_data));
        }

        Ok(None)
    }

    /// 计算校验和
    fn calculate_checksum(&self, data: &[u8]) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        data.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }
}

/// 协议编解码器
///
/// 实现Native Messaging标准的编解码逻辑，支持高性能和版本兼容性
pub struct ProtocolCodec {
    /// 配置信息
    config: CodecConfig,
    /// 性能指标
    metrics: CodecMetrics,
    /// 协议兼容性信息
    compatibility: ProtocolCompatibility,
}

impl ProtocolCodec {
    /// 创建新的协议编解码器
    ///
    /// # 参数
    /// - `config`: 编解码器配置
    ///
    /// # 返回
    /// ProtocolCodec - 编解码器实例
    pub fn new(config: CodecConfig) -> Self {
        Self {
            config,
            metrics: CodecMetrics::default(),
            compatibility: ProtocolCompatibility::new_default(),
        }
    }

    /// 创建默认编解码器
    ///
    /// # 返回
    /// ProtocolCodec - 使用默认配置的编解码器
    pub fn new_default() -> Self {
        Self::new(CodecConfig::default())
    }

    /// 创建高性能编解码器
    ///
    /// # 返回
    /// ProtocolCodec - 优化性能的编解码器
    pub fn new_high_performance() -> Self {
        let config = CodecConfig {
            compression: CompressionAlgorithm::Lz4,
            compression_level: 1, // 快速压缩
            enable_performance_monitoring: true,
            buffer_size: 32768, // 32KB
            ..Default::default()
        };
        Self::new(config)
    }

    /// 创建高压缩比编解码器
    ///
    /// # 返回
    /// ProtocolCodec - 高压缩比的编解码器
    pub fn new_high_compression() -> Self {
        let config = CodecConfig {
            compression: CompressionAlgorithm::Zstd,
            compression_level: 9, // 最高压缩
            enable_performance_monitoring: false,
            ..Default::default()
        };
        Self::new(config)
    }

    /// 编码传出消息
    ///
    /// # 参数
    /// - `message`: 待编码的传出消息
    ///
    /// # 返回
    /// Result<Vec<u8>> - 编码后的字节数组
    pub fn encode_outgoing(&mut self, message: &OutgoingMessage) -> Result<Vec<u8>> {
        let start_time = if self.config.enable_performance_monitoring {
            Some(Instant::now())
        } else {
            None
        };

        let json_data =
            serde_json::to_vec(message).map_err(NativeMessagingError::SerializationError)?;

        let compressed_data = self.compress_data(&json_data)?;

        // 更新性能指标
        if let Some(start) = start_time {
            self.update_encode_metrics(
                start.elapsed().as_micros() as u64,
                json_data.len(),
                compressed_data.len(),
            );
        }

        Ok(compressed_data)
    }

    /// 解码为传出消息
    ///
    /// # 参数
    /// - `data`: 待解码的字节数组
    ///
    /// # 返回
    /// Result<OutgoingMessage> - 解码后的传出消息
    pub fn decode_outgoing(&mut self, data: &[u8]) -> Result<OutgoingMessage> {
        let start_time = if self.config.enable_performance_monitoring {
            Some(Instant::now())
        } else {
            None
        };

        let decompressed_data = self.decompress_data(data)?;

        let message = serde_json::from_slice(&decompressed_data)
            .map_err(NativeMessagingError::SerializationError)?;

        // 更新性能指标
        if let Some(start) = start_time {
            self.update_decode_metrics(start.elapsed().as_micros() as u64);
        }

        Ok(message)
    }

    /// 编码为Native Messaging格式
    ///
    /// 按照Native Messaging标准，消息前需要4字节长度前缀
    ///
    /// # 参数
    /// - `message`: 待编码的消息
    ///
    /// # 返回
    /// Result<Vec<u8>> - Native Messaging格式的字节数组
    pub fn encode_native_format(&mut self, message: &NativeMessage) -> Result<Vec<u8>> {
        // 版本兼容性检查
        if self.config.enable_version_compatibility {
            self.check_version_compatibility(message)?;
        }

        let json_data = self.encode(message)?;
        let message_length = json_data.len() as u32;

        // 检查消息大小限制
        if json_data.len() > self.config.max_message_size {
            return Err(NativeMessagingError::ProtocolError(format!(
                "消息大小 {} 超过限制 {}",
                json_data.len(),
                self.config.max_message_size
            )));
        }

        // Native Messaging格式：4字节长度 + JSON数据
        let mut result = Vec::with_capacity(4 + json_data.len());
        result.extend_from_slice(&message_length.to_le_bytes());
        result.extend_from_slice(&json_data);

        Ok(result)
    }

    /// 解码Native Messaging格式
    ///
    /// # 参数
    /// - `data`: Native Messaging格式的字节数组
    ///
    /// # 返回
    /// Result<NativeMessage> - 解码后的消息
    pub fn decode_native_format(&mut self, data: &[u8]) -> Result<NativeMessage> {
        if data.len() < 4 {
            return Err(NativeMessagingError::ProtocolError(
                "数据长度不足，无法读取消息长度前缀".to_string(),
            ));
        }

        // 读取消息长度
        let length_bytes = &data[0..4];
        let message_length = u32::from_le_bytes([
            length_bytes[0],
            length_bytes[1],
            length_bytes[2],
            length_bytes[3],
        ]) as usize;

        // 验证消息长度
        if data.len() < 4 + message_length {
            return Err(NativeMessagingError::ProtocolError(
                "数据长度与消息长度前缀不匹配".to_string(),
            ));
        }

        if message_length > self.config.max_message_size {
            return Err(NativeMessagingError::ProtocolError(format!(
                "消息长度 {} 超过限制 {}",
                message_length, self.config.max_message_size
            )));
        }

        // 解码消息内容
        let message_data = &data[4..4 + message_length];
        let message = self.decode(message_data)?;

        // 版本兼容性检查
        if self.config.enable_version_compatibility {
            self.check_version_compatibility(&message)?;
        }

        Ok(message)
    }

    /// 版本兼容性检查
    ///
    /// # 参数
    /// - `message`: 待检查的消息
    ///
    /// # 返回
    /// Result<()> - 检查结果
    fn check_version_compatibility(&self, message: &NativeMessage) -> Result<()> {
        if !self.config.supported_versions.contains(&message.version) {
            return Err(NativeMessagingError::ProtocolError(format!(
                "不支持的协议版本: {}",
                message.version
            )));
        }

        // 检查版本特性兼容性
        if !self.compatibility.is_version_supported(message.version) {
            return Err(NativeMessagingError::ProtocolError(format!(
                "协议版本 {} 不被当前实现支持",
                message.version
            )));
        }

        Ok(())
    }

    /// 压缩数据
    ///
    /// # 参数
    /// - `data`: 待压缩的数据
    ///
    /// # 返回
    /// Result<Vec<u8>> - 压缩后的数据
    fn compress_data(&self, data: &[u8]) -> Result<Vec<u8>> {
        match self.config.compression {
            CompressionAlgorithm::None => Ok(data.to_vec()),
            CompressionAlgorithm::Gzip => {
                // TODO: 实现 Gzip 压缩
                // 当前返回原始数据避免编译错误
                Ok(data.to_vec())
            }
            CompressionAlgorithm::Lz4 => {
                // TODO: 实现 LZ4 压缩
                // 模拟快速压缩（实际应使用 lz4 crate）
                Ok(data.to_vec())
            }
            CompressionAlgorithm::Zstd => {
                // TODO: 实现 Zstd 压缩
                // 模拟高效压缩（实际应使用 zstd crate）
                Ok(data.to_vec())
            }
        }
    }

    /// 解压缩数据
    ///
    /// # 参数
    /// - `data`: 待解压缩的数据
    ///
    /// # 返回
    /// Result<Vec<u8>> - 解压缩后的数据
    fn decompress_data(&self, data: &[u8]) -> Result<Vec<u8>> {
        match self.config.compression {
            CompressionAlgorithm::None => Ok(data.to_vec()),
            CompressionAlgorithm::Gzip => {
                // TODO: 实现 Gzip 解压缩
                Ok(data.to_vec())
            }
            CompressionAlgorithm::Lz4 => {
                // TODO: 实现 LZ4 解压缩
                Ok(data.to_vec())
            }
            CompressionAlgorithm::Zstd => {
                // TODO: 实现 Zstd 解压缩
                Ok(data.to_vec())
            }
        }
    }

    /// 更新编码性能指标
    fn update_encode_metrics(
        &mut self,
        encode_time_micros: u64,
        raw_size: usize,
        compressed_size: usize,
    ) {
        self.metrics.encode_count += 1;
        self.metrics.total_encode_time_micros += encode_time_micros;
        self.metrics.total_raw_bytes += raw_size as u64;
        self.metrics.total_compressed_bytes += compressed_size as u64;

        self.metrics.avg_encode_time_micros =
            self.metrics.total_encode_time_micros as f64 / self.metrics.encode_count as f64;

        if self.metrics.total_raw_bytes > 0 {
            self.metrics.compression_ratio =
                self.metrics.total_compressed_bytes as f64 / self.metrics.total_raw_bytes as f64;
        }
    }

    /// 更新解码性能指标
    fn update_decode_metrics(&mut self, decode_time_micros: u64) {
        self.metrics.decode_count += 1;
        self.metrics.total_decode_time_micros += decode_time_micros;
        self.metrics.avg_decode_time_micros =
            self.metrics.total_decode_time_micros as f64 / self.metrics.decode_count as f64;
    }

    /// 获取性能指标
    ///
    /// # 返回
    /// &CodecMetrics - 性能指标引用
    pub fn get_metrics(&self) -> &CodecMetrics {
        &self.metrics
    }

    /// 重置性能指标
    pub fn reset_metrics(&mut self) {
        self.metrics = CodecMetrics::default();
    }

    /// 获取配置信息
    ///
    /// # 返回
    /// &CodecConfig - 配置信息引用
    pub fn get_config(&self) -> &CodecConfig {
        &self.config
    }

    /// 更新配置
    ///
    /// # 参数
    /// - `config`: 新的配置
    pub fn update_config(&mut self, config: CodecConfig) {
        self.config = config;
    }
}

impl Default for ProtocolCodec {
    fn default() -> Self {
        Self::new_default()
    }
}

impl MessageCodec for ProtocolCodec {
    fn encode(&self, message: &NativeMessage) -> Result<Vec<u8>> {
        let json_data =
            serde_json::to_vec(message).map_err(NativeMessagingError::SerializationError)?;

        self.compress_data(&json_data)
    }

    fn decode(&self, data: &[u8]) -> Result<NativeMessage> {
        let decompressed_data = self.decompress_data(data)?;

        serde_json::from_slice(&decompressed_data).map_err(NativeMessagingError::SerializationError)
    }
}

impl StreamCodec for ProtocolCodec {
    fn start_encode_stream(&self) -> Result<StreamEncoder> {
        Ok(StreamEncoder::new(self.config.compression))
    }

    fn start_decode_stream(&self) -> Result<StreamDecoder> {
        Ok(StreamDecoder::new())
    }
}

impl BatchCodec for ProtocolCodec {
    fn encode_batch(&self, messages: &[NativeMessage]) -> Result<Vec<u8>> {
        let batch = BatchMessage {
            batch_id: format!("batch_{}", std::process::id()),
            messages: messages.to_vec(),
            sequential: false,
            stop_on_error: false,
        };

        let json_data =
            serde_json::to_vec(&batch).map_err(NativeMessagingError::SerializationError)?;

        self.compress_data(&json_data)
    }

    fn decode_batch(&self, data: &[u8]) -> Result<Vec<NativeMessage>> {
        let decompressed_data = self.decompress_data(data)?;

        let batch: BatchMessage = serde_json::from_slice(&decompressed_data)
            .map_err(NativeMessagingError::SerializationError)?;

        Ok(batch.messages)
    }
}

/// 创建默认编解码器的便捷函数
///
/// # 返回
/// ProtocolCodec - 默认配置的编解码器
pub fn create_default_codec() -> ProtocolCodec {
    ProtocolCodec::new_default()
}

/// 创建高性能编解码器的便捷函数
///
/// # 返回
/// ProtocolCodec - 高性能配置的编解码器
pub fn create_high_performance_codec() -> ProtocolCodec {
    ProtocolCodec::new_high_performance()
}

/// 创建高压缩比编解码器的便捷函数
///
/// # 返回
/// ProtocolCodec - 高压缩比配置的编解码器
pub fn create_high_compression_codec() -> ProtocolCodec {
    ProtocolCodec::new_high_compression()
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::{ErrorCode, MessageType, ResponseStatus};

    #[test]
    fn test_codec_creation() {
        let codec = ProtocolCodec::new_default();
        assert_eq!(codec.get_config().compression, CompressionAlgorithm::None);
        assert!(!codec.get_config().enable_performance_monitoring);

        let hp_codec = ProtocolCodec::new_high_performance();
        assert_eq!(hp_codec.get_config().compression, CompressionAlgorithm::Lz4);
        assert!(hp_codec.get_config().enable_performance_monitoring);
    }

    #[test]
    fn test_message_encode_decode() {
        let codec = ProtocolCodec::new_default();
        let message = NativeMessage::new(
            MessageType::GetCredentials,
            "test-encode-123".to_string(),
            serde_json::json!({"domain": "example.com"}),
            "test_extension".to_string(),
        );

        // 编码
        let encoded = codec.encode(&message).unwrap();
        assert!(!encoded.is_empty());

        // 解码
        let decoded = codec.decode(&encoded).unwrap();
        assert_eq!(decoded.message_type, message.message_type);
        assert_eq!(decoded.request_id, message.request_id);
        assert_eq!(decoded.source, message.source);
    }

    #[test]
    fn test_outgoing_message_encode_decode() {
        let mut codec = ProtocolCodec::new_default();
        let outgoing = OutgoingMessage::success(
            "test-outgoing-123".to_string(),
            serde_json::json!({"result": "ok"}),
        );

        // 编码
        let encoded = codec.encode_outgoing(&outgoing).unwrap();
        assert!(!encoded.is_empty());

        // 解码
        let decoded = codec.decode_outgoing(&encoded).unwrap();
        assert_eq!(decoded.status, ResponseStatus::Success);
        assert_eq!(decoded.message.request_id, "test-outgoing-123");
    }

    #[test]
    fn test_native_format_encode_decode() {
        let mut codec = ProtocolCodec::new_default();
        let message = NativeMessage::new(
            MessageType::HealthCheck,
            "test-native-123".to_string(),
            serde_json::json!({}),
            "health_checker".to_string(),
        );

        // Native格式编码
        let encoded = codec.encode_native_format(&message).unwrap();
        assert!(encoded.len() >= 4); // 至少有长度前缀

        // 验证长度前缀
        let length_bytes = &encoded[0..4];
        let message_length = u32::from_le_bytes([
            length_bytes[0],
            length_bytes[1],
            length_bytes[2],
            length_bytes[3],
        ]) as usize;
        assert_eq!(encoded.len(), 4 + message_length);

        // Native格式解码
        let decoded = codec.decode_native_format(&encoded).unwrap();
        assert_eq!(decoded.message_type, message.message_type);
        assert_eq!(decoded.request_id, message.request_id);
    }

    #[test]
    fn test_invalid_native_format() {
        let mut codec = ProtocolCodec::new_default();

        // 测试长度不足的数据
        let short_data = vec![1, 2, 3];
        assert!(codec.decode_native_format(&short_data).is_err());

        // 测试长度不匹配的数据
        let invalid_data = vec![10, 0, 0, 0, 1, 2]; // 声明长度10，但只有2字节数据
        assert!(codec.decode_native_format(&invalid_data).is_err());
    }

    #[test]
    fn test_invalid_json_decode() {
        let codec = ProtocolCodec::new_default();
        let invalid_json = b"invalid json data";

        assert!(codec.decode(invalid_json).is_err());
    }

    #[test]
    fn test_compression_algorithms() {
        let configs = vec![
            CompressionAlgorithm::None,
            CompressionAlgorithm::Gzip,
            CompressionAlgorithm::Lz4,
            CompressionAlgorithm::Zstd,
        ];

        for compression in configs {
            let config = CodecConfig {
                compression,
                ..Default::default()
            };
            let codec = ProtocolCodec::new(config);
            assert_eq!(codec.get_config().compression, compression);
        }
    }

    #[test]
    fn test_performance_monitoring() {
        let mut codec = ProtocolCodec::new_high_performance();
        let outgoing = OutgoingMessage::success(
            "test-perf-123".to_string(),
            serde_json::json!({"data": "test"}),
        );

        // 执行几次编解码操作（使用性能监控的方法）
        for _ in 0..5 {
            let encoded = codec.encode_outgoing(&outgoing).unwrap();
            let _decoded = codec.decode_outgoing(&encoded).unwrap();
        }

        let metrics = codec.get_metrics();
        assert!(metrics.encode_count > 0);
        assert!(metrics.decode_count > 0);
        assert!(metrics.total_encode_time_micros > 0);
        assert!(metrics.total_decode_time_micros > 0);
    }

    #[test]
    fn test_version_compatibility() {
        let config = CodecConfig {
            enable_version_compatibility: true,
            supported_versions: vec![1, 2],
            ..Default::default()
        };
        let mut codec = ProtocolCodec::new(config);

        // 支持的版本
        let v1_message = NativeMessage::new_with_version(
            1,
            MessageType::GetCredentials,
            "test-v1-123".to_string(),
            serde_json::json!({}),
            "v1_client".to_string(),
        );
        assert!(codec.encode_native_format(&v1_message).is_ok());

        // 不支持的版本
        let v3_message = NativeMessage::new_with_version(
            3,
            MessageType::GetCredentials,
            "test-v3-123".to_string(),
            serde_json::json!({}),
            "v3_client".to_string(),
        );
        assert!(codec.encode_native_format(&v3_message).is_err());
    }

    #[test]
    fn test_message_size_limits() {
        let config = CodecConfig {
            max_message_size: 100, // 很小的限制
            ..Default::default()
        };
        let mut codec = ProtocolCodec::new(config);

        let large_message = NativeMessage::new(
            MessageType::GetCredentials,
            "test-large-123".to_string(),
            serde_json::json!({"large_data": "x".repeat(1000)}), // 大负载
            "large_sender".to_string(),
        );

        // 应该因为大小限制而失败
        assert!(codec.encode_native_format(&large_message).is_err());
    }

    #[test]
    fn test_stream_codec() {
        let codec = ProtocolCodec::new_default();

        // 创建流编码器
        let mut encoder = codec.start_encode_stream().unwrap();

        // 编码几个数据块
        let chunk1 = b"Hello, ";
        let chunk2 = b"World!";

        let stream_msg1 = encoder.encode_chunk(chunk1, false).unwrap();
        let stream_msg2 = encoder.encode_chunk(chunk2, true).unwrap();

        assert_eq!(stream_msg1.sequence, 0);
        assert!(!stream_msg1.is_final);
        assert_eq!(stream_msg2.sequence, 1);
        assert!(stream_msg2.is_final);

        // 创建流解码器
        let mut decoder = codec.start_decode_stream().unwrap();

        // 处理数据块
        assert!(decoder.process_chunk(stream_msg1).unwrap().is_none());
        let complete_data = decoder.process_chunk(stream_msg2).unwrap().unwrap();

        assert_eq!(complete_data, b"Hello, World!");
    }

    #[test]
    fn test_batch_codec() {
        let codec = ProtocolCodec::new_default();

        let messages = vec![
            NativeMessage::new(
                MessageType::GetCredentials,
                "batch-msg-1".to_string(),
                serde_json::json!({"domain": "example1.com"}),
                "batch_sender".to_string(),
            ),
            NativeMessage::new(
                MessageType::HealthCheck,
                "batch-msg-2".to_string(),
                serde_json::json!({}),
                "batch_sender".to_string(),
            ),
        ];

        // 批量编码
        let encoded = codec.encode_batch(&messages).unwrap();
        assert!(!encoded.is_empty());

        // 批量解码
        let decoded = codec.decode_batch(&encoded).unwrap();
        assert_eq!(decoded.len(), 2);
        assert_eq!(decoded[0].request_id, "batch-msg-1");
        assert_eq!(decoded[1].request_id, "batch-msg-2");
    }

    #[test]
    fn test_metrics_reset() {
        let mut codec = ProtocolCodec::new_high_performance();
        let outgoing =
            OutgoingMessage::success("test-reset-123".to_string(), serde_json::json!({}));

        // 执行一些操作（使用性能监控的方法）
        let _encoded = codec.encode_outgoing(&outgoing).unwrap();
        assert!(codec.get_metrics().encode_count > 0);

        // 重置指标
        codec.reset_metrics();
        assert_eq!(codec.get_metrics().encode_count, 0);
        assert_eq!(codec.get_metrics().total_encode_time_micros, 0);
    }

    #[test]
    fn test_config_update() {
        let mut codec = ProtocolCodec::new_default();
        assert_eq!(codec.get_config().compression, CompressionAlgorithm::None);

        let new_config = CodecConfig {
            compression: CompressionAlgorithm::Lz4,
            ..Default::default()
        };
        codec.update_config(new_config);
        assert_eq!(codec.get_config().compression, CompressionAlgorithm::Lz4);
    }

    #[test]
    fn test_convenience_functions() {
        let default_codec = create_default_codec();
        assert_eq!(
            default_codec.get_config().compression,
            CompressionAlgorithm::None
        );

        let hp_codec = create_high_performance_codec();
        assert_eq!(hp_codec.get_config().compression, CompressionAlgorithm::Lz4);

        let hc_codec = create_high_compression_codec();
        assert_eq!(
            hc_codec.get_config().compression,
            CompressionAlgorithm::Zstd
        );
    }

    #[test]
    fn test_stream_decoder_validation() {
        let codec = ProtocolCodec::new_default();
        let mut decoder = codec.start_decode_stream().unwrap();

        // 创建一个无效校验和的流消息
        let invalid_msg = StreamMessage {
            stream_id: "test_stream".to_string(),
            sequence: 0,
            is_final: true,
            data: b"test data".to_vec(),
            checksum: Some("invalid_checksum".to_string()),
        };

        // 应该因为校验和不匹配而失败
        assert!(decoder.process_chunk(invalid_msg).is_err());
    }

    #[test]
    fn test_error_response_encoding() {
        let mut codec = ProtocolCodec::new_default();
        let error_response = OutgoingMessage::error(
            "error-test-123".to_string(),
            ErrorCode::InvalidMessageFormat,
            "Test error message".to_string(),
        );

        let encoded = codec.encode_outgoing(&error_response).unwrap();
        let decoded = codec.decode_outgoing(&encoded).unwrap();

        match decoded.status {
            ResponseStatus::Error { code, message, .. } => {
                assert_eq!(code, ErrorCode::InvalidMessageFormat);
                assert_eq!(message, "Test error message");
            }
            _ => panic!("Expected error response"),
        }
    }
}
