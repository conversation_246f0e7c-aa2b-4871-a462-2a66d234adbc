//! Native Messaging 消息定义模块
//!
//! 定义跨浏览器兼容的消息类型、结构和相关操作

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};

/// Native Messaging 消息结构
///
/// 标准化的消息格式，支持跨浏览器兼容和版本迁移
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct NativeMessage {
    /// 协议版本号
    pub version: u32,
    /// 消息类型
    pub message_type: MessageType,
    /// 请求ID，用于匹配请求和响应
    pub request_id: String,
    /// 消息负载数据
    pub payload: serde_json::Value,
    /// 时间戳
    pub timestamp: u64,
    /// 消息来源标识
    pub source: String,
    /// 可选的元数据
    #[serde(default)]
    pub metadata: HashMap<String, String>,
    /// 协议扩展字段（用于向后兼容）
    #[serde(default, skip_serializing_if = "HashMap::is_empty")]
    pub extensions: HashMap<String, serde_json::Value>,
    /// 消息优先级
    #[serde(default)]
    pub priority: MessagePriority,
    /// 超时设置（毫秒）
    #[serde(default)]
    pub timeout_ms: Option<u64>,
}

/// 消息类型枚举
///
/// 定义所有支持的消息类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[serde(tag = "type", content = "value")]
pub enum MessageType {
    /// 获取凭证信息
    GetCredentials,
    /// 保存凭证信息
    SaveCredentials,
    /// 删除凭证信息
    DeleteCredentials,
    /// 更新凭证信息
    UpdateCredentials,
    /// 健康检查
    HealthCheck,
    /// 获取配置信息
    GetConfig,
    /// 更新配置信息
    UpdateConfig,
    /// 获取状态信息
    GetStatus,
    /// 错误响应
    Error,
    /// 成功响应
    Success,
    /// 协议握手
    Handshake,
    /// 协议升级请求
    ProtocolUpgrade,
    /// 批量操作
    BatchOperation,
    /// 流式数据传输
    StreamData,
    /// 用户认证
    Auth,
    /// 连接测试
    Ping,
    /// 密码管理
    Password,
    /// 版本信息
    Version,
    /// 测试消息
    Test,
    /// 自定义消息类型
    Custom(String),
}

/// 消息优先级枚举
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord, Default)]
pub enum MessagePriority {
    /// 低优先级
    Low = 0,
    /// 普通优先级
    #[default]
    Normal = 1,
    /// 高优先级
    High = 2,
    /// 紧急优先级
    Critical = 3,
}

/// 传入消息类型别名
pub type IncomingMessage = NativeMessage;

/// 传出消息结构
///
/// 用于构建发送给浏览器扩展的响应消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutgoingMessage {
    /// 基础消息信息
    #[serde(flatten)]
    pub message: NativeMessage,
    /// 响应状态
    pub status: ResponseStatus,
    /// 响应耗时（毫秒）
    #[serde(default)]
    pub processing_time_ms: Option<u64>,
}

/// 响应状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ResponseStatus {
    /// 成功响应
    Success,
    /// 错误响应
    Error {
        code: ErrorCode,
        message: String,
        details: Option<serde_json::Value>,
    },
    /// 部分成功
    PartialSuccess { warnings: Vec<String> },
    /// 正在处理
    Processing { progress: f32 },
    /// 需要更多数据
    NeedMoreData { required_fields: Vec<String> },
}

/// 标准化错误码枚举
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
#[repr(u32)]
pub enum ErrorCode {
    // 1000-1999: 协议相关错误
    /// 协议版本不支持
    UnsupportedProtocolVersion = 1001,
    /// 消息格式无效
    InvalidMessageFormat = 1002,
    /// 请求ID无效
    InvalidRequestId = 1003,
    /// 消息过大
    MessageTooLarge = 1004,
    /// 消息超时
    MessageTimeout = 1005,

    // 2000-2999: 认证和安全错误
    /// 认证失败
    AuthenticationFailed = 2001,
    /// 权限不足
    InsufficientPermissions = 2002,
    /// 无效的扩展ID
    InvalidExtensionId = 2003,
    /// 签名验证失败
    SignatureVerificationFailed = 2004,

    // 3000-3999: 业务逻辑错误
    /// 凭证未找到
    CredentialsNotFound = 3001,
    /// 凭证已存在
    CredentialsAlreadyExists = 3002,
    /// 配置无效
    InvalidConfiguration = 3003,
    /// 操作不支持
    OperationNotSupported = 3004,

    // 4000-4999: 系统和资源错误
    /// 内部服务器错误
    InternalServerError = 4001,
    /// 服务不可用
    ServiceUnavailable = 4002,
    /// 资源不足
    InsufficientResources = 4003,
    /// 数据库错误
    DatabaseError = 4004,

    // 5000-5999: 网络和连接错误
    /// 连接失败
    ConnectionFailed = 5001,
    /// 网络超时
    NetworkTimeout = 5002,
    /// 连接丢失
    ConnectionLost = 5003,

    // 9000-9999: 自定义错误
    /// 自定义错误
    Custom(u32),
}

/// 协议兼容性信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProtocolCompatibility {
    /// 当前协议版本
    pub current_version: u32,
    /// 支持的最低版本
    pub min_supported_version: u32,
    /// 支持的最高版本
    pub max_supported_version: u32,
    /// 推荐的协议版本
    pub recommended_version: u32,
    /// 版本特性映射
    pub version_features: HashMap<u32, Vec<String>>,
    /// 废弃的版本列表
    pub deprecated_versions: Vec<u32>,
}

/// 批量操作消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchMessage {
    /// 批量操作ID
    pub batch_id: String,
    /// 子消息列表
    pub messages: Vec<NativeMessage>,
    /// 是否按顺序执行
    pub sequential: bool,
    /// 失败时是否停止
    pub stop_on_error: bool,
}

/// 流式数据消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamMessage {
    /// 流ID
    pub stream_id: String,
    /// 序列号
    pub sequence: u64,
    /// 是否为最后一个数据块
    pub is_final: bool,
    /// 数据块
    pub data: Vec<u8>,
    /// 校验和
    pub checksum: Option<String>,
}

impl NativeMessage {
    /// 创建新的消息实例
    ///
    /// # 参数
    /// - `message_type`: 消息类型
    /// - `request_id`: 请求ID
    /// - `payload`: 消息负载
    /// - `source`: 消息来源
    ///
    /// # 返回
    /// NativeMessage - 新的消息实例
    pub fn new(
        message_type: MessageType,
        request_id: String,
        payload: serde_json::Value,
        source: String,
    ) -> Self {
        Self {
            version: super::CURRENT_PROTOCOL_VERSION,
            message_type,
            request_id,
            payload,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            source,
            metadata: HashMap::new(),
            extensions: HashMap::new(),
            priority: MessagePriority::Normal,
            timeout_ms: None,
        }
    }

    /// 创建指定版本的消息
    ///
    /// # 参数
    /// - `version`: 协议版本
    /// - `message_type`: 消息类型
    /// - `request_id`: 请求ID
    /// - `payload`: 消息负载
    /// - `source`: 消息来源
    ///
    /// # 返回
    /// NativeMessage - 新的消息实例
    pub fn new_with_version(
        version: u32,
        message_type: MessageType,
        request_id: String,
        payload: serde_json::Value,
        source: String,
    ) -> Self {
        let mut message = Self::new(message_type, request_id, payload, source);
        message.version = version;
        message
    }

    /// 添加元数据
    ///
    /// # 参数
    /// - `key`: 元数据键
    /// - `value`: 元数据值
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }

    /// 添加扩展字段
    ///
    /// # 参数
    /// - `key`: 扩展字段键
    /// - `value`: 扩展字段值
    pub fn with_extension(mut self, key: String, value: serde_json::Value) -> Self {
        self.extensions.insert(key, value);
        self
    }

    /// 设置优先级
    ///
    /// # 参数
    /// - `priority`: 消息优先级
    pub fn with_priority(mut self, priority: MessagePriority) -> Self {
        self.priority = priority;
        self
    }

    /// 设置超时时间
    ///
    /// # 参数
    /// - `timeout_ms`: 超时时间（毫秒）
    pub fn with_timeout(mut self, timeout_ms: u64) -> Self {
        self.timeout_ms = Some(timeout_ms);
        self
    }

    /// 验证消息格式
    ///
    /// # 返回
    /// bool - 消息格式是否有效
    pub fn is_valid(&self) -> bool {
        !self.request_id.is_empty()
            && !self.source.is_empty()
            && self.version > 0
            && self.is_valid_request_id()
    }

    /// 验证请求ID格式
    ///
    /// # 返回
    /// bool - 请求ID格式是否有效
    pub fn is_valid_request_id(&self) -> bool {
        // 请求ID应该是UUID格式或者符合特定规则
        self.request_id.len() >= 8
            && self.request_id.len() <= 64
            && self
                .request_id
                .chars()
                .all(|c| c.is_alphanumeric() || c == '-' || c == '_')
    }

    /// 检查是否与指定版本兼容
    ///
    /// # 参数
    /// - `target_version`: 目标版本
    ///
    /// # 返回
    /// bool - 是否兼容
    pub fn is_compatible_with_version(&self, target_version: u32) -> bool {
        // 向后兼容规则：高版本可以处理低版本消息
        self.version <= target_version
    }

    /// 升级到指定版本
    ///
    /// # 参数
    /// - `target_version`: 目标版本
    ///
    /// # 返回
    /// Result<NativeMessage> - 升级后的消息
    pub fn upgrade_to_version(&self, target_version: u32) -> Result<NativeMessage, String> {
        if target_version < self.version {
            return Err("不能降级到低版本".to_string());
        }

        let mut upgraded = self.clone();
        upgraded.version = target_version;

        // 根据版本添加新字段
        match target_version {
            2 => {
                // V2 添加了优先级和超时字段，已经在结构中
            }
            3 => {
                // V3 可能添加新的扩展字段
                upgraded.extensions.insert(
                    "v3_features".to_string(),
                    serde_json::json!({"enabled": true}),
                );
            }
            _ => {
                // 未知版本，保持现状
            }
        }

        Ok(upgraded)
    }

    /// 获取消息大小（字节）
    ///
    /// # 返回
    /// usize - 消息大小
    pub fn size_bytes(&self) -> usize {
        serde_json::to_vec(self).map(|v| v.len()).unwrap_or(0)
    }

    /// 判断是否为高优先级消息
    ///
    /// # 返回
    /// bool - 是否为高优先级
    pub fn is_high_priority(&self) -> bool {
        matches!(
            self.priority,
            MessagePriority::High | MessagePriority::Critical
        )
    }

    /// 判断是否已超时
    ///
    /// # 返回
    /// bool - 是否已超时
    pub fn is_expired(&self) -> bool {
        if let Some(timeout_ms) = self.timeout_ms {
            let current_time = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_millis() as u64;
            let message_time = self.timestamp * 1000; // 转换为毫秒
            current_time > message_time + timeout_ms
        } else {
            false
        }
    }
}

impl OutgoingMessage {
    /// 创建基本消息实例（用于测试）
    ///
    /// # 参数
    /// - `message`: 基础消息
    ///
    /// # 返回
    /// OutgoingMessage - 新的消息实例
    pub fn new(message: NativeMessage) -> Self {
        Self {
            message,
            status: ResponseStatus::Success,
            processing_time_ms: None,
        }
    }

    /// 创建成功响应消息
    ///
    /// # 参数
    /// - `request_id`: 原始请求ID
    /// - `payload`: 响应数据
    ///
    /// # 返回
    /// OutgoingMessage - 成功响应消息
    pub fn success(request_id: String, payload: serde_json::Value) -> Self {
        Self {
            message: NativeMessage::new(
                MessageType::Success,
                request_id,
                payload,
                "native_host".to_string(),
            ),
            status: ResponseStatus::Success,
            processing_time_ms: None,
        }
    }

    /// 创建错误响应消息
    ///
    /// # 参数
    /// - `request_id`: 原始请求ID
    /// - `error_code`: 错误码
    /// - `error_message`: 错误信息
    ///
    /// # 返回
    /// OutgoingMessage - 错误响应消息
    pub fn error(request_id: String, error_code: ErrorCode, error_message: String) -> Self {
        Self {
            message: NativeMessage::new(
                MessageType::Error,
                request_id,
                serde_json::json!({
                    "error_code": error_code.as_u32(),
                    "error_message": error_message
                }),
                "native_host".to_string(),
            ),
            status: ResponseStatus::Error {
                code: error_code,
                message: error_message,
                details: None,
            },
            processing_time_ms: None,
        }
    }

    /// 创建详细错误响应消息
    ///
    /// # 参数
    /// - `request_id`: 原始请求ID
    /// - `error_code`: 错误码
    /// - `error_message`: 错误信息
    /// - `details`: 详细信息
    ///
    /// # 返回
    /// OutgoingMessage - 详细错误响应消息
    pub fn detailed_error(
        request_id: String,
        error_code: ErrorCode,
        error_message: String,
        details: serde_json::Value,
    ) -> Self {
        Self {
            message: NativeMessage::new(
                MessageType::Error,
                request_id,
                serde_json::json!({
                    "error_code": error_code.as_u32(),
                    "error_message": error_message,
                    "details": details
                }),
                "native_host".to_string(),
            ),
            status: ResponseStatus::Error {
                code: error_code,
                message: error_message,
                details: Some(details),
            },
            processing_time_ms: None,
        }
    }

    /// 创建处理中响应消息
    ///
    /// # 参数
    /// - `request_id`: 原始请求ID
    /// - `progress`: 进度（0.0-1.0）
    ///
    /// # 返回
    /// OutgoingMessage - 处理中响应消息
    pub fn processing(request_id: String, progress: f32) -> Self {
        Self {
            message: NativeMessage::new(
                MessageType::Success,
                request_id,
                serde_json::json!({
                    "status": "processing",
                    "progress": progress
                }),
                "native_host".to_string(),
            ),
            status: ResponseStatus::Processing { progress },
            processing_time_ms: None,
        }
    }

    /// 设置处理时间
    ///
    /// # 参数
    /// - `processing_time_ms`: 处理时间（毫秒）
    pub fn with_processing_time(mut self, processing_time_ms: u64) -> Self {
        self.processing_time_ms = Some(processing_time_ms);
        self
    }
}

impl MessageType {
    /// 判断是否为请求类型的消息
    ///
    /// # 返回
    /// bool - 是否为请求消息
    pub fn is_request(&self) -> bool {
        matches!(
            self,
            MessageType::GetCredentials
                | MessageType::SaveCredentials
                | MessageType::DeleteCredentials
                | MessageType::UpdateCredentials
                | MessageType::HealthCheck
                | MessageType::GetConfig
                | MessageType::UpdateConfig
                | MessageType::GetStatus
                | MessageType::Handshake
                | MessageType::ProtocolUpgrade
                | MessageType::BatchOperation
                | MessageType::StreamData
                | MessageType::Custom(_)
        )
    }

    /// 判断是否为响应类型的消息
    ///
    /// # 返回
    /// bool - 是否为响应消息
    pub fn is_response(&self) -> bool {
        matches!(self, MessageType::Success | MessageType::Error)
    }

    /// 获取消息类型的字符串表示
    ///
    /// # 返回
    /// String - 消息类型字符串
    pub fn as_string(&self) -> String {
        match self {
            MessageType::GetCredentials => "get_credentials".to_string(),
            MessageType::SaveCredentials => "save_credentials".to_string(),
            MessageType::DeleteCredentials => "delete_credentials".to_string(),
            MessageType::UpdateCredentials => "update_credentials".to_string(),
            MessageType::HealthCheck => "health_check".to_string(),
            MessageType::GetConfig => "get_config".to_string(),
            MessageType::UpdateConfig => "update_config".to_string(),
            MessageType::GetStatus => "get_status".to_string(),
            MessageType::Error => "error".to_string(),
            MessageType::Success => "success".to_string(),
            MessageType::Handshake => "handshake".to_string(),
            MessageType::ProtocolUpgrade => "protocol_upgrade".to_string(),
            MessageType::BatchOperation => "batch_operation".to_string(),
            MessageType::StreamData => "stream_data".to_string(),
            MessageType::Auth => "auth".to_string(),
            MessageType::Ping => "ping".to_string(),
            MessageType::Password => "password".to_string(),
            MessageType::Version => "version".to_string(),
            MessageType::Test => "test".to_string(),
            MessageType::Custom(name) => format!("custom_{}", name),
        }
    }

    /// 获取消息类型的默认超时时间（毫秒）
    ///
    /// # 返回
    /// u64 - 默认超时时间
    pub fn default_timeout_ms(&self) -> u64 {
        match self {
            MessageType::HealthCheck | MessageType::Ping => 5000,
            MessageType::GetCredentials | MessageType::GetConfig | MessageType::GetStatus | MessageType::Version => 10000,
            MessageType::SaveCredentials
            | MessageType::UpdateCredentials
            | MessageType::DeleteCredentials => 15000,
            MessageType::UpdateConfig => 30000,
            MessageType::BatchOperation => 60000,
            MessageType::StreamData => 120000,
            MessageType::Auth | MessageType::Password => 20000,
            MessageType::Test => 5000,
            MessageType::Custom(_) => 30000,
            _ => 10000,
        }
    }
}

impl ErrorCode {
    /// 获取错误码的数值
    ///
    /// # 返回
    /// u32 - 错误码数值
    pub fn as_u32(&self) -> u32 {
        match self {
            ErrorCode::UnsupportedProtocolVersion => 1001,
            ErrorCode::InvalidMessageFormat => 1002,
            ErrorCode::InvalidRequestId => 1003,
            ErrorCode::MessageTooLarge => 1004,
            ErrorCode::MessageTimeout => 1005,
            ErrorCode::AuthenticationFailed => 2001,
            ErrorCode::InsufficientPermissions => 2002,
            ErrorCode::InvalidExtensionId => 2003,
            ErrorCode::SignatureVerificationFailed => 2004,
            ErrorCode::CredentialsNotFound => 3001,
            ErrorCode::CredentialsAlreadyExists => 3002,
            ErrorCode::InvalidConfiguration => 3003,
            ErrorCode::OperationNotSupported => 3004,
            ErrorCode::InternalServerError => 4001,
            ErrorCode::ServiceUnavailable => 4002,
            ErrorCode::InsufficientResources => 4003,
            ErrorCode::DatabaseError => 4004,
            ErrorCode::ConnectionFailed => 5001,
            ErrorCode::NetworkTimeout => 5002,
            ErrorCode::ConnectionLost => 5003,
            ErrorCode::Custom(code) => *code,
        }
    }

    /// 获取错误码的描述
    ///
    /// # 返回
    /// &str - 错误码描述
    pub fn description(&self) -> &str {
        match self {
            ErrorCode::UnsupportedProtocolVersion => "不支持的协议版本",
            ErrorCode::InvalidMessageFormat => "消息格式无效",
            ErrorCode::InvalidRequestId => "请求ID无效",
            ErrorCode::MessageTooLarge => "消息过大",
            ErrorCode::MessageTimeout => "消息超时",
            ErrorCode::AuthenticationFailed => "认证失败",
            ErrorCode::InsufficientPermissions => "权限不足",
            ErrorCode::InvalidExtensionId => "无效的扩展ID",
            ErrorCode::SignatureVerificationFailed => "签名验证失败",
            ErrorCode::CredentialsNotFound => "凭证未找到",
            ErrorCode::CredentialsAlreadyExists => "凭证已存在",
            ErrorCode::InvalidConfiguration => "配置无效",
            ErrorCode::OperationNotSupported => "操作不支持",
            ErrorCode::InternalServerError => "内部服务器错误",
            ErrorCode::ServiceUnavailable => "服务不可用",
            ErrorCode::InsufficientResources => "资源不足",
            ErrorCode::DatabaseError => "数据库错误",
            ErrorCode::ConnectionFailed => "连接失败",
            ErrorCode::NetworkTimeout => "网络超时",
            ErrorCode::ConnectionLost => "连接丢失",
            ErrorCode::Custom(_) => "自定义错误",
        }
    }

    /// 判断错误是否可重试
    ///
    /// # 返回
    /// bool - 是否可重试
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            ErrorCode::MessageTimeout
                | ErrorCode::ServiceUnavailable
                | ErrorCode::InsufficientResources
                | ErrorCode::ConnectionFailed
                | ErrorCode::NetworkTimeout
                | ErrorCode::ConnectionLost
        )
    }
}

impl ProtocolCompatibility {
    /// 创建默认的协议兼容性信息
    ///
    /// # 返回
    /// ProtocolCompatibility - 默认兼容性信息
    pub fn new_default() -> Self {
        let mut version_features = HashMap::new();
        version_features.insert(1, vec!["basic_messaging".to_string()]);
        version_features.insert(
            2,
            vec![
                "priority_support".to_string(),
                "timeout_support".to_string(),
            ],
        );
        version_features.insert(
            3,
            vec!["batch_operations".to_string(), "streaming".to_string()],
        );

        Self {
            current_version: super::CURRENT_PROTOCOL_VERSION,
            min_supported_version: super::MIN_SUPPORTED_VERSION,
            max_supported_version: super::CURRENT_PROTOCOL_VERSION,
            recommended_version: super::CURRENT_PROTOCOL_VERSION,
            version_features,
            deprecated_versions: vec![],
        }
    }

    /// 检查版本是否支持
    ///
    /// # 参数
    /// - `version`: 要检查的版本
    ///
    /// # 返回
    /// bool - 是否支持
    pub fn is_version_supported(&self, version: u32) -> bool {
        version >= self.min_supported_version && version <= self.max_supported_version
    }

    /// 检查版本是否已废弃
    ///
    /// # 参数
    /// - `version`: 要检查的版本
    ///
    /// # 返回
    /// bool - 是否已废弃
    pub fn is_version_deprecated(&self, version: u32) -> bool {
        self.deprecated_versions.contains(&version)
    }

    /// 获取版本支持的特性
    ///
    /// # 参数
    /// - `version`: 版本号
    ///
    /// # 返回
    /// Vec<String> - 支持的特性列表
    pub fn get_version_features(&self, version: u32) -> Vec<String> {
        self.version_features
            .get(&version)
            .cloned()
            .unwrap_or_default()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_native_message_creation() {
        let message = NativeMessage::new(
            MessageType::GetCredentials,
            "test-123".to_string(),
            serde_json::json!({"domain": "example.com"}),
            "test_extension".to_string(),
        );

        assert_eq!(message.message_type, MessageType::GetCredentials);
        assert_eq!(message.request_id, "test-123");
        assert_eq!(message.source, "test_extension");
        assert!(message.is_valid());
    }

    #[test]
    fn test_outgoing_message_success() {
        let message =
            OutgoingMessage::success("test-123".to_string(), serde_json::json!({"result": "ok"}));

        assert_eq!(message.status, ResponseStatus::Success);
        assert_eq!(message.message.request_id, "test-123");
    }

    #[test]
    fn test_outgoing_message_error() {
        let message = OutgoingMessage::error(
            "test-123".to_string(),
            ErrorCode::InvalidMessageFormat,
            "Invalid format".to_string(),
        );

        match message.status {
            ResponseStatus::Error {
                code, message: msg, ..
            } => {
                assert_eq!(code, ErrorCode::InvalidMessageFormat);
                assert_eq!(msg, "Invalid format");
            }
            _ => panic!("Expected error response"),
        }
    }

    #[test]
    fn test_message_type_classification() {
        assert!(MessageType::GetCredentials.is_request());
        assert!(!MessageType::GetCredentials.is_response());
        assert!(MessageType::Success.is_response());
        assert!(!MessageType::Success.is_request());
    }

    #[test]
    fn test_message_serialization() {
        let message = NativeMessage::new(
            MessageType::HealthCheck,
            "health-123".to_string(),
            serde_json::json!({}),
            "extension".to_string(),
        );

        let serialized = serde_json::to_string(&message).unwrap();
        let deserialized: NativeMessage = serde_json::from_str(&serialized).unwrap();

        assert_eq!(message, deserialized);
    }

    #[test]
    fn test_message_with_extensions() {
        let message = NativeMessage::new(
            MessageType::Custom("test_operation".to_string()),
            "ext-123".to_string(),
            serde_json::json!({"data": "test"}),
            "test_source".to_string(),
        )
        .with_extension("custom_field".to_string(), serde_json::json!({"value": 42}))
        .with_priority(MessagePriority::High)
        .with_timeout(30000);

        assert_eq!(message.priority, MessagePriority::High);
        assert_eq!(message.timeout_ms, Some(30000));
        assert!(message.extensions.contains_key("custom_field"));
        assert!(message.is_high_priority());
    }

    #[test]
    fn test_message_version_compatibility() {
        let message = NativeMessage::new_with_version(
            1,
            MessageType::GetCredentials,
            "compat-123".to_string(),
            serde_json::json!({}),
            "source".to_string(),
        );

        assert!(message.is_compatible_with_version(2));
        assert!(message.is_compatible_with_version(1));
        assert!(!message.is_compatible_with_version(0));
    }

    #[test]
    fn test_message_upgrade() {
        let v1_message = NativeMessage::new_with_version(
            1,
            MessageType::GetCredentials,
            "upgrade-123".to_string(),
            serde_json::json!({}),
            "source".to_string(),
        );

        let v2_message = v1_message.upgrade_to_version(2).unwrap();
        assert_eq!(v2_message.version, 2);
        assert_eq!(v2_message.request_id, "upgrade-123");

        // 测试不能降级
        let downgrade_result = v2_message.upgrade_to_version(1);
        assert!(downgrade_result.is_err());
    }

    #[test]
    fn test_error_code_properties() {
        let error_code = ErrorCode::NetworkTimeout;
        assert_eq!(error_code.as_u32(), 5002);
        assert!(error_code.is_retryable());
        assert_eq!(error_code.description(), "网络超时");

        let non_retryable = ErrorCode::AuthenticationFailed;
        assert!(!non_retryable.is_retryable());
    }

    #[test]
    fn test_protocol_compatibility() {
        let compat = ProtocolCompatibility::new_default();

        assert!(compat.is_version_supported(1));
        assert!(compat.is_version_supported(2));
        assert!(!compat.is_version_deprecated(2));

        let v1_features = compat.get_version_features(1);
        assert!(v1_features.contains(&"basic_messaging".to_string()));

        let v2_features = compat.get_version_features(2);
        assert!(v2_features.contains(&"priority_support".to_string()));
        assert!(v2_features.contains(&"timeout_support".to_string()));
    }

    #[test]
    fn test_message_timeout() {
        let mut message = NativeMessage::new(
            MessageType::GetCredentials,
            "timeout-123".to_string(),
            serde_json::json!({}),
            "source".to_string(),
        )
        .with_timeout(1); // 1ms 超时

        // 设置过去的时间戳来模拟超时
        message.timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
            - 1; // 1秒前

        assert!(message.is_expired());
    }

    #[test]
    fn test_batch_message() {
        let msg1 = NativeMessage::new(
            MessageType::GetCredentials,
            "batch-1".to_string(),
            serde_json::json!({"domain": "example1.com"}),
            "source".to_string(),
        );

        let msg2 = NativeMessage::new(
            MessageType::GetCredentials,
            "batch-2".to_string(),
            serde_json::json!({"domain": "example2.com"}),
            "source".to_string(),
        );

        let batch = BatchMessage {
            batch_id: "batch-123".to_string(),
            messages: vec![msg1, msg2],
            sequential: true,
            stop_on_error: false,
        };

        assert_eq!(batch.messages.len(), 2);
        assert!(batch.sequential);
        assert!(!batch.stop_on_error);
    }

    #[test]
    fn test_stream_message() {
        let stream = StreamMessage {
            stream_id: "stream-123".to_string(),
            sequence: 1,
            is_final: false,
            data: b"Hello, World!".to_vec(),
            checksum: Some("abcd1234".to_string()),
        };

        assert_eq!(stream.sequence, 1);
        assert!(!stream.is_final);
        assert_eq!(stream.data.len(), 13);
        assert!(stream.checksum.is_some());
    }

    #[test]
    fn test_message_priority_ordering() {
        assert!(MessagePriority::Critical > MessagePriority::High);
        assert!(MessagePriority::High > MessagePriority::Normal);
        assert!(MessagePriority::Normal > MessagePriority::Low);
    }

    #[test]
    fn test_detailed_error_response() {
        let details = serde_json::json!({
            "field": "username",
            "expected": "string",
            "actual": "null"
        });

        let message = OutgoingMessage::detailed_error(
            "error-123".to_string(),
            ErrorCode::InvalidMessageFormat,
            "Field validation failed".to_string(),
            details.clone(),
        );

        match message.status {
            ResponseStatus::Error {
                code,
                message: msg,
                details: Some(det),
            } => {
                assert_eq!(code, ErrorCode::InvalidMessageFormat);
                assert_eq!(msg, "Field validation failed");
                assert_eq!(det, details);
            }
            _ => panic!("Expected detailed error response"),
        }
    }

    #[test]
    fn test_processing_response() {
        let message = OutgoingMessage::processing("proc-123".to_string(), 0.75);

        match message.status {
            ResponseStatus::Processing { progress } => {
                assert_eq!(progress, 0.75);
            }
            _ => panic!("Expected processing response"),
        }
    }

    #[test]
    fn test_message_default_timeouts() {
        assert_eq!(MessageType::HealthCheck.default_timeout_ms(), 5000);
        assert_eq!(MessageType::GetCredentials.default_timeout_ms(), 10000);
        assert_eq!(MessageType::BatchOperation.default_timeout_ms(), 60000);
        assert_eq!(
            MessageType::Custom("test".to_string()).default_timeout_ms(),
            30000
        );
    }
}
