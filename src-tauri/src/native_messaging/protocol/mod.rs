//! Native Messaging 协议层模块
//!
//! 定义跨浏览器兼容的消息格式、协议验证器和编解码器

pub mod codec;
pub mod message;
pub mod validator;

// 公共导出
pub use codec::{MessageCodec, ProtocolCodec};
pub use message::{IncomingMessage, MessageType, NativeMessage, OutgoingMessage};
pub use validator::ProtocolValidator;

/// 协议版本常量
pub const CURRENT_PROTOCOL_VERSION: u32 = 2;
pub const MIN_SUPPORTED_VERSION: u32 = 1;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_protocol_versions() {
        assert!(CURRENT_PROTOCOL_VERSION >= MIN_SUPPORTED_VERSION);
        assert!(CURRENT_PROTOCOL_VERSION <= 10); // 合理的版本上限
    }
}
