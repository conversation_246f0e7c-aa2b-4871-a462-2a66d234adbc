//! Native Messaging 协议验证器模块
//!
//! 提供消息格式验证、协议版本检查和安全验证功能

use super::message::{
    ErrorCode, MessagePriority, MessageType, NativeMessage, ProtocolCompatibility,
};
use crate::native_messaging::error::{NativeMessagingError, Result};
use std::collections::HashSet;
use std::time::{SystemTime, UNIX_EPOCH};

/// 协议验证器
///
/// 负责验证消息格式、协议版本和基本安全检查
pub struct ProtocolValidator {
    /// 支持的协议版本列表
    supported_versions: Vec<u32>,
    /// 是否启用严格模式验证
    strict_mode: bool,
    /// 协议兼容性信息
    compatibility: ProtocolCompatibility,
    /// 允许的消息类型集合
    allowed_message_types: Option<HashSet<MessageType>>,
    /// 最大消息大小（字节）
    max_message_size: usize,
    /// 最大时间偏差（秒）
    max_time_skew: u64,
    /// 是否启用性能监控
    performance_monitoring: bool,
}

/// 验证规则配置
#[derive(Debug, Clone)]
pub struct ValidationRules {
    /// 最大消息大小
    pub max_message_size: usize,
    /// 最大时间偏差
    pub max_time_skew_seconds: u64,
    /// 最大元数据项数
    pub max_metadata_items: usize,
    /// 最大扩展字段数
    pub max_extension_fields: usize,
    /// 最大请求ID长度
    pub max_request_id_length: usize,
    /// 最小请求ID长度
    pub min_request_id_length: usize,
    /// 允许的来源模式
    pub allowed_source_patterns: Vec<String>,
    /// 是否启用负载大小检查
    pub enable_payload_size_check: bool,
    /// 是否启用时间戳验证
    pub enable_timestamp_validation: bool,
}

impl Default for ValidationRules {
    fn default() -> Self {
        Self {
            max_message_size: 1024 * 1024, // 1MB
            max_time_skew_seconds: 300,    // 5分钟
            max_metadata_items: 20,
            max_extension_fields: 10,
            max_request_id_length: 64,
            min_request_id_length: 8,
            allowed_source_patterns: vec![
                "chrome-extension://*".to_string(),
                "moz-extension://*".to_string(),
                "safari-web-extension://*".to_string(),
                "ms-browser-extension://*".to_string(),
            ],
            enable_payload_size_check: true,
            enable_timestamp_validation: true,
        }
    }
}

/// 验证结果详情
#[derive(Debug, Clone)]
pub struct ValidationResult {
    /// 验证是否通过
    pub is_valid: bool,
    /// 错误信息列表
    pub errors: Vec<ValidationError>,
    /// 警告信息列表
    pub warnings: Vec<String>,
    /// 验证耗时（微秒）
    pub validation_time_micros: u64,
    /// 建议的修复方案
    pub suggestions: Vec<String>,
}

/// 验证错误详情
#[derive(Debug, Clone)]
pub struct ValidationError {
    /// 错误码
    pub code: ErrorCode,
    /// 错误字段
    pub field: String,
    /// 错误描述
    pub message: String,
    /// 错误严重程度
    pub severity: ValidationSeverity,
}

/// 验证错误严重程度
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ValidationSeverity {
    /// 致命错误 - 必须修复
    Fatal,
    /// 错误 - 建议修复
    Error,
    /// 警告 - 可选修复
    Warning,
    /// 信息 - 仅供参考
    Info,
}

impl ProtocolValidator {
    /// 创建新的协议验证器
    ///
    /// # 参数
    /// - `supported_versions`: 支持的协议版本列表
    /// - `strict_mode`: 是否启用严格模式
    ///
    /// # 返回
    /// ProtocolValidator - 验证器实例
    pub fn new(supported_versions: Vec<u32>, strict_mode: bool) -> Self {
        Self {
            supported_versions,
            strict_mode,
            compatibility: ProtocolCompatibility::new_default(),
            allowed_message_types: None,
            max_message_size: 1024 * 1024, // 1MB
            max_time_skew: 300,            // 5分钟
            performance_monitoring: false,
        }
    }

    /// 创建默认验证器
    ///
    /// # 返回
    /// ProtocolValidator - 使用默认配置的验证器
    pub fn new_default() -> Self {
        Self::new(
            vec![
                super::MIN_SUPPORTED_VERSION,
                super::CURRENT_PROTOCOL_VERSION,
            ],
            true,
        )
    }

    /// 创建高性能验证器
    ///
    /// # 返回
    /// ProtocolValidator - 优化性能的验证器
    pub fn new_performance_optimized() -> Self {
        Self {
            supported_versions: vec![super::CURRENT_PROTOCOL_VERSION],
            strict_mode: false,
            compatibility: ProtocolCompatibility::new_default(),
            allowed_message_types: None,
            max_message_size: 1024 * 1024,
            max_time_skew: 300,
            performance_monitoring: true,
        }
    }

    /// 创建带自定义规则的验证器
    ///
    /// # 参数
    /// - `rules`: 验证规则配置
    ///
    /// # 返回
    /// ProtocolValidator - 自定义验证器
    pub fn new_with_rules(rules: ValidationRules) -> Self {
        Self {
            supported_versions: vec![
                super::MIN_SUPPORTED_VERSION,
                super::CURRENT_PROTOCOL_VERSION,
            ],
            strict_mode: true,
            compatibility: ProtocolCompatibility::new_default(),
            allowed_message_types: None,
            max_message_size: rules.max_message_size,
            max_time_skew: rules.max_time_skew_seconds,
            performance_monitoring: false,
        }
    }

    /// 设置允许的消息类型
    ///
    /// # 参数
    /// - `message_types`: 允许的消息类型集合
    pub fn with_allowed_message_types(mut self, message_types: HashSet<MessageType>) -> Self {
        self.allowed_message_types = Some(message_types);
        self
    }

    /// 启用性能监控
    pub fn with_performance_monitoring(mut self) -> Self {
        self.performance_monitoring = true;
        self
    }

    /// 设置最大消息大小
    ///
    /// # 参数
    /// - `size`: 最大消息大小（字节）
    pub fn with_max_message_size(mut self, size: usize) -> Self {
        self.max_message_size = size;
        self
    }

    /// 验证消息（详细结果）
    ///
    /// 对消息进行完整的协议验证并返回详细结果
    ///
    /// # 参数
    /// - `message`: 待验证的消息
    ///
    /// # 返回
    /// ValidationResult - 详细验证结果
    pub fn validate_message_detailed(&self, message: &NativeMessage) -> ValidationResult {
        let start_time = std::time::Instant::now();
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut suggestions = Vec::new();

        // 基础字段验证
        self.validate_basic_fields_detailed(message, &mut errors, &mut warnings, &mut suggestions);

        // 协议版本验证
        self.validate_version_detailed(
            message.version,
            &mut errors,
            &mut warnings,
            &mut suggestions,
        );

        // 消息类型验证
        self.validate_message_type_detailed(
            &message.message_type,
            &mut errors,
            &mut warnings,
            &mut suggestions,
        );

        // 严格模式验证
        if self.strict_mode {
            self.validate_strict_mode_detailed(
                message,
                &mut errors,
                &mut warnings,
                &mut suggestions,
            );
        }

        // 消息类型过滤验证
        if let Some(ref allowed_types) = self.allowed_message_types {
            if !allowed_types.contains(&message.message_type) {
                errors.push(ValidationError {
                    code: ErrorCode::OperationNotSupported,
                    field: "message_type".to_string(),
                    message: format!("消息类型 {:?} 不被允许", message.message_type),
                    severity: ValidationSeverity::Fatal,
                });
            }
        }

        // 兼容性验证
        self.validate_compatibility_detailed(message, &mut errors, &mut warnings, &mut suggestions);

        let validation_time = start_time.elapsed().as_micros() as u64;

        ValidationResult {
            is_valid: errors.is_empty(),
            errors,
            warnings,
            validation_time_micros: validation_time,
            suggestions,
        }
    }

    /// 验证消息（简单版本，与现有API兼容）
    ///
    /// 对消息进行完整的协议验证
    ///
    /// # 参数
    /// - `message`: 待验证的消息
    ///
    /// # 返回
    /// Result<()> - 验证成功或错误信息
    ///
    /// # 错误
    /// 当消息格式无效或协议版本不支持时返回错误
    pub fn validate_message(&self, message: &NativeMessage) -> Result<()> {
        let result = self.validate_message_detailed(message);

        if result.is_valid {
            if self.performance_monitoring && result.validation_time_micros > 1000 {
                tracing::warn!(
                    validation_time_micros = result.validation_time_micros,
                    "协议验证耗时过长"
                );
            }
            Ok(())
        } else {
            // 返回第一个致命错误
            let first_fatal_error = result
                .errors
                .iter()
                .find(|e| e.severity == ValidationSeverity::Fatal)
                .or_else(|| result.errors.first());

            if let Some(error) = first_fatal_error {
                Err(NativeMessagingError::ProtocolError(error.message.clone()))
            } else {
                Err(NativeMessagingError::ProtocolError(
                    "未知验证错误".to_string(),
                ))
            }
        }
    }

    /// 验证协议版本
    ///
    /// # 参数
    /// - `version`: 协议版本号
    ///
    /// # 返回
    /// Result<()> - 版本验证结果
    #[allow(dead_code)]
    fn validate_version(&self, version: u32) -> Result<()> {
        if !self.supported_versions.contains(&version) {
            return Err(NativeMessagingError::ProtocolError(format!(
                "不支持的协议版本: {}，支持的版本: {:?}",
                version, self.supported_versions
            )));
        }
        Ok(())
    }

    /// 详细版本验证
    fn validate_version_detailed(
        &self,
        version: u32,
        errors: &mut Vec<ValidationError>,
        warnings: &mut Vec<String>,
        suggestions: &mut Vec<String>,
    ) {
        if !self.supported_versions.contains(&version) {
            errors.push(ValidationError {
                code: ErrorCode::UnsupportedProtocolVersion,
                field: "version".to_string(),
                message: format!("不支持的协议版本: {}", version),
                severity: ValidationSeverity::Fatal,
            });
            suggestions.push(format!(
                "请使用支持的协议版本: {:?}",
                self.supported_versions
            ));
        } else if self.compatibility.is_version_deprecated(version) {
            warnings.push(format!(
                "协议版本 {} 已废弃，建议升级到版本 {}",
                version, self.compatibility.recommended_version
            ));
            suggestions.push(format!(
                "建议升级到推荐版本 {}",
                self.compatibility.recommended_version
            ));
        }
    }

    /// 验证基本字段
    ///
    /// # 参数
    /// - `message`: 待验证的消息
    ///
    /// # 返回
    /// Result<()> - 字段验证结果
    #[allow(dead_code)]
    fn validate_basic_fields(&self, message: &NativeMessage) -> Result<()> {
        if message.request_id.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "请求ID不能为空".to_string(),
            ));
        }

        if message.source.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "消息来源不能为空".to_string(),
            ));
        }

        if message.timestamp == 0 {
            return Err(NativeMessagingError::ProtocolError(
                "时间戳不能为零".to_string(),
            ));
        }

        // 验证请求ID格式
        if !self.is_valid_request_id(&message.request_id) {
            return Err(NativeMessagingError::ProtocolError(
                "请求ID格式无效".to_string(),
            ));
        }

        Ok(())
    }

    /// 详细基础字段验证
    fn validate_basic_fields_detailed(
        &self,
        message: &NativeMessage,
        errors: &mut Vec<ValidationError>,
        warnings: &mut Vec<String>,
        suggestions: &mut Vec<String>,
    ) {
        // 请求ID验证
        if message.request_id.is_empty() {
            errors.push(ValidationError {
                code: ErrorCode::InvalidRequestId,
                field: "request_id".to_string(),
                message: "请求ID不能为空".to_string(),
                severity: ValidationSeverity::Fatal,
            });
            suggestions.push("请提供有效的请求ID".to_string());
        } else if !self.is_valid_request_id(&message.request_id) {
            errors.push(ValidationError {
                code: ErrorCode::InvalidRequestId,
                field: "request_id".to_string(),
                message: "请求ID格式无效".to_string(),
                severity: ValidationSeverity::Fatal,
            });
            suggestions.push("请求ID应为8-64字符，仅包含字母数字、连字符和下划线".to_string());
        }

        // 来源验证
        if message.source.is_empty() {
            errors.push(ValidationError {
                code: ErrorCode::InvalidMessageFormat,
                field: "source".to_string(),
                message: "消息来源不能为空".to_string(),
                severity: ValidationSeverity::Fatal,
            });
            suggestions.push("请提供有效的消息来源标识".to_string());
        }

        // 时间戳验证
        if message.timestamp == 0 {
            errors.push(ValidationError {
                code: ErrorCode::InvalidMessageFormat,
                field: "timestamp".to_string(),
                message: "时间戳不能为零".to_string(),
                severity: ValidationSeverity::Fatal,
            });
            suggestions.push("请提供有效的Unix时间戳".to_string());
        }

        // 优先级验证
        if message.priority == MessagePriority::Critical && !message.message_type.is_request() {
            warnings.push("响应消息不应使用Critical优先级".to_string());
        }
    }

    /// 验证消息类型
    ///
    /// # 参数
    /// - `message_type`: 消息类型
    ///
    /// # 返回
    /// Result<()> - 消息类型验证结果
    #[allow(dead_code)]
    fn validate_message_type(&self, message_type: &MessageType) -> Result<()> {
        match message_type {
            MessageType::Custom(custom_type) => {
                if custom_type.is_empty() {
                    return Err(NativeMessagingError::ProtocolError(
                        "自定义消息类型不能为空".to_string(),
                    ));
                }

                if custom_type.len() > 100 {
                    return Err(NativeMessagingError::ProtocolError(
                        "自定义消息类型长度不能超过100字符".to_string(),
                    ));
                }
            }
            _ => {
                // 标准消息类型无需额外验证
            }
        }

        Ok(())
    }

    /// 详细消息类型验证
    fn validate_message_type_detailed(
        &self,
        message_type: &MessageType,
        errors: &mut Vec<ValidationError>,
        warnings: &mut Vec<String>,
        suggestions: &mut Vec<String>,
    ) {
        match message_type {
            MessageType::Custom(custom_type) => {
                if custom_type.is_empty() {
                    errors.push(ValidationError {
                        code: ErrorCode::InvalidMessageFormat,
                        field: "message_type".to_string(),
                        message: "自定义消息类型不能为空".to_string(),
                        severity: ValidationSeverity::Fatal,
                    });
                    suggestions.push("请为自定义消息类型提供有效名称".to_string());
                } else if custom_type.len() > 100 {
                    errors.push(ValidationError {
                        code: ErrorCode::InvalidMessageFormat,
                        field: "message_type".to_string(),
                        message: "自定义消息类型长度不能超过100字符".to_string(),
                        severity: ValidationSeverity::Error,
                    });
                    suggestions.push("请缩短自定义消息类型名称".to_string());
                } else if !custom_type.chars().all(|c| c.is_alphanumeric() || c == '_') {
                    warnings.push("自定义消息类型建议仅使用字母数字和下划线".to_string());
                }
            }
            MessageType::StreamData => {
                // 流式数据需要特殊验证
                warnings.push("流式数据消息需要额外的序列号和校验和验证".to_string());
            }
            MessageType::BatchOperation => {
                // 批量操作需要特殊验证
                warnings.push("批量操作消息需要验证子消息格式".to_string());
            }
            _ => {
                // 标准消息类型无需额外验证
            }
        }
    }

    /// 严格模式验证
    ///
    /// # 参数
    /// - `message`: 待验证的消息
    ///
    /// # 返回
    /// Result<()> - 严格验证结果
    #[allow(dead_code)]
    fn validate_strict_mode(&self, message: &NativeMessage) -> Result<()> {
        // 验证时间戳不能过于久远或未来
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        let time_diff = if current_time > message.timestamp {
            current_time - message.timestamp
        } else {
            message.timestamp - current_time
        };

        // 允许5分钟的时间偏差
        if time_diff > self.max_time_skew {
            return Err(NativeMessagingError::ProtocolError(
                "消息时间戳偏差过大".to_string(),
            ));
        }

        // 验证负载大小
        if let Ok(payload_str) = serde_json::to_string(&message.payload) {
            if payload_str.len() > self.max_message_size {
                return Err(NativeMessagingError::ProtocolError(format!(
                    "消息负载过大，超过{}字节限制",
                    self.max_message_size
                )));
            }
        }

        // 验证元数据
        if message.metadata.len() > 20 {
            return Err(NativeMessagingError::ProtocolError(
                "元数据项数量不能超过20个".to_string(),
            ));
        }

        Ok(())
    }

    /// 详细严格模式验证
    fn validate_strict_mode_detailed(
        &self,
        message: &NativeMessage,
        errors: &mut Vec<ValidationError>,
        warnings: &mut Vec<String>,
        suggestions: &mut Vec<String>,
    ) {
        // 时间戳验证
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        let time_diff = if current_time > message.timestamp {
            current_time - message.timestamp
        } else {
            message.timestamp - current_time
        };

        if time_diff > self.max_time_skew {
            errors.push(ValidationError {
                code: ErrorCode::MessageTimeout,
                field: "timestamp".to_string(),
                message: format!("消息时间戳偏差过大: {}秒", time_diff),
                severity: ValidationSeverity::Error,
            });
            suggestions.push("请确保客户端时间同步".to_string());
        } else if time_diff > self.max_time_skew / 2 {
            warnings.push(format!("消息时间戳偏差较大: {}秒", time_diff));
        }

        // 消息大小验证
        if let Ok(payload_str) = serde_json::to_string(&message.payload) {
            if payload_str.len() > self.max_message_size {
                errors.push(ValidationError {
                    code: ErrorCode::MessageTooLarge,
                    field: "payload".to_string(),
                    message: format!("消息负载过大: {} 字节", payload_str.len()),
                    severity: ValidationSeverity::Error,
                });
                suggestions.push("请减小消息负载大小或使用分块传输".to_string());
            } else if payload_str.len() > self.max_message_size / 2 {
                warnings.push(format!("消息负载较大: {} 字节", payload_str.len()));
            }
        }

        // 元数据验证
        if message.metadata.len() > 20 {
            errors.push(ValidationError {
                code: ErrorCode::InvalidMessageFormat,
                field: "metadata".to_string(),
                message: format!("元数据项过多: {} 项", message.metadata.len()),
                severity: ValidationSeverity::Error,
            });
            suggestions.push("请减少元数据项数量".to_string());
        }

        // 扩展字段验证
        if message.extensions.len() > 10 {
            warnings.push(format!("扩展字段较多: {} 项", message.extensions.len()));
        }

        // 超时设置验证
        if let Some(timeout_ms) = message.timeout_ms {
            if timeout_ms > 300000 {
                // 5分钟
                warnings.push("消息超时时间过长".to_string());
            } else if timeout_ms < 1000 {
                // 1秒
                warnings.push("消息超时时间过短".to_string());
            }
        }
    }

    /// 兼容性验证
    fn validate_compatibility_detailed(
        &self,
        message: &NativeMessage,
        errors: &mut Vec<ValidationError>,
        warnings: &mut Vec<String>,
        suggestions: &mut Vec<String>,
    ) {
        // 检查版本特性兼容性
        let _version_features = self.compatibility.get_version_features(message.version);

        // 检查新特性在旧版本中的使用
        if message.version < 2 {
            if message.priority != MessagePriority::Normal {
                warnings.push("协议版本1不支持消息优先级".to_string());
            }
            if message.timeout_ms.is_some() {
                warnings.push("协议版本1不支持超时设置".to_string());
            }
        }

        if message.version < 3
            && matches!(
                message.message_type,
                MessageType::BatchOperation | MessageType::StreamData
            )
        {
            errors.push(ValidationError {
                code: ErrorCode::UnsupportedProtocolVersion,
                field: "message_type".to_string(),
                message: format!(
                    "协议版本{}不支持消息类型 {:?}",
                    message.version, message.message_type
                ),
                severity: ValidationSeverity::Error,
            });
            suggestions.push("请升级到协议版本3以支持批量操作和流式数据".to_string());
        }

        // 检查向前兼容性
        if !message.extensions.is_empty() && message.version == 1 {
            warnings.push("协议版本1不完全支持扩展字段".to_string());
        }
    }

    /// 验证请求ID格式
    ///
    /// # 参数
    /// - `request_id`: 请求ID字符串
    ///
    /// # 返回
    /// bool - 请求ID格式是否有效
    fn is_valid_request_id(&self, request_id: &str) -> bool {
        // 请求ID应该是UUID格式或者符合特定规则
        if request_id.len() < 8 || request_id.len() > 64 {
            return false;
        }

        // 检查字符是否都是合法的
        request_id
            .chars()
            .all(|c| c.is_ascii_alphanumeric() || c == '-' || c == '_')
    }

    /// 批量验证消息
    ///
    /// # 参数
    /// - `messages`: 要验证的消息列表
    ///
    /// # 返回
    /// Vec<ValidationResult> - 每个消息的验证结果
    pub fn validate_messages_batch(&self, messages: &[NativeMessage]) -> Vec<ValidationResult> {
        messages
            .iter()
            .map(|msg| self.validate_message_detailed(msg))
            .collect()
    }

    /// 快速验证（跳过部分检查以提高性能）
    ///
    /// # 参数
    /// - `message`: 待验证的消息
    ///
    /// # 返回
    /// Result<()> - 快速验证结果
    pub fn validate_message_fast(&self, message: &NativeMessage) -> Result<()> {
        // 只进行关键验证
        if !self.supported_versions.contains(&message.version) {
            return Err(NativeMessagingError::ProtocolError(
                "不支持的协议版本".to_string(),
            ));
        }

        if message.request_id.is_empty() || message.source.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "缺少必填字段".to_string(),
            ));
        }

        // 跳过时间戳、负载大小等耗时检查
        Ok(())
    }

    /// 获取支持的协议版本列表
    ///
    /// # 返回
    /// &[u32] - 支持的版本列表
    pub fn supported_versions(&self) -> &[u32] {
        &self.supported_versions
    }

    /// 获取协议兼容性信息
    ///
    /// # 返回
    /// &ProtocolCompatibility - 兼容性信息
    pub fn compatibility_info(&self) -> &ProtocolCompatibility {
        &self.compatibility
    }

    /// 检查是否启用严格模式
    ///
    /// # 返回
    /// bool - 是否启用严格模式
    pub fn is_strict_mode(&self) -> bool {
        self.strict_mode
    }

    /// 获取验证器统计信息
    ///
    /// # 返回
    /// ValidatorStats - 统计信息
    pub fn get_stats(&self) -> ValidatorStats {
        ValidatorStats {
            supported_versions: self.supported_versions.clone(),
            strict_mode: self.strict_mode,
            max_message_size: self.max_message_size,
            max_time_skew: self.max_time_skew,
            allowed_message_types_count: self.allowed_message_types.as_ref().map(|s| s.len()),
            performance_monitoring: self.performance_monitoring,
        }
    }
}

/// 验证器统计信息
#[derive(Debug, Clone)]
pub struct ValidatorStats {
    /// 支持的协议版本
    pub supported_versions: Vec<u32>,
    /// 是否启用严格模式
    pub strict_mode: bool,
    /// 最大消息大小
    pub max_message_size: usize,
    /// 最大时间偏差
    pub max_time_skew: u64,
    /// 允许的消息类型数量
    pub allowed_message_types_count: Option<usize>,
    /// 是否启用性能监控
    pub performance_monitoring: bool,
}

impl Default for ProtocolValidator {
    fn default() -> Self {
        Self::new_default()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashSet;

    #[test]
    fn test_validator_creation() {
        let validator = ProtocolValidator::new(vec![1, 2], true);
        assert_eq!(validator.supported_versions(), &[1, 2]);
        assert!(validator.is_strict_mode());
    }

    #[test]
    fn test_valid_message() {
        let validator = ProtocolValidator::new_default();
        let message = NativeMessage::new(
            MessageType::GetCredentials,
            "test-123".to_string(),
            serde_json::json!({"domain": "example.com"}),
            "test_extension".to_string(),
        );

        assert!(validator.validate_message(&message).is_ok());
    }

    #[test]
    fn test_invalid_version() {
        let validator = ProtocolValidator::new(vec![1], true);
        let mut message = NativeMessage::new(
            MessageType::GetCredentials,
            "test-123".to_string(),
            serde_json::json!({}),
            "test_extension".to_string(),
        );
        message.version = 999; // 不支持的版本

        assert!(validator.validate_message(&message).is_err());
    }

    #[test]
    fn test_empty_request_id() {
        let validator = ProtocolValidator::new_default();
        let message = NativeMessage::new(
            MessageType::GetCredentials,
            "".to_string(), // 空请求ID
            serde_json::json!({}),
            "test_extension".to_string(),
        );

        assert!(validator.validate_message(&message).is_err());
    }

    #[test]
    fn test_invalid_request_id_format() {
        let validator = ProtocolValidator::new_default();
        let message = NativeMessage::new(
            MessageType::GetCredentials,
            "invalid!@#$%".to_string(), // 无效字符
            serde_json::json!({}),
            "test_extension".to_string(),
        );

        assert!(validator.validate_message(&message).is_err());
    }

    #[test]
    fn test_custom_message_type_validation() {
        let validator = ProtocolValidator::new_default();

        // 空自定义类型
        let empty_custom = NativeMessage::new(
            MessageType::Custom("".to_string()),
            "test-123".to_string(),
            serde_json::json!({}),
            "test_extension".to_string(),
        );
        assert!(validator.validate_message(&empty_custom).is_err());

        // 过长自定义类型
        let long_custom = NativeMessage::new(
            MessageType::Custom("x".repeat(101)),
            "test-123".to_string(),
            serde_json::json!({}),
            "test_extension".to_string(),
        );
        assert!(validator.validate_message(&long_custom).is_err());

        // 有效自定义类型
        let valid_custom = NativeMessage::new(
            MessageType::Custom("valid_type".to_string()),
            "test-123".to_string(),
            serde_json::json!({}),
            "test_extension".to_string(),
        );
        assert!(validator.validate_message(&valid_custom).is_ok());
    }

    #[test]
    fn test_strict_mode_disabled() {
        let validator = ProtocolValidator::new(vec![1, 2], false);
        let mut message = NativeMessage::new(
            MessageType::GetCredentials,
            "test-123".to_string(),
            serde_json::json!({}),
            "test_extension".to_string(),
        );

        // 设置过去很久的时间戳（在非严格模式下应该通过）
        message.timestamp = 1000000000; // 2001年的时间戳

        assert!(validator.validate_message(&message).is_ok());
    }

    #[test]
    fn test_detailed_validation() {
        let validator = ProtocolValidator::new_default();
        let message = NativeMessage::new(
            MessageType::GetCredentials,
            "test-123".to_string(),
            serde_json::json!({"domain": "example.com"}),
            "test_extension".to_string(),
        );

        let result = validator.validate_message_detailed(&message);
        assert!(result.is_valid);
        assert!(result.errors.is_empty());
        assert!(result.validation_time_micros > 0);
    }

    #[test]
    fn test_message_type_filtering() {
        let mut allowed_types = HashSet::new();
        allowed_types.insert(MessageType::HealthCheck);

        let validator = ProtocolValidator::new_default().with_allowed_message_types(allowed_types);

        // 允许的消息类型
        let allowed_message = NativeMessage::new(
            MessageType::HealthCheck,
            "test-123".to_string(),
            serde_json::json!({}),
            "test_extension".to_string(),
        );
        let result = validator.validate_message_detailed(&allowed_message);
        assert!(result.is_valid);

        // 不允许的消息类型
        let disallowed_message = NativeMessage::new(
            MessageType::GetCredentials,
            "test-456".to_string(),
            serde_json::json!({}),
            "test_extension".to_string(),
        );
        let result = validator.validate_message_detailed(&disallowed_message);
        assert!(!result.is_valid);
        assert!(!result.errors.is_empty());
    }

    #[test]
    fn test_performance_optimized_validator() {
        let validator = ProtocolValidator::new_performance_optimized();
        let stats = validator.get_stats();

        assert!(stats.performance_monitoring);
        assert!(!stats.strict_mode);
        assert_eq!(
            stats.supported_versions,
            vec![super::super::CURRENT_PROTOCOL_VERSION]
        );
    }

    #[test]
    fn test_fast_validation() {
        let validator = ProtocolValidator::new_default();
        let message = NativeMessage::new(
            MessageType::GetCredentials,
            "test-123".to_string(),
            serde_json::json!({"large": "data".repeat(1000)}), // 大负载
            "test_extension".to_string(),
        );

        // 快速验证应该通过（跳过大小检查）
        assert!(validator.validate_message_fast(&message).is_ok());
    }

    #[test]
    fn test_batch_validation() {
        let validator = ProtocolValidator::new_default();
        let messages = vec![
            NativeMessage::new(
                MessageType::GetCredentials,
                "test-msg-1".to_string(), // 至少8个字符
                serde_json::json!({}),
                "source1".to_string(),
            ),
            NativeMessage::new(
                MessageType::HealthCheck,
                "test-msg-2".to_string(), // 至少8个字符
                serde_json::json!({}),
                "source2".to_string(),
            ),
        ];

        let results = validator.validate_messages_batch(&messages);
        assert_eq!(results.len(), 2);
        assert!(results[0].is_valid);
        assert!(results[1].is_valid);
    }

    #[test]
    fn test_compatibility_validation() {
        let validator = ProtocolValidator::new_default();

        // V1消息使用V2特性
        let v1_message_with_priority = NativeMessage::new_with_version(
            1,
            MessageType::GetCredentials,
            "test-123".to_string(),
            serde_json::json!({}),
            "source".to_string(),
        )
        .with_priority(MessagePriority::High);

        let result = validator.validate_message_detailed(&v1_message_with_priority);
        assert!(result.is_valid); // 不是错误，只是警告
        assert!(!result.warnings.is_empty());
    }

    #[test]
    fn test_validation_rules_config() {
        let rules = ValidationRules {
            max_message_size: 1024,    // 1KB
            max_time_skew_seconds: 60, // 1分钟
            ..Default::default()
        };

        let validator = ProtocolValidator::new_with_rules(rules);
        let stats = validator.get_stats();

        assert_eq!(stats.max_message_size, 1024);
        assert_eq!(stats.max_time_skew, 60);
    }

    #[test]
    fn test_validation_error_details() {
        let validator = ProtocolValidator::new_default();
        let invalid_message = NativeMessage::new(
            MessageType::GetCredentials,
            "".to_string(), // 空请求ID
            serde_json::json!({}),
            "".to_string(), // 空来源
        );

        let result = validator.validate_message_detailed(&invalid_message);
        assert!(!result.is_valid);
        assert!(result.errors.len() >= 2); // 至少有请求ID和来源的错误
        assert!(!result.suggestions.is_empty());

        // 检查错误详情
        let request_id_error = result
            .errors
            .iter()
            .find(|e| e.field == "request_id")
            .expect("应该有请求ID错误");
        assert_eq!(request_id_error.code, ErrorCode::InvalidRequestId);
        assert_eq!(request_id_error.severity, ValidationSeverity::Fatal);
    }

    #[test]
    fn test_version_compatibility_detailed() {
        let validator = ProtocolValidator::new_default();

        // 测试V3特性在V2中的使用
        let v2_with_batch = NativeMessage::new_with_version(
            2,
            MessageType::BatchOperation, // V3特性
            "test-123".to_string(),
            serde_json::json!({}),
            "source".to_string(),
        );

        let result = validator.validate_message_detailed(&v2_with_batch);
        assert!(!result.is_valid);
        let version_error = result
            .errors
            .iter()
            .find(|e| e.code == ErrorCode::UnsupportedProtocolVersion)
            .expect("应该有版本不支持错误");
        assert!(version_error.message.contains("不支持消息类型"));
    }
}
