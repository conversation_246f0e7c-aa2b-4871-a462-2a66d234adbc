//! 审计日志模块
//!
//! 提供安全事件记录、日志分析和合规性审计功能

use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::MessageType,
    security::auth::AuthContext,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::fs::OpenOptions;
use tokio::io::AsyncWriteExt;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};
use uuid::Uuid;

/// 审计事件类型
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum AuditEventType {
    /// 认证事件
    Authentication,
    /// 授权事件
    Authorization,
    /// 数据访问
    DataAccess,
    /// 配置更改
    ConfigurationChange,
    /// 安全事件
    SecurityEvent,
    /// 系统事件
    SystemEvent,
    /// 错误事件
    ErrorEvent,
    /// 会话管理
    SessionManagement,
    /// 密钥管理
    KeyManagement,
}

impl AuditEventType {
    /// 获取事件类型的字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            AuditEventType::Authentication => "authentication",
            AuditEventType::Authorization => "authorization",
            AuditEventType::DataAccess => "data_access",
            AuditEventType::ConfigurationChange => "configuration_change",
            AuditEventType::SecurityEvent => "security_event",
            AuditEventType::SystemEvent => "system_event",
            AuditEventType::ErrorEvent => "error_event",
            AuditEventType::SessionManagement => "session_management",
            AuditEventType::KeyManagement => "key_management",
        }
    }

    /// 从字符串创建事件类型
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "authentication" => Some(AuditEventType::Authentication),
            "authorization" => Some(AuditEventType::Authorization),
            "data_access" => Some(AuditEventType::DataAccess),
            "configuration_change" => Some(AuditEventType::ConfigurationChange),
            "security_event" => Some(AuditEventType::SecurityEvent),
            "system_event" => Some(AuditEventType::SystemEvent),
            "error_event" => Some(AuditEventType::ErrorEvent),
            "session_management" => Some(AuditEventType::SessionManagement),
            "key_management" => Some(AuditEventType::KeyManagement),
            _ => None,
        }
    }
}

/// 审计事件严重级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum AuditSeverity {
    /// 信息级别
    Info = 0,
    /// 警告级别
    Warning = 1,
    /// 错误级别
    Error = 2,
    /// 严重错误级别
    Critical = 3,
}

impl AuditSeverity {
    /// 获取严重级别的字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            AuditSeverity::Info => "info",
            AuditSeverity::Warning => "warning",
            AuditSeverity::Error => "error",
            AuditSeverity::Critical => "critical",
        }
    }

    /// 从字符串创建严重级别
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "info" => Some(AuditSeverity::Info),
            "warning" => Some(AuditSeverity::Warning),
            "error" => Some(AuditSeverity::Error),
            "critical" => Some(AuditSeverity::Critical),
            _ => None,
        }
    }
}

/// 审计事件记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditEvent {
    /// 事件ID
    pub id: String,
    /// 事件类型
    pub event_type: AuditEventType,
    /// 严重级别
    pub severity: AuditSeverity,
    /// 事件时间戳
    pub timestamp: SystemTime,
    /// 用户/扩展ID
    pub actor: String,
    /// 会话ID（如果有）
    pub session_id: Option<String>,
    /// 操作描述
    pub action: String,
    /// 目标资源
    pub resource: Option<String>,
    /// 操作结果
    pub result: AuditResult,
    /// 源IP地址
    pub source_ip: Option<String>,
    /// 用户代理
    pub user_agent: Option<String>,
    /// 额外的事件数据
    pub metadata: HashMap<String, String>,
    /// 风险评分 (0-100)
    pub risk_score: u8,
}

impl AuditEvent {
    /// 创建新的审计事件
    pub fn new(
        event_type: AuditEventType,
        severity: AuditSeverity,
        actor: String,
        action: String,
    ) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            event_type,
            severity,
            timestamp: SystemTime::now(),
            actor,
            session_id: None,
            action,
            resource: None,
            result: AuditResult::Unknown,
            source_ip: None,
            user_agent: None,
            metadata: HashMap::new(),
            risk_score: 0,
        }
    }

    /// 设置会话ID
    pub fn with_session_id(mut self, session_id: String) -> Self {
        self.session_id = Some(session_id);
        self
    }

    /// 设置目标资源
    pub fn with_resource(mut self, resource: String) -> Self {
        self.resource = Some(resource);
        self
    }

    /// 设置操作结果
    pub fn with_result(mut self, result: AuditResult) -> Self {
        self.result = result;
        self
    }

    /// 设置源IP
    pub fn with_source_ip(mut self, source_ip: String) -> Self {
        self.source_ip = Some(source_ip);
        self
    }

    /// 设置用户代理
    pub fn with_user_agent(mut self, user_agent: String) -> Self {
        self.user_agent = Some(user_agent);
        self
    }

    /// 添加元数据
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }

    /// 设置风险评分
    pub fn with_risk_score(mut self, risk_score: u8) -> Self {
        self.risk_score = risk_score.min(100);
        self
    }

    /// 计算风险评分
    pub fn calculate_risk_score(&mut self) {
        let mut score = 0u8;

        // 基于事件类型的基础评分
        score += match self.event_type {
            AuditEventType::SecurityEvent => 30,
            AuditEventType::Authentication => 20,
            AuditEventType::Authorization => 15,
            AuditEventType::KeyManagement => 25,
            AuditEventType::ConfigurationChange => 20,
            AuditEventType::ErrorEvent => 10,
            _ => 5,
        };

        // 基于严重级别的评分
        score += match self.severity {
            AuditSeverity::Critical => 40,
            AuditSeverity::Error => 20,
            AuditSeverity::Warning => 10,
            AuditSeverity::Info => 0,
        };

        // 基于操作结果的评分
        score += match self.result {
            AuditResult::Failure => 20,
            AuditResult::PartialSuccess => 10,
            AuditResult::Success => 0,
            AuditResult::Unknown => 5,
        };

        // 基于特定关键词的评分
        let action_lower = self.action.to_lowercase();
        if action_lower.contains("failed") || action_lower.contains("error") {
            score += 15;
        }
        if action_lower.contains("unauthorized") || action_lower.contains("denied") {
            score += 20;
        }
        if action_lower.contains("attack") || action_lower.contains("malicious") {
            score += 30;
        }

        self.risk_score = score.min(100);
    }
}

/// 审计操作结果
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum AuditResult {
    /// 成功
    Success,
    /// 失败
    Failure,
    /// 部分成功
    PartialSuccess,
    /// 未知
    Unknown,
}

impl AuditResult {
    /// 获取结果的字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            AuditResult::Success => "success",
            AuditResult::Failure => "failure",
            AuditResult::PartialSuccess => "partial_success",
            AuditResult::Unknown => "unknown",
        }
    }
}

/// 审计配置
#[derive(Debug, Clone)]
pub struct AuditConfig {
    /// 是否启用审计日志
    pub enabled: bool,
    /// 日志文件路径
    pub log_file_path: Option<String>,
    /// 是否记录详细信息
    pub detailed_logging: bool,
    /// 日志保留时间（天）
    pub retention_days: u32,
    /// 最大日志文件大小（字节）
    pub max_file_size: u64,
    /// 是否启用实时分析
    pub enable_real_time_analysis: bool,
    /// 高风险事件阈值
    pub high_risk_threshold: u8,
}

impl Default for AuditConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_file_path: None,
            detailed_logging: true,
            retention_days: 90,
            max_file_size: 100 * 1024 * 1024, // 100MB
            enable_real_time_analysis: true,
            high_risk_threshold: 70,
        }
    }
}

/// 审计统计信息
#[derive(Debug)]
struct AuditStatistics {
    /// 总事件数
    total_events: u64,
    /// 按类型统计
    events_by_type: HashMap<AuditEventType, u64>,
    /// 按严重级别统计
    events_by_severity: HashMap<AuditSeverity, u64>,
    /// 高风险事件数
    high_risk_events: u64,
    /// 认证失败次数
    auth_failures: u64,
    /// 授权失败次数
    authz_failures: u64,
    /// 上次统计重置时间
    last_reset: SystemTime,
}

impl Default for AuditStatistics {
    fn default() -> Self {
        Self {
            total_events: 0,
            events_by_type: HashMap::new(),
            events_by_severity: HashMap::new(),
            high_risk_events: 0,
            auth_failures: 0,
            authz_failures: 0,
            last_reset: SystemTime::now(),
        }
    }
}

/// 审计日志管理器
pub struct AuditLogger {
    /// 配置
    config: AuditConfig,
    /// 事件缓冲区
    event_buffer: Arc<RwLock<Vec<AuditEvent>>>,
    /// 统计信息
    statistics: Arc<RwLock<AuditStatistics>>,
    /// 风险事件监听器
    risk_listeners: Arc<RwLock<Vec<Box<dyn Fn(&AuditEvent) + Send + Sync>>>>,
}

impl std::fmt::Debug for AuditLogger {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("AuditLogger")
            .field("config", &self.config)
            .field("event_buffer", &"<Arc<RwLock<Vec<AuditEvent>>>>")
            .field("statistics", &"<Arc<RwLock<AuditStatistics>>>")
            .field("risk_listeners", &"<Vec<Box<dyn Fn>>>")
            .finish()
    }
}

impl AuditLogger {
    /// 创建新的审计日志管理器
    pub fn new(config: AuditConfig) -> Self {
        Self {
            config,
            event_buffer: Arc::new(RwLock::new(Vec::new())),
            statistics: Arc::new(RwLock::new(AuditStatistics::default())),
            risk_listeners: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// 记录审计事件
    pub async fn log_event(&self, mut event: AuditEvent) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        // 计算风险评分
        event.calculate_risk_score();

        // 更新统计信息
        self.update_statistics(&event).await;

        // 检查高风险事件
        if event.risk_score >= self.config.high_risk_threshold {
            self.handle_high_risk_event(&event).await;
        }

        // 添加到缓冲区
        {
            let mut buffer = self.event_buffer.write().await;
            buffer.push(event.clone());
        }

        // 写入日志文件
        if let Some(ref log_path) = self.config.log_file_path {
            self.write_to_file(&event, log_path).await?;
        }

        debug!("记录审计事件: {} - {}", event.event_type.as_str(), event.action);
        Ok(())
    }

    /// 记录认证事件
    pub async fn log_authentication(
        &self,
        actor: String,
        action: String,
        result: AuditResult,
        context: Option<&AuthContext>,
    ) -> Result<()> {
        let severity = match result {
            AuditResult::Success => AuditSeverity::Info,
            AuditResult::Failure => AuditSeverity::Warning,
            AuditResult::PartialSuccess => AuditSeverity::Warning,
            AuditResult::Unknown => AuditSeverity::Warning,
        };

        let mut event = AuditEvent::new(
            AuditEventType::Authentication,
            severity,
            actor,
            action,
        ).with_result(result);

        if let Some(ctx) = context {
            event = event.with_session_id(ctx.session_id.clone());
            if let Some(ref ip) = ctx.source_ip {
                event = event.with_source_ip(ip.clone());
            }
            if let Some(ref ua) = ctx.user_agent {
                event = event.with_user_agent(ua.clone());
            }
        }

        self.log_event(event).await
    }

    /// 记录授权事件
    pub async fn log_authorization(
        &self,
        actor: String,
        action: String,
        resource: String,
        result: AuditResult,
        required_permission: Option<String>,
    ) -> Result<()> {
        let severity = match result {
            AuditResult::Success => AuditSeverity::Info,
            AuditResult::Failure => AuditSeverity::Error,
            AuditResult::PartialSuccess => AuditSeverity::Warning,
            AuditResult::Unknown => AuditSeverity::Warning,
        };

        let mut event = AuditEvent::new(
            AuditEventType::Authorization,
            severity,
            actor,
            action,
        )
        .with_result(result)
        .with_resource(resource);

        if let Some(perm) = required_permission {
            event = event.with_metadata("required_permission".to_string(), perm);
        }

        self.log_event(event).await
    }

    /// 记录数据访问事件
    pub async fn log_data_access(
        &self,
        actor: String,
        action: String,
        resource: String,
        message_type: Option<MessageType>,
    ) -> Result<()> {
        let mut event = AuditEvent::new(
            AuditEventType::DataAccess,
            AuditSeverity::Info,
            actor,
            action,
        )
        .with_result(AuditResult::Success)
        .with_resource(resource);

        if let Some(msg_type) = message_type {
            event = event.with_metadata("message_type".to_string(), msg_type.as_string());
        }

        self.log_event(event).await
    }

    /// 记录安全事件
    pub async fn log_security_event(
        &self,
        actor: String,
        action: String,
        severity: AuditSeverity,
        details: HashMap<String, String>,
    ) -> Result<()> {
        let mut event = AuditEvent::new(
            AuditEventType::SecurityEvent,
            severity,
            actor,
            action,
        )
        .with_result(AuditResult::Failure);

        for (key, value) in details {
            event = event.with_metadata(key, value);
        }

        self.log_event(event).await
    }

    /// 记录错误事件
    pub async fn log_error(
        &self,
        actor: String,
        error_message: String,
        error_type: String,
    ) -> Result<()> {
        let event = AuditEvent::new(
            AuditEventType::ErrorEvent,
            AuditSeverity::Error,
            actor,
            error_message,
        )
        .with_result(AuditResult::Failure)
        .with_metadata("error_type".to_string(), error_type);

        self.log_event(event).await
    }

    /// 获取审计统计信息
    pub async fn get_statistics(&self) -> AuditStatisticsInfo {
        let stats = self.statistics.read().await;
        let buffer = self.event_buffer.read().await;

        AuditStatisticsInfo {
            total_events: stats.total_events,
            events_by_type: stats.events_by_type.clone(),
            events_by_severity: stats.events_by_severity.clone(),
            high_risk_events: stats.high_risk_events,
            auth_failures: stats.auth_failures,
            authz_failures: stats.authz_failures,
            buffered_events: buffer.len(),
            last_reset: stats.last_reset,
        }
    }

    /// 查询审计事件
    pub async fn query_events(&self, filter: AuditEventFilter) -> Vec<AuditEvent> {
        let buffer = self.event_buffer.read().await;
        buffer
            .iter()
            .filter(|event| filter.matches(event))
            .cloned()
            .collect()
    }

    /// 清理旧事件
    pub async fn cleanup_old_events(&self) -> usize {
        let retention_duration = Duration::from_secs(self.config.retention_days as u64 * 24 * 3600);
        let cutoff_time = SystemTime::now() - retention_duration;

        let mut buffer = self.event_buffer.write().await;
        let initial_count = buffer.len();
        
        buffer.retain(|event| event.timestamp > cutoff_time);
        
        let removed_count = initial_count - buffer.len();
        if removed_count > 0 {
            info!("清理了 {} 个过期审计事件", removed_count);
        }
        
        removed_count
    }

    /// 添加风险事件监听器
    pub async fn add_risk_listener<F>(&self, listener: F)
    where
        F: Fn(&AuditEvent) + Send + Sync + 'static,
    {
        let mut listeners = self.risk_listeners.write().await;
        listeners.push(Box::new(listener));
    }

    /// 处理高风险事件
    async fn handle_high_risk_event(&self, event: &AuditEvent) {
        warn!(
            "检测到高风险事件: {} (风险评分: {})",
            event.action,
            event.risk_score
        );

        // 通知所有监听器
        let listeners = self.risk_listeners.read().await;
        for listener in listeners.iter() {
            listener(event);
        }
    }

    /// 更新统计信息
    async fn update_statistics(&self, event: &AuditEvent) {
        let mut stats = self.statistics.write().await;
        
        stats.total_events += 1;
        
        *stats.events_by_type.entry(event.event_type).or_insert(0) += 1;
        *stats.events_by_severity.entry(event.severity).or_insert(0) += 1;
        
        if event.risk_score >= self.config.high_risk_threshold {
            stats.high_risk_events += 1;
        }
        
        if event.event_type == AuditEventType::Authentication && event.result == AuditResult::Failure {
            stats.auth_failures += 1;
        }
        
        if event.event_type == AuditEventType::Authorization && event.result == AuditResult::Failure {
            stats.authz_failures += 1;
        }
    }

    /// 写入文件
    async fn write_to_file(&self, event: &AuditEvent, log_path: &str) -> Result<()> {
        let log_entry = serde_json::to_string(event)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;

        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(log_path)
            .await
            .map_err(|e| NativeMessagingError::IoError(e))?;

        file.write_all(log_entry.as_bytes()).await
            .map_err(|e| NativeMessagingError::IoError(e))?;
        file.write_all(b"\n").await
            .map_err(|e| NativeMessagingError::IoError(e))?;

        Ok(())
    }

    /// 重置统计信息
    pub async fn reset_statistics(&self) {
        let mut stats = self.statistics.write().await;
        *stats = AuditStatistics::default();
        info!("审计统计信息已重置");
    }
}

/// 审计事件过滤器
#[derive(Debug, Clone)]
pub struct AuditEventFilter {
    /// 事件类型过滤
    pub event_types: Option<Vec<AuditEventType>>,
    /// 严重级别过滤
    pub severities: Option<Vec<AuditSeverity>>,
    /// 参与者过滤
    pub actors: Option<Vec<String>>,
    /// 时间范围过滤
    pub time_range: Option<(SystemTime, SystemTime)>,
    /// 风险评分范围
    pub risk_score_range: Option<(u8, u8)>,
    /// 结果过滤
    pub results: Option<Vec<AuditResult>>,
}

impl AuditEventFilter {
    /// 创建空过滤器
    pub fn new() -> Self {
        Self {
            event_types: None,
            severities: None,
            actors: None,
            time_range: None,
            risk_score_range: None,
            results: None,
        }
    }

    /// 检查事件是否匹配过滤器
    pub fn matches(&self, event: &AuditEvent) -> bool {
        if let Some(ref types) = self.event_types {
            if !types.contains(&event.event_type) {
                return false;
            }
        }

        if let Some(ref severities) = self.severities {
            if !severities.contains(&event.severity) {
                return false;
            }
        }

        if let Some(ref actors) = self.actors {
            if !actors.contains(&event.actor) {
                return false;
            }
        }

        if let Some((start, end)) = self.time_range {
            if event.timestamp < start || event.timestamp > end {
                return false;
            }
        }

        if let Some((min_score, max_score)) = self.risk_score_range {
            if event.risk_score < min_score || event.risk_score > max_score {
                return false;
            }
        }

        if let Some(ref results) = self.results {
            if !results.contains(&event.result) {
                return false;
            }
        }

        true
    }
}

impl Default for AuditEventFilter {
    fn default() -> Self {
        Self::new()
    }
}

/// 审计统计信息（外部接口）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditStatisticsInfo {
    /// 总事件数
    pub total_events: u64,
    /// 按类型统计
    pub events_by_type: HashMap<AuditEventType, u64>,
    /// 按严重级别统计
    pub events_by_severity: HashMap<AuditSeverity, u64>,
    /// 高风险事件数
    pub high_risk_events: u64,
    /// 认证失败次数
    pub auth_failures: u64,
    /// 授权失败次数
    pub authz_failures: u64,
    /// 缓冲区中的事件数
    pub buffered_events: usize,
    /// 上次重置时间
    pub last_reset: SystemTime,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_audit_event_creation() {
        let event = AuditEvent::new(
            AuditEventType::Authentication,
            AuditSeverity::Info,
            "test-user".to_string(),
            "login".to_string(),
        );

        assert_eq!(event.event_type, AuditEventType::Authentication);
        assert_eq!(event.severity, AuditSeverity::Info);
        assert_eq!(event.actor, "test-user");
        assert_eq!(event.action, "login");
        assert!(!event.id.is_empty());
    }

    #[test]
    fn test_audit_event_builder() {
        let event = AuditEvent::new(
            AuditEventType::DataAccess,
            AuditSeverity::Info,
            "test-user".to_string(),
            "read_credentials".to_string(),
        )
        .with_session_id("session-123".to_string())
        .with_resource("credentials".to_string())
        .with_result(AuditResult::Success);

        assert_eq!(event.session_id, Some("session-123".to_string()));
        assert_eq!(event.resource, Some("credentials".to_string()));
        assert_eq!(event.result, AuditResult::Success);
    }

    #[test]
    fn test_risk_score_calculation() {
        let mut event = AuditEvent::new(
            AuditEventType::SecurityEvent,
            AuditSeverity::Critical,
            "malicious-actor".to_string(),
            "unauthorized access failed".to_string(),
        ).with_result(AuditResult::Failure);

        event.calculate_risk_score();
        
        // 应该有较高的风险评分
        assert!(event.risk_score > 50);
    }

    #[tokio::test]
    async fn test_audit_logger_creation() {
        let config = AuditConfig::default();
        let logger = AuditLogger::new(config);
        
        let stats = logger.get_statistics().await;
        assert_eq!(stats.total_events, 0);
    }

    #[tokio::test]
    async fn test_audit_logger_log_event() {
        let config = AuditConfig::default();
        let logger = AuditLogger::new(config);
        
        let event = AuditEvent::new(
            AuditEventType::Authentication,
            AuditSeverity::Info,
            "test-user".to_string(),
            "login".to_string(),
        );

        let result = logger.log_event(event).await;
        assert!(result.is_ok());

        let stats = logger.get_statistics().await;
        assert_eq!(stats.total_events, 1);
        assert_eq!(stats.buffered_events, 1);
    }

    #[tokio::test]
    async fn test_audit_logger_disabled() {
        let mut config = AuditConfig::default();
        config.enabled = false;
        let logger = AuditLogger::new(config);
        
        let event = AuditEvent::new(
            AuditEventType::Authentication,
            AuditSeverity::Info,
            "test-user".to_string(),
            "login".to_string(),
        );

        let result = logger.log_event(event).await;
        assert!(result.is_ok());

        let stats = logger.get_statistics().await;
        assert_eq!(stats.total_events, 0); // 应该没有记录
    }

    #[tokio::test]
    async fn test_event_filtering() {
        let config = AuditConfig::default();
        let logger = AuditLogger::new(config);
        
        // 创建不同类型的事件
        let auth_event = AuditEvent::new(
            AuditEventType::Authentication,
            AuditSeverity::Info,
            "user1".to_string(),
            "login".to_string(),
        );
        
        let data_event = AuditEvent::new(
            AuditEventType::DataAccess,
            AuditSeverity::Info,
            "user2".to_string(),
            "read".to_string(),
        );

        logger.log_event(auth_event).await.unwrap();
        logger.log_event(data_event).await.unwrap();

        // 过滤认证事件
        let filter = AuditEventFilter {
            event_types: Some(vec![AuditEventType::Authentication]),
            ..Default::default()
        };

        let filtered_events = logger.query_events(filter).await;
        assert_eq!(filtered_events.len(), 1);
        assert_eq!(filtered_events[0].event_type, AuditEventType::Authentication);
    }

    #[tokio::test]
    async fn test_cleanup_old_events() {
        let mut config = AuditConfig::default();
        config.retention_days = 0; // 立即过期
        let logger = AuditLogger::new(config);
        
        let event = AuditEvent::new(
            AuditEventType::Authentication,
            AuditSeverity::Info,
            "test-user".to_string(),
            "login".to_string(),
        );

        logger.log_event(event).await.unwrap();
        
        // 等待事件过期
        tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
        
        let cleaned = logger.cleanup_old_events().await;
        assert_eq!(cleaned, 1);
        
        let stats = logger.get_statistics().await;
        assert_eq!(stats.buffered_events, 0);
    }

    #[test]
    fn test_event_type_string_conversion() {
        assert_eq!(AuditEventType::Authentication.as_str(), "authentication");
        assert_eq!(AuditEventType::from_str("authentication"), Some(AuditEventType::Authentication));
        assert_eq!(AuditEventType::from_str("invalid"), None);
    }

    #[test]
    fn test_severity_ordering() {
        assert!(AuditSeverity::Critical > AuditSeverity::Error);
        assert!(AuditSeverity::Error > AuditSeverity::Warning);
        assert!(AuditSeverity::Warning > AuditSeverity::Info);
    }
} 