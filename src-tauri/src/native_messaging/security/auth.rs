//! 认证授权模块
//! 
//! 提供扩展身份验证、权限控制和会话管理功能

use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::NativeMessage,
    config::SecurityConfig,
};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use tracing::{debug, info};
use uuid::Uuid;

/// 认证上下文
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct AuthContext {
    /// 扩展ID
    pub extension_id: String,
    /// 权限列表
    pub permissions: Vec<String>,
    /// 会话ID
    pub session_id: String,
    /// 认证时间
    pub auth_time: SystemTime,
    /// 最后活跃时间
    pub last_active: SystemTime,
    /// 用户代理信息
    pub user_agent: Option<String>,
    /// 请求来源IP
    pub source_ip: Option<String>,
}

impl AuthContext {
    /// 创建新的认证上下文
    pub fn new(extension_id: String, permissions: Vec<String>) -> Self {
        let now = SystemTime::now();
        Self {
            extension_id,
            permissions,
            session_id: Uuid::new_v4().to_string(),
            auth_time: now,
            last_active: now,
            user_agent: None,
            source_ip: None,
        }
    }

    /// 更新最后活跃时间
    pub fn update_activity(&mut self) {
        self.last_active = SystemTime::now();
    }

    /// 检查权限
    pub fn has_permission(&self, permission: &str) -> bool {
        self.permissions.contains(&permission.to_string())
    }

    /// 检查会话是否过期
    pub fn is_expired(&self, timeout: Duration) -> bool {
        if let Ok(elapsed) = self.last_active.elapsed() {
            elapsed > timeout
        } else {
            true // 时间异常时认为已过期
        }
    }

    /// 添加额外信息
    pub fn with_user_agent(mut self, user_agent: String) -> Self {
        self.user_agent = Some(user_agent);
        self
    }

    /// 添加来源IP
    pub fn with_source_ip(mut self, source_ip: String) -> Self {
        self.source_ip = Some(source_ip);
        self
    }
}

/// 权限类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Permission {
    /// 读取凭证
    ReadCredentials,
    /// 写入凭证
    WriteCredentials,
    /// 删除凭证
    DeleteCredentials,
    /// 修改配置
    ModifyConfig,
    /// 访问系统信息
    SystemAccess,
    /// 管理员权限
    AdminAccess,
    /// 自定义权限
    Custom(String),
}

impl Permission {
    /// 转换为字符串
    pub fn as_str(&self) -> &str {
        match self {
            Permission::ReadCredentials => "read_credentials",
            Permission::WriteCredentials => "write_credentials",
            Permission::DeleteCredentials => "delete_credentials",
            Permission::ModifyConfig => "modify_config",
            Permission::SystemAccess => "system_access",
            Permission::AdminAccess => "admin_access",
            Permission::Custom(name) => name,
        }
    }

    /// 从字符串创建权限
    pub fn from_str(s: &str) -> Self {
        match s {
            "read_credentials" => Permission::ReadCredentials,
            "write_credentials" => Permission::WriteCredentials,
            "delete_credentials" => Permission::DeleteCredentials,
            "modify_config" => Permission::ModifyConfig,
            "system_access" => Permission::SystemAccess,
            "admin_access" => Permission::AdminAccess,
            custom => Permission::Custom(custom.to_string()),
        }
    }
}

/// 会话管理器
#[derive(Debug)]
pub struct SessionManager {
    /// 活跃会话映射
    sessions: Arc<RwLock<HashMap<String, AuthContext>>>,
    /// 会话超时时间
    session_timeout: Duration,
    /// 最大会话数
    max_sessions: usize,
}

impl SessionManager {
    /// 创建新的会话管理器
    pub fn new(session_timeout: Duration, max_sessions: usize) -> Self {
        Self {
            sessions: Arc::new(RwLock::new(HashMap::new())),
            session_timeout,
            max_sessions,
        }
    }

    /// 创建新会话
    pub async fn create_session(&self, extension_id: String, permissions: Vec<String>) -> Result<String> {
        let mut sessions = self.sessions.write().await;
        
        // 检查会话数限制
        if sessions.len() >= self.max_sessions {
            // 清理过期会话
            self.cleanup_expired_sessions_internal(&mut sessions).await;
            
            // 如果清理后仍然超过限制，拒绝创建新会话
            if sessions.len() >= self.max_sessions {
                return Err(NativeMessagingError::SecurityError(
                    "超过最大会话数限制".to_string()
                ));
            }
        }

        let context = AuthContext::new(extension_id, permissions);
        let session_id = context.session_id.clone();
        
        sessions.insert(session_id.clone(), context);
        info!("创建新会话: {}", session_id);
        Ok(session_id)
    }

    /// 获取会话
    pub async fn get_session(&self, session_id: &str) -> Option<AuthContext> {
        let sessions = self.sessions.read().await;
        sessions.get(session_id).cloned()
    }

    /// 更新会话活跃时间
    pub async fn update_session_activity(&self, session_id: &str) -> Result<()> {
        let mut sessions = self.sessions.write().await;
        if let Some(context) = sessions.get_mut(session_id) {
            context.update_activity();
            debug!("更新会话活跃时间: {}", session_id);
            Ok(())
        } else {
            Err(NativeMessagingError::SecurityError(format!(
                "会话不存在: {}",
                session_id
            )))
        }
    }

    /// 删除会话
    pub async fn remove_session(&self, session_id: &str) -> Result<()> {
        let mut sessions = self.sessions.write().await;
        if sessions.remove(session_id).is_some() {
            info!("删除会话: {}", session_id);
            Ok(())
        } else {
            Err(NativeMessagingError::SecurityError(format!(
                "会话不存在: {}",
                session_id
            )))
        }
    }

    /// 清理过期会话
    pub async fn cleanup_expired_sessions(&self) -> usize {
        let mut sessions = self.sessions.write().await;
        self.cleanup_expired_sessions_internal(&mut sessions).await
    }

    /// 内部清理过期会话实现
    async fn cleanup_expired_sessions_internal(&self, sessions: &mut HashMap<String, AuthContext>) -> usize {
        let expired_sessions: Vec<String> = sessions
            .iter()
            .filter(|(_, context)| context.is_expired(self.session_timeout))
            .map(|(id, _)| id.clone())
            .collect();

        let count = expired_sessions.len();
        for session_id in expired_sessions {
            sessions.remove(&session_id);
            debug!("清理过期会话: {}", session_id);
        }

        if count > 0 {
            info!("清理了 {} 个过期会话", count);
        }
        count
    }

    /// 获取活跃会话数
    pub async fn active_session_count(&self) -> usize {
        let sessions = self.sessions.read().await;
        sessions.len()
    }

    /// 验证会话并返回上下文
    pub async fn validate_session(&self, session_id: &str) -> Result<AuthContext> {
        let sessions = self.sessions.read().await;
        if let Some(context) = sessions.get(session_id) {
            if context.is_expired(self.session_timeout) {
                drop(sessions);
                self.remove_session(session_id).await?;
                Err(NativeMessagingError::SecurityError(
                    "会话已过期".to_string()
                ))
            } else {
                Ok(context.clone())
            }
        } else {
            Err(NativeMessagingError::SecurityError(
                "无效的会话ID".to_string()
            ))
        }
    }
}

/// 认证管理器
#[derive(Debug)]
pub struct AuthManager {
    /// 授权的扩展ID集合
    authorized_extensions: HashSet<String>,
    /// 扩展权限映射
    extension_permissions: HashMap<String, Vec<Permission>>,
    /// 会话管理器
    session_manager: SessionManager,
    /// 是否启用认证
    authentication_enabled: bool,
    /// 认证统计
    auth_stats: Arc<RwLock<AuthStats>>,
}

/// 认证统计信息
#[derive(Debug)]
struct AuthStats {
    /// 总认证尝试次数
    total_attempts: u64,
    /// 成功认证次数
    successful_auths: u64,
    /// 失败认证次数
    failed_auths: u64,
    /// 被拒绝的请求次数
    rejected_requests: u64,
    /// 上次重置时间
    last_reset: Instant,
}

impl Default for AuthStats {
    fn default() -> Self {
        Self {
            total_attempts: 0,
            successful_auths: 0,
            failed_auths: 0,
            rejected_requests: 0,
            last_reset: Instant::now(),
        }
    }
}

impl AuthManager {
    /// 创建新的认证管理器
    pub fn new(config: &SecurityConfig) -> Self {
        let session_manager = SessionManager::new(
            config.session_timeout,
            100, // 默认最大会话数
        );

        Self {
            authorized_extensions: config.authorized_extensions.iter().cloned().collect(),
            extension_permissions: HashMap::new(),
            session_manager,
            authentication_enabled: config.enable_authentication,
            auth_stats: Arc::new(RwLock::new(AuthStats::default())),
        }
    }

    /// 添加授权扩展
    pub fn add_authorized_extension(&mut self, extension_id: String, permissions: Vec<Permission>) {
        self.authorized_extensions.insert(extension_id.clone());
        self.extension_permissions.insert(extension_id, permissions);
    }

    /// 移除授权扩展
    pub fn remove_authorized_extension(&mut self, extension_id: &str) {
        self.authorized_extensions.remove(extension_id);
        self.extension_permissions.remove(extension_id);
    }

    /// 认证扩展消息
    pub async fn authenticate(&self, message: &NativeMessage) -> Result<AuthContext> {
        let mut stats = self.auth_stats.write().await;
        stats.total_attempts += 1;
        drop(stats);

        // 如果未启用认证，创建默认上下文
        if !self.authentication_enabled {
            let context = AuthContext::new(
                message.source.clone(),
                vec!["read_credentials".to_string(), "write_credentials".to_string()],
            );
            self.update_auth_stats(true).await;
            return Ok(context);
        }

        // 验证扩展身份
        if !self.authorized_extensions.contains(&message.source) {
            self.update_auth_stats(false).await;
            return Err(NativeMessagingError::SecurityError(
                format!("未授权的扩展: {}", message.source)
            ));
        }

        // 获取扩展权限
        let permissions = self.extension_permissions
            .get(&message.source)
            .cloned()
            .unwrap_or_default()
            .iter()
            .map(|p| p.as_str().to_string())
            .collect();

        // 创建认证上下文
        let context = AuthContext::new(message.source.clone(), permissions);
        
        self.update_auth_stats(true).await;
        debug!("扩展认证成功: {}", message.source);
        Ok(context)
    }

    /// 验证权限
    pub async fn check_permission(&self, context: &AuthContext, required_permission: &str) -> Result<()> {
        if !context.has_permission(required_permission) {
            let mut stats = self.auth_stats.write().await;
            stats.rejected_requests += 1;
            drop(stats);

            return Err(NativeMessagingError::SecurityError(
                format!("权限不足，需要权限: {}", required_permission)
            ));
        }
        Ok(())
    }

    /// 创建会话
    pub async fn create_session(&self, extension_id: String) -> Result<String> {
        let permissions = self.extension_permissions
            .get(&extension_id)
            .cloned()
            .unwrap_or_default()
            .iter()
            .map(|p| p.as_str().to_string())
            .collect();

        self.session_manager.create_session(extension_id, permissions).await
    }

    /// 验证会话
    pub async fn validate_session(&self, session_id: &str) -> Result<AuthContext> {
        self.session_manager.validate_session(session_id).await
    }

    /// 更新会话活跃时间
    pub async fn update_session_activity(&self, session_id: &str) -> Result<()> {
        self.session_manager.update_session_activity(session_id).await
    }

    /// 删除会话
    pub async fn remove_session(&self, session_id: &str) -> Result<()> {
        self.session_manager.remove_session(session_id).await
    }

    /// 清理过期会话
    pub async fn cleanup_expired_sessions(&self) -> usize {
        self.session_manager.cleanup_expired_sessions().await
    }

    /// 获取认证统计信息
    pub async fn get_auth_statistics(&self) -> AuthStatistics {
        let stats = self.auth_stats.read().await;
        AuthStatistics {
            total_attempts: stats.total_attempts,
            successful_auths: stats.successful_auths,
            failed_auths: stats.failed_auths,
            rejected_requests: stats.rejected_requests,
            success_rate: if stats.total_attempts > 0 {
                stats.successful_auths as f64 / stats.total_attempts as f64
            } else {
                0.0
            },
            active_sessions: self.session_manager.active_session_count().await,
        }
    }

    /// 更新认证统计
    async fn update_auth_stats(&self, success: bool) {
        let mut stats = self.auth_stats.write().await;
        if success {
            stats.successful_auths += 1;
        } else {
            stats.failed_auths += 1;
        }
    }

    /// 重置统计信息
    pub async fn reset_statistics(&self) {
        let mut stats = self.auth_stats.write().await;
        *stats = AuthStats::default();
        info!("认证统计信息已重置");
    }
}

/// 认证统计信息（外部接口）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthStatistics {
    /// 总认证尝试次数
    pub total_attempts: u64,
    /// 成功认证次数
    pub successful_auths: u64,
    /// 失败认证次数
    pub failed_auths: u64,
    /// 被拒绝的请求次数
    pub rejected_requests: u64,
    /// 成功率 (0.0-1.0)
    pub success_rate: f64,
    /// 活跃会话数
    pub active_sessions: usize,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::config::SecurityConfig;
    use tokio::time::{sleep, Duration as TokioDuration};

    #[test]
    fn test_auth_context_creation() {
        let permissions = vec!["read".to_string(), "write".to_string()];
        let context = AuthContext::new("test-extension".to_string(), permissions.clone());

        assert_eq!(context.extension_id, "test-extension");
        assert_eq!(context.permissions, permissions);
        assert!(!context.session_id.is_empty());
    }

    #[test]
    fn test_auth_context_permissions() {
        let context = AuthContext::new(
            "test".to_string(),
            vec!["read".to_string(), "write".to_string()],
        );

        assert!(context.has_permission("read"));
        assert!(context.has_permission("write"));
        assert!(!context.has_permission("admin"));
    }

    #[test]
    fn test_permission_string_conversion() {
        assert_eq!(Permission::ReadCredentials.as_str(), "read_credentials");
        assert_eq!(Permission::from_str("read_credentials"), Permission::ReadCredentials);
        assert_eq!(Permission::from_str("custom_perm"), Permission::Custom("custom_perm".to_string()));
    }

    #[tokio::test]
    async fn test_session_manager_create() {
        let manager = SessionManager::new(Duration::from_secs(3600), 10);
        let session_id = manager.create_session(
            "test-ext".to_string(),
            vec!["read".to_string()],
        ).await.unwrap();

        assert!(!session_id.is_empty());
        assert_eq!(manager.active_session_count().await, 1);
    }

    #[tokio::test]
    async fn test_session_manager_validate() {
        let manager = SessionManager::new(Duration::from_secs(3600), 10);
        let session_id = manager.create_session(
            "test-ext".to_string(),
            vec!["read".to_string()],
        ).await.unwrap();

        let context = manager.validate_session(&session_id).await.unwrap();
        assert_eq!(context.extension_id, "test-ext");
    }

    #[tokio::test]
    async fn test_session_expiration() {
        let manager = SessionManager::new(Duration::from_millis(100), 10);
        let session_id = manager.create_session(
            "test-ext".to_string(),
            vec!["read".to_string()],
        ).await.unwrap();

        // 等待过期
        sleep(TokioDuration::from_millis(200)).await;

        // 验证会话应该失败
        assert!(manager.validate_session(&session_id).await.is_err());
    }

    #[tokio::test]
    async fn test_auth_manager_creation() {
        let config = SecurityConfig::default();
        let manager = AuthManager::new(&config);
        
        assert_eq!(manager.authentication_enabled, config.enable_authentication);
    }

    #[tokio::test]
    async fn test_auth_manager_authenticate() {
        let mut config = SecurityConfig::default();
        config.enable_authentication = false;

        let manager = AuthManager::new(&config);
        
        let message = NativeMessage {
            version: 1,
            message_type: crate::native_messaging::protocol::message::MessageType::GetCredentials,
            request_id: "test".to_string(),
            payload: serde_json::json!({}),
            timestamp: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
            source: "test-extension".to_string(),
            metadata: HashMap::new(),
            extensions: HashMap::new(),
            priority: crate::native_messaging::protocol::message::MessagePriority::Normal,
            timeout_ms: None,
        };

        let result = manager.authenticate(&message).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_auth_manager_unauthorized_extension() {
        let mut config = SecurityConfig::default();
        config.enable_authentication = true;
        config.authorized_extensions = vec!["authorized-ext".to_string()];

        let manager = AuthManager::new(&config);
        
        let message = NativeMessage {
            version: 1,
            message_type: crate::native_messaging::protocol::message::MessageType::GetCredentials,
            request_id: "test".to_string(),
            payload: serde_json::json!({}),
            timestamp: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
            source: "unauthorized-ext".to_string(),
            metadata: HashMap::new(),
            extensions: HashMap::new(),
            priority: crate::native_messaging::protocol::message::MessagePriority::Normal,
            timeout_ms: None,
        };

        let result = manager.authenticate(&message).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_auth_statistics() {
        let config = SecurityConfig::default();
        let manager = AuthManager::new(&config);

        let stats = manager.get_auth_statistics().await;
        assert_eq!(stats.total_attempts, 0);
        assert_eq!(stats.successful_auths, 0);
        assert_eq!(stats.failed_auths, 0);
    }

    #[tokio::test]
    async fn test_session_cleanup() {
        let manager = SessionManager::new(Duration::from_millis(50), 10);
        
        // 创建一些会话
        for i in 0..3 {
            manager.create_session(
                format!("ext-{}", i),
                vec!["read".to_string()],
            ).await.unwrap();
        }

        assert_eq!(manager.active_session_count().await, 3);

        // 等待过期
        sleep(TokioDuration::from_millis(100)).await;

        // 清理过期会话
        let cleaned = manager.cleanup_expired_sessions().await;
        assert_eq!(cleaned, 3);
        assert_eq!(manager.active_session_count().await, 0);
    }
} 