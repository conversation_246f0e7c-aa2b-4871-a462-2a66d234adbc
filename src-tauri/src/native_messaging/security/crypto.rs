//! 加密传输模块
//!
//! 提供消息加密、解密和完整性验证功能

use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::NativeMessage,
};
use aes_gcm::{
    aead::{A<PERSON>, AeadCore, KeyInit, OsRng},
    Aes256Gcm, Key, Nonce,
};
use base64::{engine::general_purpose, Engine as _};
use hmac::{Hmac, Mac};
use rand::RngCore;
use serde::{Deserialize, Serialize};
use sha2::Sha256;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use tracing::{debug, info};

type HmacSha256 = Hmac<Sha256>;

/// 加密密钥长度（32字节 = 256位）
const KEY_SIZE: usize = 32;
/// 随机数长度（12字节）
const NONCE_SIZE: usize = 12;
/// HMAC 标签长度（32字节）
const HMAC_SIZE: usize = 32;

/// 加密配置
#[derive(Debug, Clone)]
pub struct CryptoConfig {
    /// 是否启用加密
    pub enable_encryption: bool,
    /// 是否启用完整性验证
    pub enable_integrity_check: bool,
    /// 密钥轮换间隔（秒）
    pub key_rotation_interval: u64,
    /// 最大消息大小（字节）
    pub max_message_size: usize,
}

impl Default for CryptoConfig {
    fn default() -> Self {
        Self {
            enable_encryption: true,
            enable_integrity_check: true,
            key_rotation_interval: 3600, // 1小时
            max_message_size: 1024 * 1024, // 1MB
        }
    }
}

/// 加密密钥信息
#[derive(Debug, Clone)]
pub struct CryptoKey {
    /// 密钥ID
    pub id: String,
    /// AES密钥
    pub aes_key: [u8; KEY_SIZE],
    /// HMAC密钥
    pub hmac_key: [u8; KEY_SIZE],
    /// 创建时间
    pub created_at: SystemTime,
    /// 过期时间
    pub expires_at: SystemTime,
    /// 密钥版本
    pub version: u32,
}

impl CryptoKey {
    /// 生成新的加密密钥
    pub fn generate(id: String, version: u32, ttl_seconds: u64) -> Self {
        let mut aes_key = [0u8; KEY_SIZE];
        let mut hmac_key = [0u8; KEY_SIZE];
        
        OsRng.fill_bytes(&mut aes_key);
        OsRng.fill_bytes(&mut hmac_key);

        let now = SystemTime::now();
        let expires_at = now + std::time::Duration::from_secs(ttl_seconds);

        Self {
            id,
            aes_key,
            hmac_key,
            created_at: now,
            expires_at,
            version,
        }
    }

    /// 检查密钥是否过期
    pub fn is_expired(&self) -> bool {
        SystemTime::now() > self.expires_at
    }

    /// 获取密钥年龄（秒）
    pub fn age_seconds(&self) -> u64 {
        self.created_at
            .elapsed()
            .unwrap_or(std::time::Duration::ZERO)
            .as_secs()
    }
}

/// 加密消息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptedMessage {
    /// 加密的有效负载
    pub encrypted_payload: String,
    /// 随机数（Base64编码）
    pub nonce: String,
    /// HMAC标签（Base64编码）
    pub hmac: String,
    /// 密钥ID
    pub key_id: String,
    /// 密钥版本
    pub key_version: u32,
    /// 加密时间戳
    pub encrypted_at: u64,
    /// 消息大小（加密前）
    pub original_size: usize,
}

/// 密钥管理器
#[derive(Debug)]
pub struct KeyManager {
    /// 当前密钥
    current_keys: Arc<RwLock<HashMap<String, CryptoKey>>>,
    /// 历史密钥（用于解密旧消息）
    historical_keys: Arc<RwLock<HashMap<String, CryptoKey>>>,
    /// 配置
    config: CryptoConfig,
    /// 密钥统计
    key_stats: Arc<RwLock<KeyStatistics>>,
}

/// 密钥统计信息
#[derive(Debug, Default)]
struct KeyStatistics {
    /// 密钥生成次数
    keys_generated: u64,
    /// 密钥轮换次数
    key_rotations: u64,
    /// 加密操作次数
    encrypt_operations: u64,
    /// 解密操作次数
    decrypt_operations: u64,
    /// 加密失败次数
    encrypt_failures: u64,
    /// 解密失败次数
    decrypt_failures: u64,
}

impl KeyManager {
    /// 创建新的密钥管理器
    pub fn new(config: CryptoConfig) -> Self {
        Self {
            current_keys: Arc::new(RwLock::new(HashMap::new())),
            historical_keys: Arc::new(RwLock::new(HashMap::new())),
            config,
            key_stats: Arc::new(RwLock::new(KeyStatistics::default())),
        }
    }

    /// 生成新密钥
    pub async fn generate_key(&self, extension_id: &str) -> Result<String> {
        let mut current_keys = self.current_keys.write().await;
        let mut stats = self.key_stats.write().await;

        // 如果已存在密钥，移到历史密钥中
        if let Some(old_key) = current_keys.remove(extension_id) {
            let mut historical_keys = self.historical_keys.write().await;
            historical_keys.insert(old_key.id.clone(), old_key);
            stats.key_rotations += 1;
        }

        // 生成新密钥
        let key_id = format!("{}_{}", extension_id, SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs());
        
        let version = stats.keys_generated as u32 + 1;
        let key = CryptoKey::generate(
            key_id.clone(),
            version,
            self.config.key_rotation_interval,
        );

        current_keys.insert(extension_id.to_string(), key);
        stats.keys_generated += 1;

        info!("为扩展 {} 生成新密钥: {}", extension_id, key_id);
        Ok(key_id)
    }

    /// 获取当前密钥
    pub async fn get_current_key(&self, extension_id: &str) -> Option<CryptoKey> {
        let current_keys = self.current_keys.read().await;
        current_keys.get(extension_id).cloned()
    }

    /// 根据ID获取密钥（包括历史密钥）
    pub async fn get_key_by_id(&self, key_id: &str) -> Option<CryptoKey> {
        // 先查找当前密钥
        {
            let current_keys = self.current_keys.read().await;
            for key in current_keys.values() {
                if key.id == key_id {
                    return Some(key.clone());
                }
            }
        }

        // 再查找历史密钥
        let historical_keys = self.historical_keys.read().await;
        historical_keys.get(key_id).cloned()
    }

    /// 清理过期密钥
    pub async fn cleanup_expired_keys(&self) -> usize {
        let mut historical_keys = self.historical_keys.write().await;
        let expired_keys: Vec<String> = historical_keys
            .iter()
            .filter(|(_, key)| key.is_expired())
            .map(|(id, _)| id.clone())
            .collect();

        let count = expired_keys.len();
        for key_id in expired_keys {
            historical_keys.remove(&key_id);
            debug!("清理过期密钥: {}", key_id);
        }

        if count > 0 {
            info!("清理了 {} 个过期密钥", count);
        }
        count
    }

    /// 获取密钥统计信息
    pub async fn get_key_statistics(&self) -> KeyStatisticsInfo {
        let stats = self.key_stats.read().await;
        let current_keys = self.current_keys.read().await;
        let historical_keys = self.historical_keys.read().await;

        KeyStatisticsInfo {
            keys_generated: stats.keys_generated,
            key_rotations: stats.key_rotations,
            encrypt_operations: stats.encrypt_operations,
            decrypt_operations: stats.decrypt_operations,
            encrypt_failures: stats.encrypt_failures,
            decrypt_failures: stats.decrypt_failures,
            current_keys_count: current_keys.len(),
            historical_keys_count: historical_keys.len(),
            encrypt_success_rate: if stats.encrypt_operations > 0 {
                (stats.encrypt_operations - stats.encrypt_failures) as f64 / stats.encrypt_operations as f64
            } else {
                0.0
            },
            decrypt_success_rate: if stats.decrypt_operations > 0 {
                (stats.decrypt_operations - stats.decrypt_failures) as f64 / stats.decrypt_operations as f64
            } else {
                0.0
            },
        }
    }

    /// 更新统计信息
    async fn update_stats(&self, operation: StatOperation) {
        let mut stats = self.key_stats.write().await;
        match operation {
            StatOperation::EncryptSuccess => stats.encrypt_operations += 1,
            StatOperation::EncryptFailure => {
                stats.encrypt_operations += 1;
                stats.encrypt_failures += 1;
            }
            StatOperation::DecryptSuccess => stats.decrypt_operations += 1,
            StatOperation::DecryptFailure => {
                stats.decrypt_operations += 1;
                stats.decrypt_failures += 1;
            }
        }
    }
}

/// 统计操作类型
enum StatOperation {
    EncryptSuccess,
    EncryptFailure,
    DecryptSuccess,
    DecryptFailure,
}

/// 密钥统计信息（外部接口）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyStatisticsInfo {
    /// 密钥生成次数
    pub keys_generated: u64,
    /// 密钥轮换次数
    pub key_rotations: u64,
    /// 加密操作次数
    pub encrypt_operations: u64,
    /// 解密操作次数
    pub decrypt_operations: u64,
    /// 加密失败次数
    pub encrypt_failures: u64,
    /// 解密失败次数
    pub decrypt_failures: u64,
    /// 当前密钥数量
    pub current_keys_count: usize,
    /// 历史密钥数量
    pub historical_keys_count: usize,
    /// 加密成功率
    pub encrypt_success_rate: f64,
    /// 解密成功率
    pub decrypt_success_rate: f64,
}

/// 加密传输管理器
#[derive(Debug)]
pub struct CryptoManager {
    /// 密钥管理器
    key_manager: KeyManager,
    /// 配置
    config: CryptoConfig,
}

impl CryptoManager {
    /// 创建新的加密管理器
    pub fn new(config: CryptoConfig) -> Self {
        let key_manager = KeyManager::new(config.clone());
        Self {
            key_manager,
            config,
        }
    }

    /// 加密消息
    pub async fn encrypt_message(
        &self,
        message: &NativeMessage,
        extension_id: &str,
    ) -> Result<EncryptedMessage> {
        // 如果未启用加密，返回错误
        if !self.config.enable_encryption {
            return Err(NativeMessagingError::SecurityError(
                "加密功能未启用".to_string(),
            ));
        }

        // 序列化消息
        let payload = serde_json::to_vec(message)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;

        // 检查消息大小
        if payload.len() > self.config.max_message_size {
            return Err(NativeMessagingError::SecurityError(format!(
                "消息大小超过限制: {} > {}",
                payload.len(),
                self.config.max_message_size
            )));
        }

        // 获取或生成密钥
        let key = match self.key_manager.get_current_key(extension_id).await {
            Some(key) if !key.is_expired() => key,
            _ => {
                let key_id = self.key_manager.generate_key(extension_id).await?;
                self.key_manager
                    .get_key_by_id(&key_id)
                    .await
                    .ok_or_else(|| NativeMessagingError::SecurityError("密钥生成失败".to_string()))?
            }
        };

        // 执行加密
        match self.encrypt_with_key(&payload, &key).await {
            Ok(encrypted) => {
                self.key_manager.update_stats(StatOperation::EncryptSuccess).await;
                Ok(encrypted)
            }
            Err(e) => {
                self.key_manager.update_stats(StatOperation::EncryptFailure).await;
                Err(e)
            }
        }
    }

    /// 解密消息
    pub async fn decrypt_message(&self, encrypted: &EncryptedMessage) -> Result<NativeMessage> {
        // 如果未启用加密，返回错误
        if !self.config.enable_encryption {
            return Err(NativeMessagingError::SecurityError(
                "加密功能未启用".to_string(),
            ));
        }

        // 获取密钥
        let key = self
            .key_manager
            .get_key_by_id(&encrypted.key_id)
            .await
            .ok_or_else(|| NativeMessagingError::SecurityError("密钥未找到".to_string()))?;

        // 执行解密
        match self.decrypt_with_key(encrypted, &key).await {
            Ok(payload) => {
                // 反序列化消息
                let message = serde_json::from_slice(&payload)
                    .map_err(|e| NativeMessagingError::SerializationError(e))?;
                
                self.key_manager.update_stats(StatOperation::DecryptSuccess).await;
                Ok(message)
            }
            Err(e) => {
                self.key_manager.update_stats(StatOperation::DecryptFailure).await;
                Err(e)
            }
        }
    }

    /// 使用密钥加密数据
    async fn encrypt_with_key(&self, data: &[u8], key: &CryptoKey) -> Result<EncryptedMessage> {
        // 创建AES-GCM加密器
        let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(&key.aes_key));
        
        // 生成随机数
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        
        // 执行加密
        let ciphertext = cipher
            .encrypt(&nonce, data)
            .map_err(|e| NativeMessagingError::SecurityError(format!("加密失败: {}", e)))?;

        // 计算HMAC（如果启用完整性检查）
        let hmac = if self.config.enable_integrity_check {
            let mut mac = <HmacSha256 as hmac::Mac>::new_from_slice(&key.hmac_key)
                .map_err(|e| NativeMessagingError::SecurityError(format!("HMAC初始化失败: {}", e)))?;
            
            mac.update(&ciphertext);
            mac.update(&nonce);
            mac.update(key.id.as_bytes());
            
            let result = mac.finalize();
            general_purpose::STANDARD.encode(result.into_bytes())
        } else {
            String::new()
        };

        Ok(EncryptedMessage {
            encrypted_payload: general_purpose::STANDARD.encode(&ciphertext),
            nonce: general_purpose::STANDARD.encode(&nonce),
            hmac,
            key_id: key.id.clone(),
            key_version: key.version,
            encrypted_at: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            original_size: data.len(),
        })
    }

    /// 使用密钥解密数据
    async fn decrypt_with_key(&self, encrypted: &EncryptedMessage, key: &CryptoKey) -> Result<Vec<u8>> {
        // 解码Base64数据
        let ciphertext = general_purpose::STANDARD
            .decode(&encrypted.encrypted_payload)
            .map_err(|e| NativeMessagingError::SecurityError(format!("Base64解码失败: {}", e)))?;
        
        let nonce_bytes = general_purpose::STANDARD
            .decode(&encrypted.nonce)
            .map_err(|e| NativeMessagingError::SecurityError(format!("随机数解码失败: {}", e)))?;

        // 验证HMAC（如果启用完整性检查）
        if self.config.enable_integrity_check && !encrypted.hmac.is_empty() {
            let expected_hmac = general_purpose::STANDARD
                .decode(&encrypted.hmac)
                .map_err(|e| NativeMessagingError::SecurityError(format!("HMAC解码失败: {}", e)))?;

            let mut mac = <HmacSha256 as hmac::Mac>::new_from_slice(&key.hmac_key)
                .map_err(|e| NativeMessagingError::SecurityError(format!("HMAC初始化失败: {}", e)))?;
            
            mac.update(&ciphertext);
            mac.update(&nonce_bytes);
            mac.update(key.id.as_bytes());
            
            let computed_hmac = mac.finalize().into_bytes();
            
            if computed_hmac.as_slice() != expected_hmac {
                return Err(NativeMessagingError::SecurityError(
                    "HMAC验证失败，数据可能被篡改".to_string(),
                ));
            }
        }

        // 创建AES-GCM解密器
        let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(&key.aes_key));
        
        // 转换随机数
        if nonce_bytes.len() != NONCE_SIZE {
            return Err(NativeMessagingError::SecurityError(
                "随机数长度不正确".to_string(),
            ));
        }
        let nonce = Nonce::from_slice(&nonce_bytes);

        // 执行解密
        let plaintext = cipher
            .decrypt(nonce, ciphertext.as_slice())
            .map_err(|e| NativeMessagingError::SecurityError(format!("解密失败: {}", e)))?;

        Ok(plaintext)
    }

    /// 生成密钥
    pub async fn generate_key_for_extension(&self, extension_id: &str) -> Result<String> {
        self.key_manager.generate_key(extension_id).await
    }

    /// 清理过期密钥
    pub async fn cleanup_expired_keys(&self) -> usize {
        self.key_manager.cleanup_expired_keys().await
    }

    /// 获取统计信息
    pub async fn get_statistics(&self) -> KeyStatisticsInfo {
        self.key_manager.get_key_statistics().await
    }

    /// 验证消息完整性
    pub fn verify_message_integrity(&self, message: &NativeMessage) -> Result<()> {
        // 基本的消息完整性检查
        if message.request_id.is_empty() {
            return Err(NativeMessagingError::SecurityError(
                "消息缺少请求ID".to_string(),
            ));
        }

        if message.source.is_empty() {
            return Err(NativeMessagingError::SecurityError(
                "消息缺少来源标识".to_string(),
            ));
        }

        // 检查时间戳合理性（防止重放攻击）
        let current_timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        let message_age = current_timestamp.saturating_sub(message.timestamp);
        if message_age > 300 { // 5分钟
            return Err(NativeMessagingError::SecurityError(
                "消息时间戳过旧，可能是重放攻击".to_string(),
            ));
        }

        if message.timestamp > current_timestamp + 60 { // 允许1分钟的时钟偏差
            return Err(NativeMessagingError::SecurityError(
                "消息时间戳异常".to_string(),
            ));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::{MessageType, MessagePriority};
    use std::collections::HashMap;

    #[test]
    fn test_crypto_key_generation() {
        let key = CryptoKey::generate("test".to_string(), 1, 3600);
        assert_eq!(key.id, "test");
        assert_eq!(key.version, 1);
        assert!(!key.is_expired());
    }

    #[test]
    fn test_crypto_key_expiration() {
        let key = CryptoKey::generate("test".to_string(), 1, 0);
        // 立即过期的密钥
        std::thread::sleep(std::time::Duration::from_millis(1));
        assert!(key.is_expired());
    }

    #[tokio::test]
    async fn test_key_manager_generate() {
        let config = CryptoConfig::default();
        let manager = KeyManager::new(config);
        
        let key_id = manager.generate_key("test-ext").await.unwrap();
        assert!(!key_id.is_empty());
        
        let key = manager.get_current_key("test-ext").await.unwrap();
        assert_eq!(key.id, key_id);
    }

    #[tokio::test]
    async fn test_key_manager_rotation() {
        let config = CryptoConfig::default();
        let manager = KeyManager::new(config);
        
        let key_id1 = manager.generate_key("test-ext").await.unwrap();
        let key_id2 = manager.generate_key("test-ext").await.unwrap();
        
        assert_ne!(key_id1, key_id2);
        
        // 第一个密钥应该在历史密钥中
        let historical_key = manager.get_key_by_id(&key_id1).await.unwrap();
        assert_eq!(historical_key.id, key_id1);
    }

    #[tokio::test]
    async fn test_crypto_manager_encrypt_decrypt() {
        let config = CryptoConfig::default();
        let manager = CryptoManager::new(config);
        
        let message = NativeMessage {
            version: 1,
            message_type: MessageType::GetCredentials,
            request_id: "test-request".to_string(),
            payload: serde_json::json!({"test": "data"}),
            timestamp: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
            source: "test-extension".to_string(),
            metadata: HashMap::new(),
            extensions: HashMap::new(),
            priority: MessagePriority::Normal,
            timeout_ms: None,
        };

        // 加密消息
        let encrypted = manager.encrypt_message(&message, "test-ext").await.unwrap();
        assert!(!encrypted.encrypted_payload.is_empty());
        assert!(!encrypted.nonce.is_empty());
        assert!(!encrypted.key_id.is_empty());

        // 解密消息
        let decrypted = manager.decrypt_message(&encrypted).await.unwrap();
        assert_eq!(decrypted.request_id, message.request_id);
        assert_eq!(decrypted.source, message.source);
    }

    #[tokio::test]
    async fn test_crypto_manager_disabled_encryption() {
        let mut config = CryptoConfig::default();
        config.enable_encryption = false;
        
        let manager = CryptoManager::new(config);
        
        let message = NativeMessage {
            version: 1,
            message_type: MessageType::GetCredentials,
            request_id: "test-request".to_string(),
            payload: serde_json::json!({"test": "data"}),
            timestamp: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
            source: "test-extension".to_string(),
            metadata: HashMap::new(),
            extensions: HashMap::new(),
            priority: MessagePriority::Normal,
            timeout_ms: None,
        };

        // 应该返回错误
        let result = manager.encrypt_message(&message, "test-ext").await;
        assert!(result.is_err());
    }

    #[test]
    fn test_message_integrity_verification() {
        let config = CryptoConfig::default();
        let manager = CryptoManager::new(config);
        
        let mut message = NativeMessage {
            version: 1,
            message_type: MessageType::GetCredentials,
            request_id: "test-request".to_string(),
            payload: serde_json::json!({"test": "data"}),
            timestamp: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
            source: "test-extension".to_string(),
            metadata: HashMap::new(),
            extensions: HashMap::new(),
            priority: MessagePriority::Normal,
            timeout_ms: None,
        };

        // 正常消息应该通过验证
        assert!(manager.verify_message_integrity(&message).is_ok());

        // 缺少请求ID的消息应该失败
        message.request_id = String::new();
        assert!(manager.verify_message_integrity(&message).is_err());
    }

    #[test]
    fn test_message_timestamp_validation() {
        let config = CryptoConfig::default();
        let manager = CryptoManager::new(config);
        
        // 过旧的消息
        let old_message = NativeMessage {
            version: 1,
            message_type: MessageType::GetCredentials,
            request_id: "test-request".to_string(),
            payload: serde_json::json!({"test": "data"}),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs()
                .saturating_sub(400), // 6分钟前
            source: "test-extension".to_string(),
            metadata: HashMap::new(),
            extensions: HashMap::new(),
            priority: MessagePriority::Normal,
            timeout_ms: None,
        };

        assert!(manager.verify_message_integrity(&old_message).is_err());
    }

    #[tokio::test]
    async fn test_statistics() {
        let config = CryptoConfig::default();
        let manager = CryptoManager::new(config);
        
        let stats = manager.get_statistics().await;
        assert_eq!(stats.keys_generated, 0);
        assert_eq!(stats.encrypt_operations, 0);
        assert_eq!(stats.decrypt_operations, 0);
    }
} 