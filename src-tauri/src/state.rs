use crate::crypto::{vault_crypto::VaultCrypto, CryptoConfig, KEY_SIZE};
use crate::errors::VaultError;
use crate::hybrid_storage::OrmPasswordService;
use rusqlite::Connection;
use std::path::PathBuf;
use std::sync::Arc;
use tauri::AppHandle;
use tauri_plugin_store::StoreExt;
use tokio::sync::Mutex; // Import VaultError

/// 当前用户信息
#[derive(Clone, Debug, serde::Serialize, serde::Deserialize)]
pub struct CurrentUser {
    /// 用户联系方式（邮箱或手机号）
    pub contact: String,
    /// 用户昵称
    pub nickname: Option<String>,
}

#[derive(Clone)] // Add Clone derive
pub struct AppState {
    // 使用 Arc<Mutex<>> 允许多线程安全访问数据库连接
    pub db: Arc<Mutex<Option<Connection>>>,
    // 使用新的模块化加密系统
    pub crypto: Arc<VaultCrypto>,
    // Store the database path
    pub db_path: Arc<Mutex<Option<PathBuf>>>,
    // ORM 密码服务
    pub orm_service: Arc<Mutex<Option<OrmPasswordService>>>,
    // 当前用户信息
    pub current_user: Arc<Mutex<Option<CurrentUser>>>,
    // AppHandle 用于持久化存储
    pub app_handle: Arc<Mutex<Option<AppHandle>>>,
}

impl Default for AppState {
    fn default() -> Self {
        Self::new()
    }
}

impl AppState {
    pub fn new() -> Self {
        // 创建默认的加密配置
        let mut config = CryptoConfig::default();
        // 根据运行环境调整配置
        #[cfg(debug_assertions)]
        {
            // 开发环境使用较快的参数
            config.argon2_memory = 19456; // 19 MB
            config.argon2_iterations = 2;
        }

        // 创建加密系统实例
        let crypto = VaultCrypto::new(config).expect("Failed to initialize crypto system");

        Self {
            db: Arc::new(Mutex::new(None)),
            crypto: Arc::new(crypto),
            db_path: Arc::new(Mutex::new(None)), // Initialize db_path
            orm_service: Arc::new(Mutex::new(None)),
            current_user: Arc::new(Mutex::new(None)),
            app_handle: Arc::new(Mutex::new(None)),
        }
    }

    /// 使用自定义配置创建状态
    pub fn with_config(config: CryptoConfig) -> Result<Self, VaultError> {
        let crypto = VaultCrypto::new(config)?;

        Ok(Self {
            db: Arc::new(Mutex::new(None)),
            crypto: Arc::new(crypto),
            db_path: Arc::new(Mutex::new(None)),
            orm_service: Arc::new(Mutex::new(None)),
            current_user: Arc::new(Mutex::new(None)),
            app_handle: Arc::new(Mutex::new(None)),
        })
    }

    /// 设置 AppHandle（在应用初始化时调用）
    pub async fn set_app_handle(&self, app_handle: AppHandle) {
        let mut handle_guard = self.app_handle.lock().await;
        *handle_guard = Some(app_handle);

        // 设置 AppHandle 后，尝试从持久化存储加载用户信息
        drop(handle_guard);
        if let Err(e) = self.load_user_from_store().await {
            log::warn!("Failed to load user from store: {}", e);
        }
    }

    /// 从持久化存储加载用户信息
    async fn load_user_from_store(&self) -> Result<(), VaultError> {
        let handle_guard = self.app_handle.lock().await;
        if let Some(ref app_handle) = *handle_guard {
            let store = app_handle
                .store("user.json")
                .map_err(|e| VaultError::InternalError(format!("Failed to access store: {}", e)))?;

            if let Some(user_data) = store.get("current_user") {
                if let Ok(stored_user) = serde_json::from_value::<CurrentUser>(user_data) {
                    drop(handle_guard);
                    let mut user_guard = self.current_user.lock().await;
                    *user_guard = Some(stored_user.clone());
                    log::info!("用户信息已从持久化存储加载: {}", stored_user.contact);
                    return Ok(());
                }
            }
        }
        Ok(())
    }

    /// 保存用户信息到持久化存储
    async fn save_user_to_store(&self, user: &CurrentUser) -> Result<(), VaultError> {
        let handle_guard = self.app_handle.lock().await;
        if let Some(ref app_handle) = *handle_guard {
            let store = app_handle
                .store("user.json")
                .map_err(|e| VaultError::InternalError(format!("Failed to access store: {}", e)))?;

            let user_value = serde_json::to_value(user).map_err(|e| {
                VaultError::InternalError(format!("Failed to serialize user: {}", e))
            })?;

            store.set("current_user", user_value);
            store
                .save()
                .map_err(|e| VaultError::InternalError(format!("Failed to save store: {}", e)))?;

            log::info!("用户信息已保存到持久化存储: {}", user.contact);
        }
        Ok(())
    }

    /// 从持久化存储删除用户信息
    async fn remove_user_from_store(&self) -> Result<(), VaultError> {
        let handle_guard = self.app_handle.lock().await;
        if let Some(ref app_handle) = *handle_guard {
            let store = app_handle
                .store("user.json")
                .map_err(|e| VaultError::InternalError(format!("Failed to access store: {}", e)))?;

            store.delete("current_user");
            store
                .save()
                .map_err(|e| VaultError::InternalError(format!("Failed to save store: {}", e)))?;

            log::info!("用户信息已从持久化存储删除");
        }
        Ok(())
    }

    // 获取一个数据库连接的 guard
    pub async fn db_lock(&self) -> tokio::sync::MutexGuard<'_, Option<Connection>> {
        self.db.lock().await
    }

    // 获取ORM服务的 guard
    pub async fn orm_service_lock(
        &self,
    ) -> tokio::sync::MutexGuard<'_, Option<OrmPasswordService>> {
        self.orm_service.lock().await
    }

    // 设置ORM服务
    pub async fn set_orm_service(&self, service: OrmPasswordService) {
        let mut service_guard = self.orm_service.lock().await;
        *service_guard = Some(service);
    }

    // 获取加密系统引用
    pub fn crypto(&self) -> &VaultCrypto {
        &self.crypto
    }

    // 获取加密系统的Arc引用（用于跨线程传递）
    pub fn crypto_arc(&self) -> Arc<VaultCrypto> {
        Arc::clone(&self.crypto)
    }

    // 获取数据库路径
    pub async fn get_db_path(&self) -> Result<PathBuf, VaultError> {
        let path_guard = self.db_path.lock().await;
        path_guard.clone().ok_or(VaultError::InternalError(
            "Database path not set in state".to_string(),
        ))
    }

    // 设置数据库路径 (called during initialization)
    pub async fn set_db_path(&self, path: PathBuf) {
        let mut path_guard = self.db_path.lock().await;
        *path_guard = Some(path);
    }

    // 辅助函数检查是否已解锁
    pub async fn is_locked(&self) -> bool {
        !self.crypto.is_unlocked().await
    }

    /// 检查保险库是否已初始化
    pub async fn is_initialized(&self) -> bool {
        use crate::crypto::vault_crypto::CryptoState;
        !matches!(self.crypto.state().await, CryptoState::Uninitialized)
    }

    /// 启动自动锁定任务
    pub fn start_auto_lock_task(&self) {
        let crypto_arc = self.crypto_arc();
        crypto_arc.start_auto_lock_task();
    }

    /// 获取保险库统计信息
    pub async fn get_vault_stats(&self) -> crate::crypto::vault_crypto::VaultStats {
        self.crypto.get_vault_stats().await
    }

    /// 清理过期的会话密钥
    pub async fn cleanup_expired_sessions(&self) {
        self.crypto.cleanup_expired_sessions().await;
    }

    /// 为了向后兼容，提供 key_lock 方法
    /// 注意：这是一个临时的兼容层，新代码应该直接使用 crypto() 方法
    pub async fn key_lock(&self) -> KeyGuard {
        // 这里返回一个空的守卫，因为新系统不直接暴露主密钥
        // 旧的代码可能需要重构以使用新的加密API
        KeyGuard::new(None)
    }

    // 获取当前用户信息
    pub async fn get_current_user(&self) -> Option<CurrentUser> {
        let user_guard = self.current_user.lock().await;
        user_guard.clone()
    }

    // 设置当前用户信息（带持久化）
    pub async fn set_current_user(&self, user: CurrentUser) {
        // 保存到内存
        let mut user_guard = self.current_user.lock().await;
        *user_guard = Some(user.clone());
        drop(user_guard);

        // 保存到持久化存储
        if let Err(e) = self.save_user_to_store(&user).await {
            log::error!("Failed to save user to store: {}", e);
        }
    }

    // 清除当前用户信息（带持久化）
    pub async fn clear_current_user(&self) {
        // 从内存清除
        let mut user_guard = self.current_user.lock().await;
        *user_guard = None;
        drop(user_guard);

        // 从持久化存储清除
        if let Err(e) = self.remove_user_from_store().await {
            log::error!("Failed to remove user from store: {}", e);
        }
    }

    // 获取当前用户的对称密钥（从 keychain）
    pub async fn get_user_symmetric_key(&self) -> Result<[u8; KEY_SIZE], VaultError> {
        use crate::crypto::keychain::RegistrationKeychainManager;

        let user = self
            .get_current_user()
            .await
            .ok_or_else(|| VaultError::InternalError("No current user set".to_string()))?;

        let keychain_manager = RegistrationKeychainManager::new(&user.contact);
        keychain_manager.get_symmetric_key().map_err(|e| {
            VaultError::InternalError(format!("Failed to get symmetric key from keychain: {}", e))
        })
    }

    /// 检查是否有已登录的用户
    pub async fn has_logged_in_user(&self) -> bool {
        self.get_current_user().await.is_some()
    }

    /// 重新加载用户信息（从持久化存储）
    pub async fn reload_user_from_store(&self) -> Result<(), VaultError> {
        self.load_user_from_store().await
    }
}

// 为了向后兼容，创建一个密钥守卫类型
pub struct KeyGuard {
    key: Option<[u8; KEY_SIZE]>,
}

impl KeyGuard {
    pub fn new(key: Option<[u8; KEY_SIZE]>) -> Self {
        Self { key }
    }

    pub fn as_ref(&self) -> Option<&[u8; KEY_SIZE]> {
        self.key.as_ref()
    }

    pub fn is_some(&self) -> bool {
        self.key.is_some()
    }

    pub fn take(&mut self) -> Option<[u8; KEY_SIZE]> {
        self.key.take()
    }

    // 为了兼容旧的 ok_or 调用
    pub fn ok_or<E>(self, err: E) -> Result<[u8; KEY_SIZE], E> {
        match self.key {
            Some(key) => Ok(key),
            None => Err(err),
        }
    }

    // 为了兼容旧的赋值操作
    pub fn replace(&mut self, new_key: Option<[u8; KEY_SIZE]>) -> Option<[u8; KEY_SIZE]> {
        std::mem::replace(&mut self.key, new_key)
    }
}

// 为了支持解引用赋值操作
impl std::ops::Deref for KeyGuard {
    type Target = Option<[u8; KEY_SIZE]>;

    fn deref(&self) -> &Self::Target {
        &self.key
    }
}

impl std::ops::DerefMut for KeyGuard {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.key
    }
}
