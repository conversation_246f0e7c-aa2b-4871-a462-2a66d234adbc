//! 企业级密码管理器多端同步系统
//!
//! 基于向量时钟的最佳同步算法，支持复杂的离线同步场景

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};

// 导出所有子模块
pub mod errors;
pub mod examples;
pub mod manager;
pub mod merge;
pub mod ntp;
pub mod storage;
pub mod types;
pub mod vector_clock;

// 重新导出核心类型
pub use errors::*;
pub use manager::{SessionStatus, SyncDirection, SyncManager, SyncSession, SyncStrategyConfig};
pub use merge::{MergeConfig, MergeManager, MergeOutcome, MergeStrategy, ThreeWayMerger};
pub use ntp::{NtpConfig, TimeManager, TimeProvider};
pub use storage::{StorageBackend, StorageConfig, StorageManager, StorageQuery};
pub use types::{CredentialType, OperationType, SyncRecord, SyncRecordBuilder};
pub use vector_clock::{CausalRelation, VectorClock, VectorClockManager};

/// 同步配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncConfig {
    /// 设备ID
    pub device_id: String,
    /// 是否启用同步
    pub enabled: bool,
    /// 同步间隔（秒）
    pub sync_interval_seconds: u64,
    /// 服务器URL
    pub server_url: String,
    /// 最大重试次数
    pub max_retries: u32,
    /// 连接超时（毫秒）
    pub timeout_ms: u64,
    /// 批量操作大小
    pub batch_size: usize,
    /// 是否启用压缩
    pub compression_enabled: bool,
    /// 是否启用加密
    pub encryption_enabled: bool,
    /// 向量时钟最大大小
    pub max_vector_clock_size: usize,
    /// NTP服务器列表
    pub ntp_servers: Vec<String>,
    /// 时间同步间隔（秒）
    pub time_sync_interval_seconds: u64,
    /// 是否启用自动同步
    pub auto_sync_enabled: bool,
}

impl Default for SyncConfig {
    fn default() -> Self {
        Self {
            device_id: uuid::Uuid::new_v4().to_string(),
            enabled: true,
            sync_interval_seconds: 300, // 5分钟
            server_url: "http://example.com".to_string(),
            max_retries: 3,
            timeout_ms: 30000, // 30秒
            batch_size: 100,
            compression_enabled: true,
            encryption_enabled: true,
            max_vector_clock_size: 1000,
            ntp_servers: vec![
                "time.apple.com:123".to_string(),
                "time.cloudflare.com:123".to_string(),
                "pool.ntp.org:123".to_string(),
            ],
            time_sync_interval_seconds: 300, // 5分钟
            auto_sync_enabled: true,
        }
    }
}

/// 冲突解决策略
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[derive(Default)]
pub enum ConflictResolutionStrategy {
    /// 智能合并
    #[default]
    SmartMerge,
    /// 最后写入获胜
    LastWriteWins,
    /// 最新时间戳获胜
    LatestTimestamp,
    /// 手动解决
    Manual,
    /// 保留所有版本
    KeepAll,
}


/// 性能配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// 最大并发同步数
    pub max_concurrent_syncs: usize,
    /// 缓存大小
    pub cache_size: usize,
    /// 压缩阈值（字节）
    pub compression_threshold: usize,
    /// 是否启用增量同步
    pub enable_incremental_sync: bool,
    /// 向量时钟压缩间隔（秒）
    pub vector_clock_compression_interval: u64,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            max_concurrent_syncs: 5,
            cache_size: 1000,
            compression_threshold: 1024, // 1KB
            enable_incremental_sync: true,
            vector_clock_compression_interval: 3600, // 1小时
        }
    }
}

/// 同步结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncResult {
    /// 是否成功
    pub success: bool,
    /// 同步的记录数
    pub synced_count: usize,
    /// 冲突数
    pub conflict_count: usize,
    /// 错误信息
    pub error: Option<String>,
    /// 耗时（毫秒）
    pub duration_ms: u64,
    /// 统计信息
    pub statistics: SyncStatistics,
    /// 详细信息
    pub details: HashMap<String, String>,
}

/// 同步统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct SyncStatistics {
    /// 上传的记录数
    pub uploaded_records: usize,
    /// 下载的记录数
    pub downloaded_records: usize,
    /// 合并的记录数
    pub merged_records: usize,
    /// 跳过的记录数
    pub skipped_records: usize,
    /// 错误的记录数
    pub error_records: usize,
    /// 传输的字节数
    pub bytes_transferred: u64,
    /// 压缩比率
    pub compression_ratio: f64,
}

/// 同步事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncEvent {
    /// 事件ID
    pub id: uuid::Uuid,
    /// 事件类型
    pub event_type: SyncEventType,
    /// 设备ID
    pub device_id: String,
    /// 凭据ID（可选）
    pub credential_id: Option<String>,
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 向量时钟（可选）
    pub vector_clock: Option<VectorClock>,
    /// 事件数据（可选）
    pub data: Option<String>,
    /// 元数据
    pub metadata: HashMap<String, String>,
}

/// 同步事件类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SyncEventType {
    /// 同步开始
    SyncStarted,
    /// 同步完成
    SyncCompleted,
    /// 同步失败
    SyncFailed,
    /// 同步暂停
    SyncPaused,
    /// 同步恢复
    SyncResumed,
    /// 检测到冲突
    ConflictDetected,
    /// 冲突已解决
    ConflictResolved,
    /// 凭据变更
    CredentialChanged,
    /// 设备连接
    DeviceConnected,
    /// 设备断开
    DeviceDisconnected,
    /// 时间同步
    TimeSynced,
    /// 向量时钟更新
    VectorClockUpdated,
}

/// 同步模块特征
///
/// 定义同步模块的基本接口
#[async_trait]
pub trait SyncModule {
    /// 初始化模块
    async fn initialize(&mut self) -> Result<(), SyncError>;

    /// 启动模块
    async fn start(&mut self) -> Result<(), SyncError>;

    /// 停止模块
    async fn stop(&mut self) -> Result<(), SyncError>;

    /// 获取模块状态
    async fn get_status(&self) -> manager::SyncManagerStatus;

    /// 健康检查
    async fn health_check(&self) -> Result<bool, SyncError>;
}

/// 工具函数：计算数据哈希
pub fn calculate_data_hash(data: &str) -> String {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};

    let mut hasher = DefaultHasher::new();
    data.hash(&mut hasher);
    format!("{:x}", hasher.finish())
}

/// 生成版本号
///
/// 基于当前时间戳生成唯一版本号
pub fn generate_version() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_nanos() as u64
}

/// 工具函数：验证设备ID
pub fn validate_device_id(device_id: &str) -> bool {
    !device_id.is_empty()
        && device_id.len() <= 64
        && device_id
            .chars()
            .all(|c| c.is_alphanumeric() || c == '-' || c == '_')
}

/// 工具函数：格式化时间戳
pub fn format_timestamp(timestamp: &DateTime<Utc>) -> String {
    timestamp.to_rfc3339()
}

/// 工具函数：解析时间戳
pub fn parse_timestamp(timestamp_str: &str) -> Result<DateTime<Utc>, chrono::ParseError> {
    DateTime::parse_from_rfc3339(timestamp_str).map(|dt| dt.with_timezone(&Utc))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sync_config_default() {
        let config = SyncConfig::default();
        assert!(config.enabled);
        assert_eq!(config.sync_interval_seconds, 300);
        assert_eq!(config.max_retries, 3);
        assert!(config.compression_enabled);
        assert!(config.encryption_enabled);
        assert_eq!(config.max_vector_clock_size, 1000);
    }

    #[test]
    fn test_conflict_resolution_strategy() {
        let strategy = ConflictResolutionStrategy::default();
        assert_eq!(strategy, ConflictResolutionStrategy::SmartMerge);
    }

    #[test]
    fn test_performance_config_default() {
        let config = PerformanceConfig::default();
        assert_eq!(config.max_concurrent_syncs, 5);
        assert_eq!(config.cache_size, 1000);
        assert_eq!(config.compression_threshold, 1024);
        assert!(config.enable_incremental_sync);
    }

    #[test]
    fn test_sync_statistics_default() {
        let stats = SyncStatistics::default();
        assert_eq!(stats.uploaded_records, 0);
        assert_eq!(stats.downloaded_records, 0);
        assert_eq!(stats.merged_records, 0);
    }

    #[test]
    fn test_calculate_data_hash() {
        let data = "test data";
        let hash1 = calculate_data_hash(data);
        let hash2 = calculate_data_hash(data);
        assert_eq!(hash1, hash2);

        let different_data = "different data";
        let hash3 = calculate_data_hash(different_data);
        assert_ne!(hash1, hash3);
    }

    #[test]
    fn test_validate_device_id() {
        assert!(validate_device_id("device-001"));
        assert!(validate_device_id("device_001"));
        assert!(validate_device_id("device001"));
        assert!(!validate_device_id(""));
        assert!(!validate_device_id("device with spaces"));
        assert!(!validate_device_id("device@001"));
    }

    #[test]
    fn test_format_and_parse_timestamp() {
        let now = Utc::now();
        let formatted = format_timestamp(&now);

        // 使用parse_timestamp函数
        let parsed = parse_timestamp(&formatted).unwrap();

        // 由于格式化会丢失微秒精度，我们只比较到秒
        assert_eq!(now.timestamp(), parsed.timestamp());
    }

    #[test]
    fn test_sync_event_creation() {
        let event = SyncEvent {
            id: uuid::Uuid::new_v4(),
            event_type: SyncEventType::SyncStarted,
            device_id: "test_device".to_string(),
            credential_id: None,
            timestamp: Utc::now(),
            vector_clock: None,
            data: None,
            metadata: HashMap::new(),
        };

        assert_eq!(event.event_type, SyncEventType::SyncStarted);
        assert_eq!(event.device_id, "test_device");
    }

    #[test]
    fn test_sync_result_creation() {
        let result = SyncResult {
            success: true,
            synced_count: 10,
            conflict_count: 2,
            error: None,
            duration_ms: 1000,
            statistics: SyncStatistics::default(),
            details: HashMap::new(),
        };

        assert!(result.success);
        assert_eq!(result.synced_count, 10);
        assert_eq!(result.conflict_count, 2);
    }
}
