//! 存储抽象层实现
//!
//! 提供同步系统的统一存储接口

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use tokio::sync::RwLock;

// 保留类型导入以备将来扩展功能使用
#[allow(unused_imports)]
use crate::sync::types::LoginCredential;
use crate::sync::{SyncError, SyncRecord};
// 保留以下导入以备将来的功能扩展
#[allow(unused_imports)]
use crate::sync::{CredentialType, OperationType, VectorClock};

/// 存储操作结果
pub type StorageResult<T> = Result<T, SyncError>;

/// 存储配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    /// 数据库路径
    pub database_path: Option<PathBuf>,
    /// 最大连接数
    pub max_connections: u32,
    /// 连接超时时间（秒）
    pub connection_timeout_secs: u64,
    /// 是否启用WAL模式
    pub enable_wal: bool,
    /// 缓存大小
    pub cache_size: usize,
    /// 是否启用自动备份
    pub enable_auto_backup: bool,
    /// 备份间隔（小时）
    pub backup_interval_hours: u64,
}

impl Default for StorageConfig {
    fn default() -> Self {
        Self {
            database_path: None,
            max_connections: 10,
            connection_timeout_secs: 30,
            enable_wal: true,
            cache_size: 1000,
            enable_auto_backup: false,
            backup_interval_hours: 24,
        }
    }
}

/// 存储统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageStats {
    /// 总记录数
    pub total_records: u64,
    /// 存储大小（字节）
    pub storage_size: u64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
    /// 读取操作次数
    pub read_operations: u64,
    /// 写入操作次数
    pub write_operations: u64,
    /// 删除操作次数
    pub delete_operations: u64,
}

impl Default for StorageStats {
    fn default() -> Self {
        Self {
            total_records: 0,
            storage_size: 0,
            last_updated: Utc::now(),
            read_operations: 0,
            write_operations: 0,
            delete_operations: 0,
        }
    }
}

/// 存储查询条件
#[derive(Debug, Clone)]
#[derive(Default)]
pub struct StorageQuery {
    /// 按记录ID过滤
    pub record_ids: Option<Vec<String>>,
    /// 按设备ID过滤
    pub device_ids: Option<Vec<String>>,
    /// 按时间范围过滤
    pub time_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    /// 按凭据类型过滤
    pub credential_types: Option<Vec<String>>,
    /// 限制返回数量
    pub limit: Option<usize>,
    /// 偏移量
    pub offset: Option<usize>,
}


/// 存储后端抽象特征
///
/// 定义了同步系统所需的所有存储操作接口
#[async_trait]
pub trait StorageBackend: Send + Sync {
    /// 初始化存储后端
    async fn initialize(&mut self) -> StorageResult<()>;

    /// 关闭存储后端
    async fn close(&mut self) -> StorageResult<()>;

    /// 存储单个同步记录
    ///
    /// # 参数
    /// * `record` - 要存储的同步记录
    ///
    /// # 返回
    /// 成功时返回记录ID
    async fn store_record(&mut self, record: &SyncRecord) -> StorageResult<String>;

    /// 批量存储同步记录
    ///
    /// # 参数
    /// * `records` - 要存储的同步记录列表
    ///
    /// # 返回
    /// 成功时返回存储的记录数量
    async fn store_records(&mut self, records: &[SyncRecord]) -> StorageResult<usize>;

    /// 根据ID获取同步记录
    ///
    /// # 参数
    /// * `record_id` - 记录ID
    ///
    /// # 返回
    /// 找到的同步记录，如果不存在则返回None
    async fn get_record(&self, record_id: &str) -> StorageResult<Option<SyncRecord>>;

    /// 根据查询条件获取多个记录
    ///
    /// # 参数
    /// * `query` - 查询条件
    ///
    /// # 返回
    /// 匹配的同步记录列表
    async fn get_records(&self, query: &StorageQuery) -> StorageResult<Vec<SyncRecord>>;

    /// 获取所有记录
    ///
    /// # 返回
    /// 所有同步记录的列表
    async fn get_all_records(&self) -> StorageResult<Vec<SyncRecord>>;

    /// 更新同步记录
    ///
    /// # 参数
    /// * `record_id` - 记录ID
    /// * `record` - 更新后的记录
    ///
    /// # 返回
    /// 是否成功更新
    async fn update_record(&mut self, record_id: &str, record: &SyncRecord) -> StorageResult<bool>;

    /// 删除同步记录
    ///
    /// # 参数
    /// * `record_id` - 要删除的记录ID
    ///
    /// # 返回
    /// 是否成功删除
    async fn delete_record(&mut self, record_id: &str) -> StorageResult<bool>;

    /// 批量删除记录
    ///
    /// # 参数
    /// * `record_ids` - 要删除的记录ID列表
    ///
    /// # 返回
    /// 成功删除的记录数量
    async fn delete_records(&mut self, record_ids: &[String]) -> StorageResult<usize>;

    /// 清空所有记录
    ///
    /// # 返回
    /// 清空的记录数量
    async fn clear_all(&mut self) -> StorageResult<usize>;

    /// 获取存储统计信息
    ///
    /// # 返回
    /// 存储统计信息
    async fn get_stats(&self) -> StorageResult<StorageStats>;

    /// 压缩存储（可选实现）
    ///
    /// # 返回
    /// 压缩后释放的空间大小（字节）
    async fn compact(&mut self) -> StorageResult<u64> {
        Ok(0)
    }

    /// 备份存储到指定路径（可选实现）
    ///
    /// # 参数
    /// * `backup_path` - 备份文件路径
    ///
    /// # 返回
    /// 备份的记录数量
    async fn backup_to(&self, _backup_path: &PathBuf) -> StorageResult<usize> {
        Err(SyncError::Storage {
            operation: "backup".to_string(),
            source: Box::new(std::io::Error::new(
                std::io::ErrorKind::Unsupported,
                "Backup not supported",
            )),
        })
    }

    /// 从备份文件恢复（可选实现）
    ///
    /// # 参数
    /// * `backup_path` - 备份文件路径
    ///
    /// # 返回
    /// 恢复的记录数量
    async fn restore_from(&mut self, _backup_path: &PathBuf) -> StorageResult<usize> {
        Err(SyncError::Storage {
            operation: "restore".to_string(),
            source: Box::new(std::io::Error::new(
                std::io::ErrorKind::Unsupported,
                "Restore not supported",
            )),
        })
    }
}

/// 内存存储后端实现
///
/// 用于测试和临时存储，数据不会持久化
pub struct MemoryStorage {
    records: RwLock<HashMap<String, SyncRecord>>,
    stats: RwLock<StorageStats>,
}

impl MemoryStorage {
    /// 创建新的内存存储实例
    pub fn new() -> Self {
        Self {
            records: RwLock::new(HashMap::new()),
            stats: RwLock::new(StorageStats::default()),
        }
    }

    /// 更新统计信息
    async fn update_stats<F>(&self, updater: F)
    where
        F: FnOnce(&mut StorageStats),
    {
        let mut stats = self.stats.write().await;
        updater(&mut stats);
        stats.last_updated = Utc::now();
    }

    /// 检查记录是否匹配查询条件
    fn matches_query(&self, record: &SyncRecord, query: &StorageQuery) -> bool {
        // 检查记录ID过滤
        if let Some(ref record_ids) = query.record_ids {
            if !record_ids.contains(&record.id.to_string()) {
                return false;
            }
        }

        // 检查设备ID过滤
        if let Some(ref device_ids) = query.device_ids {
            if !device_ids.contains(&record.device_id) {
                return false;
            }
        }

        // 检查时间范围过滤
        if let Some((start, end)) = query.time_range {
            if record.local_timestamp < start || record.local_timestamp > end {
                return false;
            }
        }

        // 检查凭据类型过滤
        if let Some(ref credential_types) = query.credential_types {
            let record_type = record.credential_type.name();
            if !credential_types.contains(&record_type.to_string()) {
                return false;
            }
        }

        true
    }
}

impl Default for MemoryStorage {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl StorageBackend for MemoryStorage {
    async fn initialize(&mut self) -> StorageResult<()> {
        // 内存存储无需初始化
        Ok(())
    }

    async fn close(&mut self) -> StorageResult<()> {
        // 清空内存数据
        self.records.write().await.clear();
        Ok(())
    }

    async fn store_record(&mut self, record: &SyncRecord) -> StorageResult<String> {
        let record_id = record.id.to_string();

        {
            let mut records = self.records.write().await;
            records.insert(record_id.clone(), record.clone());
        }

        self.update_stats(|stats| {
            stats.total_records += 1;
            stats.write_operations += 1;
            stats.storage_size += std::mem::size_of_val(record) as u64;
        })
        .await;

        Ok(record_id)
    }

    async fn store_records(&mut self, records: &[SyncRecord]) -> StorageResult<usize> {
        let count = records.len();

        {
            let mut storage_records = self.records.write().await;
            for record in records {
                storage_records.insert(record.id.to_string(), record.clone());
            }
        }

        self.update_stats(|stats| {
            stats.total_records += count as u64;
            stats.write_operations += count as u64;
            stats.storage_size += records
                .iter()
                .map(|r| std::mem::size_of_val(r) as u64)
                .sum::<u64>();
        })
        .await;

        Ok(count)
    }

    async fn get_record(&self, record_id: &str) -> StorageResult<Option<SyncRecord>> {
        let records = self.records.read().await;
        let result = records.get(record_id).cloned();

        self.update_stats(|stats| {
            stats.read_operations += 1;
        })
        .await;

        Ok(result)
    }

    async fn get_records(&self, query: &StorageQuery) -> StorageResult<Vec<SyncRecord>> {
        let records = self.records.read().await;
        let mut results: Vec<SyncRecord> = records
            .values()
            .filter(|record| self.matches_query(record, query))
            .cloned()
            .collect();

        // 按时间戳排序
        results.sort_by(|a, b| a.local_timestamp.cmp(&b.local_timestamp));

        // 应用偏移量和限制
        if let Some(offset) = query.offset {
            if offset < results.len() {
                results = results.into_iter().skip(offset).collect();
            } else {
                results.clear();
            }
        }

        if let Some(limit) = query.limit {
            results.truncate(limit);
        }

        self.update_stats(|stats| {
            stats.read_operations += 1;
        })
        .await;

        Ok(results)
    }

    async fn get_all_records(&self) -> StorageResult<Vec<SyncRecord>> {
        let records = self.records.read().await;
        let mut results: Vec<SyncRecord> = records.values().cloned().collect();

        // 按时间戳排序
        results.sort_by(|a, b| a.local_timestamp.cmp(&b.local_timestamp));

        self.update_stats(|stats| {
            stats.read_operations += 1;
        })
        .await;

        Ok(results)
    }

    async fn update_record(&mut self, record_id: &str, record: &SyncRecord) -> StorageResult<bool> {
        let mut records = self.records.write().await;
        let updated = records
            .insert(record_id.to_string(), record.clone())
            .is_some();

        self.update_stats(|stats| {
            stats.write_operations += 1;
        })
        .await;

        Ok(updated)
    }

    async fn delete_record(&mut self, record_id: &str) -> StorageResult<bool> {
        let mut records = self.records.write().await;
        let deleted = records.remove(record_id).is_some();

        if deleted {
            self.update_stats(|stats| {
                stats.total_records = stats.total_records.saturating_sub(1);
                stats.delete_operations += 1;
            })
            .await;
        }

        Ok(deleted)
    }

    async fn delete_records(&mut self, record_ids: &[String]) -> StorageResult<usize> {
        let mut records = self.records.write().await;
        let mut deleted_count = 0;

        for record_id in record_ids {
            if records.remove(record_id).is_some() {
                deleted_count += 1;
            }
        }

        if deleted_count > 0 {
            self.update_stats(|stats| {
                stats.total_records = stats.total_records.saturating_sub(deleted_count as u64);
                stats.delete_operations += deleted_count as u64;
            })
            .await;
        }

        Ok(deleted_count)
    }

    async fn clear_all(&mut self) -> StorageResult<usize> {
        let mut records = self.records.write().await;
        let count = records.len();
        records.clear();

        self.update_stats(|stats| {
            stats.total_records = 0;
            stats.storage_size = 0;
            stats.delete_operations += count as u64;
        })
        .await;

        Ok(count)
    }

    async fn get_stats(&self) -> StorageResult<StorageStats> {
        let stats = self.stats.read().await;
        Ok(stats.clone())
    }

    async fn compact(&mut self) -> StorageResult<u64> {
        // 内存存储无需压缩，返回0
        Ok(0)
    }
}

/// 存储管理器
///
/// 提供存储后端的统一管理接口
pub struct StorageManager {
    backend: Box<dyn StorageBackend>,
}

impl StorageManager {
    /// 创建新的存储管理器
    ///
    /// # 参数
    /// * `backend` - 存储后端实现
    pub fn new(backend: Box<dyn StorageBackend>) -> Self {
        Self { backend }
    }

    /// 创建使用内存存储的管理器
    pub fn with_memory_storage() -> Self {
        Self::new(Box::new(MemoryStorage::new()))
    }

    /// 初始化存储
    pub async fn initialize(&mut self) -> StorageResult<()> {
        self.backend.initialize().await
    }

    /// 关闭存储
    pub async fn close(&mut self) -> StorageResult<()> {
        self.backend.close().await
    }

    /// 存储记录
    pub async fn store_record(&mut self, record: &SyncRecord) -> StorageResult<String> {
        self.backend.store_record(record).await
    }

    /// 批量存储记录
    pub async fn store_records(&mut self, records: &[SyncRecord]) -> StorageResult<usize> {
        self.backend.store_records(records).await
    }

    /// 获取记录
    pub async fn get_record(&self, record_id: &str) -> StorageResult<Option<SyncRecord>> {
        self.backend.get_record(record_id).await
    }

    /// 查询记录
    pub async fn get_records(&self, query: &StorageQuery) -> StorageResult<Vec<SyncRecord>> {
        self.backend.get_records(query).await
    }

    /// 获取所有记录
    pub async fn get_all_records(&self) -> StorageResult<Vec<SyncRecord>> {
        self.backend.get_all_records().await
    }

    /// 更新记录
    pub async fn update_record(
        &mut self,
        record_id: &str,
        record: &SyncRecord,
    ) -> StorageResult<bool> {
        self.backend.update_record(record_id, record).await
    }

    /// 删除记录
    pub async fn delete_record(&mut self, record_id: &str) -> StorageResult<bool> {
        self.backend.delete_record(record_id).await
    }

    /// 批量删除记录
    pub async fn delete_records(&mut self, record_ids: &[String]) -> StorageResult<usize> {
        self.backend.delete_records(record_ids).await
    }

    /// 清空所有记录
    pub async fn clear_all(&mut self) -> StorageResult<usize> {
        self.backend.clear_all().await
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> StorageResult<StorageStats> {
        self.backend.get_stats().await
    }

    /// 压缩存储
    pub async fn compact(&mut self) -> StorageResult<u64> {
        self.backend.compact().await
    }

    /// 备份存储
    pub async fn backup_to(&self, backup_path: &PathBuf) -> StorageResult<usize> {
        self.backend.backup_to(backup_path).await
    }

    /// 从备份恢复
    pub async fn restore_from(&mut self, backup_path: &PathBuf) -> StorageResult<usize> {
        self.backend.restore_from(backup_path).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    /// 创建测试用的同步记录
    fn create_test_record(id: &str, device_id: &str) -> SyncRecord {
        SyncRecord::builder(
            format!("cred_{}", id),
            CredentialType::Login,
            OperationType::Create,
            device_id.to_string(),
        )
        .data(
            serde_json::to_string(&LoginCredential {
                name: "Test Login".to_string(),
                username: "<EMAIL>".to_string(),
                password: "password123".to_string(),
                url: Some("https://example.com".to_string()),
                notes: None,
                tags: vec!["test".to_string()],
                folder_id: None,
                favorite: false,
                custom_fields: HashMap::new(),
                password_history: Vec::new(),
                last_used: None,
            })
            .unwrap(),
        )
        .build()
        .unwrap()
    }

    #[tokio::test]
    async fn test_memory_storage_basic_operations() {
        let mut storage = MemoryStorage::new();
        storage.initialize().await.unwrap();

        // 测试存储记录
        let record = create_test_record("test1", "device1");
        let stored_id = storage.store_record(&record).await.unwrap();
        assert_eq!(stored_id, record.id.to_string());

        // 测试获取记录
        let retrieved = storage.get_record(&record.id.to_string()).await.unwrap();
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().id, record.id);

        // 测试更新记录
        let mut updated_record = record.clone();
        updated_record.version += 1;
        let updated = storage
            .update_record(&record.id.to_string(), &updated_record)
            .await
            .unwrap();
        assert!(updated);

        // 测试删除记录
        let deleted = storage.delete_record(&record.id.to_string()).await.unwrap();
        assert!(deleted);

        // 验证记录已删除
        let retrieved = storage.get_record(&record.id.to_string()).await.unwrap();
        assert!(retrieved.is_none());
    }

    #[tokio::test]
    async fn test_memory_storage_batch_operations() {
        let mut storage = MemoryStorage::new();
        storage.initialize().await.unwrap();

        // 测试批量存储
        let records = vec![
            create_test_record("test1", "device1"),
            create_test_record("test2", "device2"),
            create_test_record("test3", "device1"),
        ];

        let stored_count = storage.store_records(&records).await.unwrap();
        assert_eq!(stored_count, 3);

        // 测试获取所有记录
        let all_records = storage.get_all_records().await.unwrap();
        assert_eq!(all_records.len(), 3);

        // 测试批量删除
        let record_ids = vec![records[0].id.to_string(), records[1].id.to_string()];
        let deleted_count = storage.delete_records(&record_ids).await.unwrap();
        assert_eq!(deleted_count, 2);

        // 验证剩余记录
        let remaining = storage.get_all_records().await.unwrap();
        assert_eq!(remaining.len(), 1);
        assert_eq!(remaining[0].id, records[2].id);
    }

    #[tokio::test]
    async fn test_storage_query() {
        let mut storage = MemoryStorage::new();
        storage.initialize().await.unwrap();

        // 存储测试数据
        let records = vec![
            create_test_record("test1", "device1"),
            create_test_record("test2", "device2"),
            create_test_record("test3", "device1"),
        ];
        storage.store_records(&records).await.unwrap();

        // 测试按设备ID查询
        let query = StorageQuery {
            device_ids: Some(vec!["device1".to_string()]),
            ..Default::default()
        };
        let results = storage.get_records(&query).await.unwrap();
        assert_eq!(results.len(), 2);

        // 测试限制数量
        let query = StorageQuery {
            limit: Some(1),
            ..Default::default()
        };
        let results = storage.get_records(&query).await.unwrap();
        assert_eq!(results.len(), 1);
    }

    #[tokio::test]
    async fn test_storage_stats() {
        let mut storage = MemoryStorage::new();
        storage.initialize().await.unwrap();

        // 初始统计
        let stats = storage.get_stats().await.unwrap();
        assert_eq!(stats.total_records, 0);

        // 存储记录后的统计
        let record = create_test_record("test1", "device1");
        storage.store_record(&record).await.unwrap();

        let stats = storage.get_stats().await.unwrap();
        assert_eq!(stats.total_records, 1);
        assert_eq!(stats.write_operations, 1);
    }

    #[tokio::test]
    async fn test_storage_manager() {
        let mut manager = StorageManager::with_memory_storage();
        manager.initialize().await.unwrap();

        // 测试通过管理器进行操作
        let record = create_test_record("test1", "device1");
        let stored_id = manager.store_record(&record).await.unwrap();
        assert_eq!(stored_id, record.id.to_string());

        let retrieved = manager.get_record(&record.id.to_string()).await.unwrap();
        assert!(retrieved.is_some());

        let stats = manager.get_stats().await.unwrap();
        assert_eq!(stats.total_records, 1);
    }
}
