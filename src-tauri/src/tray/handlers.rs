/// 托盘事件处理器实现
///
/// 提供默认的托盘事件处理器实现，
/// 包括基本的窗口操作和应用控制。
use crate::tray::{
    events::{TrayClickType, TrayEvent, TrayEventHandler, TrayEventType},
    Tray<PERSON>rror, TrayResult,
};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, WebviewWindow};

/// 默认托盘事件处理器
///
/// 提供常见的托盘事件处理逻辑，如显示/隐藏窗口、退出应用等。
#[derive(Debug)]
pub struct DefaultTrayHandler {
    /// 处理器名称
    name: String,
    /// 主窗口标识符
    main_window_label: String,
    /// 是否在左键点击时切换窗口显示状态
    toggle_window_on_left_click: bool,
    /// 是否在右键点击时显示菜单
    show_menu_on_right_click: bool,
}

impl DefaultTrayHandler {
    /// 创建新的默认托盘处理器
    ///
    /// # 参数
    ///
    /// * `main_window_label` - 主窗口标识符
    ///
    /// # 返回值
    ///
    /// 返回新的处理器实例
    pub fn new(main_window_label: impl Into<String>) -> Self {
        Self {
            name: "DefaultTrayHandler".to_string(),
            main_window_label: main_window_label.into(),
            toggle_window_on_left_click: true,
            show_menu_on_right_click: true,
        }
    }

    /// 设置是否在左键点击时切换窗口显示状态
    ///
    /// # 参数
    ///
    /// * `toggle` - 是否切换
    pub fn toggle_window_on_left_click(mut self, toggle: bool) -> Self {
        self.toggle_window_on_left_click = toggle;
        self
    }

    /// 设置是否在右键点击时显示菜单
    ///
    /// # 参数
    ///
    /// * `show` - 是否显示
    pub fn show_menu_on_right_click(mut self, show: bool) -> Self {
        self.show_menu_on_right_click = show;
        self
    }

    /// 切换窗口显示状态
    ///
    /// # 参数
    ///
    /// * `window` - 目标窗口
    ///
    /// # 返回值
    ///
    /// 如果操作成功则返回 Ok(())，否则返回错误
    async fn toggle_window_visibility(&self, window: &WebviewWindow) -> TrayResult<()> {
        let is_visible = window.is_visible().map_err(TrayError::from)?;

        if is_visible {
            // 如果窗口可见，则隐藏它
            window.hide().map_err(TrayError::from)?;
        } else {
            // 如果窗口隐藏，则显示并聚焦它
            window.show().map_err(TrayError::from)?;
            window.set_focus().map_err(TrayError::from)?;

            // 如果窗口被最小化，则恢复它
            if window.is_minimized().map_err(TrayError::from)? {
                window.unminimize().map_err(TrayError::from)?;
            }
        }

        Ok(())
    }

    /// 处理菜单项点击事件
    ///
    /// # 参数
    ///
    /// * `item_id` - 菜单项ID
    /// * `app_handle` - 应用句柄
    ///
    /// # 返回值
    ///
    /// 如果处理成功则返回 Ok(())，否则返回错误
    async fn handle_menu_click(&self, item_id: &str, app_handle: &AppHandle) -> TrayResult<()> {
        match item_id {
            "quit" => {
                // 退出应用
                app_handle.exit(0);
                Ok(())
            }
            "show" | "show_window" => {
                // 显示主窗口
                if let Some(window) = app_handle.get_webview_window(&self.main_window_label) {
                    window.show().map_err(TrayError::from)?;
                    window.set_focus().map_err(TrayError::from)?;
                    if window.is_minimized().map_err(TrayError::from)? {
                        window.unminimize().map_err(TrayError::from)?;
                    }
                }
                Ok(())
            }
            "hide" | "hide_window" => {
                // 隐藏主窗口
                if let Some(window) = app_handle.get_webview_window(&self.main_window_label) {
                    window.hide().map_err(TrayError::from)?;
                }
                Ok(())
            }
            "toggle" | "toggle_window" => {
                // 切换窗口显示状态
                if let Some(window) = app_handle.get_webview_window(&self.main_window_label) {
                    self.toggle_window_visibility(&window).await?;
                }
                Ok(())
            }
            "minimize" => {
                // 最小化窗口
                if let Some(window) = app_handle.get_webview_window(&self.main_window_label) {
                    window.minimize().map_err(TrayError::from)?;
                }
                Ok(())
            }
            "about" => {
                // 显示关于对话框（可以扩展）
                println!("关于 Secure Password 应用");
                Ok(())
            }
            _ => {
                // 未知菜单项，记录日志但不报错
                println!("未处理的菜单项点击: {}", item_id);
                Ok(())
            }
        }
    }
}

#[async_trait::async_trait]
impl TrayEventHandler for DefaultTrayHandler {
    async fn handle_event(&self, event: &TrayEvent, app_handle: &AppHandle) -> TrayResult<()> {
        match &event.event_type {
            TrayEventType::Click {
                click_type,
                position: _,
            } => {
                match click_type {
                    TrayClickType::LeftClick if self.toggle_window_on_left_click => {
                        // 左键点击切换窗口显示状态
                        if let Some(window) = app_handle.get_webview_window(&self.main_window_label)
                        {
                            self.toggle_window_visibility(&window).await?;
                        }
                    }
                    TrayClickType::RightClick if self.show_menu_on_right_click => {
                        // 右键点击显示菜单（由系统处理）
                        // 这里不需要额外处理，Tauri 会自动显示菜单
                    }
                    TrayClickType::DoubleClick => {
                        // 双击显示主窗口
                        if let Some(window) = app_handle.get_webview_window(&self.main_window_label)
                        {
                            window.show().map_err(TrayError::from)?;
                            window.set_focus().map_err(TrayError::from)?;
                            if window.is_minimized().map_err(TrayError::from)? {
                                window.unminimize().map_err(TrayError::from)?;
                            }
                        }
                    }
                    _ => {
                        // 其他点击类型暂不处理
                    }
                }
            }
            TrayEventType::MenuItemClick { item_id } => {
                // 处理菜单项点击
                self.handle_menu_click(item_id, app_handle).await?;
            }
            TrayEventType::MouseEnter => {
                // 鼠标进入托盘图标
                // 可以在这里实现工具提示等功能
            }
            TrayEventType::MouseLeave => {
                // 鼠标离开托盘图标
                // 可以在这里清理工具提示等
            }
            TrayEventType::Drop { paths } => {
                // 文件拖拽到托盘图标
                println!("文件拖拽到托盘: {:?}", paths);
                // 可以在这里实现文件处理逻辑
            }
        }

        Ok(())
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn should_handle(&self, event_type: &TrayEventType) -> bool {
        // 处理所有类型的事件
        match event_type {
            TrayEventType::Click { .. } => true,
            TrayEventType::MenuItemClick { .. } => true,
            TrayEventType::MouseEnter => false, // 暂不处理
            TrayEventType::MouseLeave => false, // 暂不处理
            TrayEventType::Drop { .. } => true,
        }
    }

    fn priority(&self) -> u32 {
        50 // 中等优先级
    }
}

/// 窗口管理事件处理器
///
/// 专门处理窗口相关的托盘事件。
#[derive(Debug)]
pub struct WindowManagerTrayHandler {
    /// 处理器名称
    name: String,
    /// 要管理的窗口标识符列表
    managed_windows: Vec<String>,
}

impl WindowManagerTrayHandler {
    /// 创建新的窗口管理处理器
    ///
    /// # 参数
    ///
    /// * `managed_windows` - 要管理的窗口标识符列表
    ///
    /// # 返回值
    ///
    /// 返回新的处理器实例
    pub fn new(managed_windows: Vec<String>) -> Self {
        Self {
            name: "WindowManagerTrayHandler".to_string(),
            managed_windows,
        }
    }

    /// 隐藏所有管理的窗口
    ///
    /// # 参数
    ///
    /// * `app_handle` - 应用句柄
    ///
    /// # 返回值
    ///
    /// 如果操作成功则返回 Ok(())，否则返回错误
    async fn hide_all_windows(&self, app_handle: &AppHandle) -> TrayResult<()> {
        for window_label in &self.managed_windows {
            if let Some(window) = app_handle.get_webview_window(window_label) {
                window.hide().map_err(TrayError::from)?;
            }
        }
        Ok(())
    }

    /// 显示所有管理的窗口
    ///
    /// # 参数
    ///
    /// * `app_handle` - 应用句柄
    ///
    /// # 返回值
    ///
    /// 如果操作成功则返回 Ok(())，否则返回错误
    async fn show_all_windows(&self, app_handle: &AppHandle) -> TrayResult<()> {
        for window_label in &self.managed_windows {
            if let Some(window) = app_handle.get_webview_window(window_label) {
                window.show().map_err(TrayError::from)?;
                if window.is_minimized().map_err(TrayError::from)? {
                    window.unminimize().map_err(TrayError::from)?;
                }
            }
        }
        Ok(())
    }
}

#[async_trait::async_trait]
impl TrayEventHandler for WindowManagerTrayHandler {
    async fn handle_event(&self, event: &TrayEvent, app_handle: &AppHandle) -> TrayResult<()> {
        match &event.event_type {
            TrayEventType::MenuItemClick { item_id } => {
                match item_id.as_str() {
                    "hide_all" => {
                        self.hide_all_windows(app_handle).await?;
                    }
                    "show_all" => {
                        self.show_all_windows(app_handle).await?;
                    }
                    _ => {
                        // 不处理其他菜单项
                    }
                }
            }
            _ => {
                // 不处理其他事件类型
            }
        }

        Ok(())
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn should_handle(&self, event_type: &TrayEventType) -> bool {
        if let TrayEventType::MenuItemClick { item_id } = event_type {
            item_id == "hide_all" || item_id == "show_all"
        } else {
            false
        }
    }

    fn priority(&self) -> u32 {
        60 // 较低优先级
    }
}

/// 创建标准托盘处理器集合
///
/// # 参数
///
/// * `main_window_label` - 主窗口标识符
///
/// # 返回值
///
/// 返回标准处理器列表
pub fn create_default_handlers(
    main_window_label: impl Into<String>,
) -> Vec<Box<dyn TrayEventHandler>> {
    let main_window = main_window_label.into();

    vec![
        Box::new(DefaultTrayHandler::new(main_window.clone())),
        Box::new(WindowManagerTrayHandler::new(vec![main_window])),
    ]
}

/// 托盘处理器特质
///
/// 为实现自定义托盘处理器提供基础接口。
pub trait TrayHandler: Send + Sync {
    /// 处理托盘点击事件
    ///
    /// # 参数
    ///
    /// * `app_handle` - 应用句柄
    ///
    /// # 返回值
    ///
    /// 如果处理成功则返回 Ok(())，否则返回错误
    fn handle_tray_click(&self, app_handle: &AppHandle) -> TrayResult<()>;

    /// 处理菜单项点击事件
    ///
    /// # 参数
    ///
    /// * `item_id` - 菜单项ID
    /// * `app_handle` - 应用句柄
    ///
    /// # 返回值
    ///
    /// 如果处理成功则返回 Ok(())，否则返回错误
    fn handle_menu_click(&self, item_id: &str, app_handle: &AppHandle) -> TrayResult<()>;
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tray::events::{TrayClickType, TrayEventType};

    #[tokio::test]
    async fn test_default_handler_creation() {
        let handler = DefaultTrayHandler::new("main")
            .toggle_window_on_left_click(true)
            .show_menu_on_right_click(true);

        assert_eq!(handler.name(), "DefaultTrayHandler");
        assert!(handler.should_handle(&TrayEventType::Click {
            click_type: TrayClickType::LeftClick,
            position: None,
        }));
        assert!(handler.should_handle(&TrayEventType::MenuItemClick {
            item_id: "quit".to_string(),
        }));
        assert!(!handler.should_handle(&TrayEventType::MouseEnter));
    }

    #[test]
    fn test_window_manager_handler() {
        let handler =
            WindowManagerTrayHandler::new(vec!["main".to_string(), "settings".to_string()]);

        assert_eq!(handler.name(), "WindowManagerTrayHandler");
        assert!(handler.should_handle(&TrayEventType::MenuItemClick {
            item_id: "hide_all".to_string(),
        }));
        assert!(handler.should_handle(&TrayEventType::MenuItemClick {
            item_id: "show_all".to_string(),
        }));
        assert!(!handler.should_handle(&TrayEventType::MenuItemClick {
            item_id: "quit".to_string(),
        }));
    }

    #[test]
    fn test_handler_priority() {
        let default_handler = DefaultTrayHandler::new("main");
        let window_handler = WindowManagerTrayHandler::new(vec!["main".to_string()]);

        assert_eq!(default_handler.priority(), 50);
        assert_eq!(window_handler.priority(), 60);
        assert!(default_handler.priority() < window_handler.priority());
    }

    #[test]
    fn test_create_default_handlers() {
        let handlers = create_default_handlers("main");
        assert_eq!(handlers.len(), 2);
    }
}
