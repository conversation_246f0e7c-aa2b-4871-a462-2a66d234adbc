/// 托盘管理器模块
///
/// 使用 Tauri 官方 API 实现的托盘管理器，
/// 提供完整的托盘功能管理和事件处理。
use crate::tray::{config::TrayConfig, menu::T<PERSON><PERSON><PERSON>u, Tray<PERSON>rror, TrayR<PERSON>ult};
use log::{debug, error, info, trace, warn};
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tauri::{
    tray::{TrayIcon, TrayIconBuilder, TrayIconEvent},
    AppHandle, Manager,
};

/// 事件频率限制器
///
/// 用于限制频繁事件的日志输出频率
#[derive(Debug)]
struct EventRateLimiter {
    /// 最后记录时间
    last_logged: Instant,
    /// 事件计数
    event_count: u64,
    /// 日志间隔
    log_interval: Duration,
}

impl EventRateLimiter {
    /// 创建新的频率限制器
    fn new(log_interval: Duration) -> Self {
        Self {
            last_logged: Instant::now(),
            event_count: 0,
            log_interval,
        }
    }

    /// 检查是否应该记录日志
    fn should_log(&mut self) -> bool {
        self.event_count += 1;
        let now = Instant::now();

        if now.duration_since(self.last_logged) >= self.log_interval {
            self.last_logged = now;
            true
        } else {
            false
        }
    }

    /// 获取事件计数
    fn get_count(&self) -> u64 {
        self.event_count
    }

    /// 重置计数器
    fn reset(&mut self) {
        self.event_count = 0;
        self.last_logged = Instant::now();
    }
}

/// 托盘管理器
///
/// 基于 Tauri 官方 API 的托盘管理器，负责创建、管理和控制系统托盘。
pub struct TrayManager {
    /// 托盘配置
    config: TrayConfig,
    /// 托盘图标实例
    tray_icon: Option<TrayIcon<tauri::Wry>>,
    /// 托盘菜单
    menu: Option<TrayMenu>,
    /// 是否已初始化
    initialized: bool,
    /// 主窗口标签
    main_window_label: String,
    /// Move 事件频率限制器
    move_event_limiter: Arc<Mutex<EventRateLimiter>>,
}

impl TrayManager {
    /// 创建新的托盘管理器
    ///
    /// # 参数
    ///
    /// * `config` - 托盘配置
    /// * `main_window_label` - 主窗口标签，默认为 "main"
    ///
    /// # 返回值
    ///
    /// 返回新的管理器实例
    pub fn new(config: TrayConfig) -> TrayResult<Self> {
        Self::with_window_label(config, "main")
    }

    /// 创建新的托盘管理器，指定主窗口标签
    ///
    /// # 参数
    ///
    /// * `config` - 托盘配置
    /// * `main_window_label` - 主窗口标签
    ///
    /// # 返回值
    ///
    /// 返回新的管理器实例
    pub fn with_window_label(
        config: TrayConfig,
        main_window_label: impl Into<String>,
    ) -> TrayResult<Self> {
        // 验证配置
        config.validate()?;

        Ok(Self {
            config,
            tray_icon: None,
            menu: None,
            initialized: false,
            main_window_label: main_window_label.into(),
            // Move 事件每 5 秒最多记录一次
            move_event_limiter: Arc::new(Mutex::new(EventRateLimiter::new(Duration::from_secs(5)))),
        })
    }

    /// 智能日志记录托盘事件
    ///
    /// # 参数
    ///
    /// * `event` - 托盘图标事件
    /// * `move_limiter` - Move 事件频率限制器
    fn log_tray_event_intelligently(
        event: &TrayIconEvent,
        move_limiter: Arc<Mutex<EventRateLimiter>>,
    ) {
        match event {
            TrayIconEvent::Click {
                button,
                button_state,
                position,
                ..
            } => {
                info!(
                    "托盘图标点击事件: 按钮={:?}, 状态={:?}, 位置=({:.1}, {:.1})",
                    button, button_state, position.x, position.y
                );
            }
            TrayIconEvent::DoubleClick {
                button, position, ..
            } => {
                info!(
                    "托盘图标双击事件: 按钮={:?}, 位置=({:.1}, {:.1})",
                    button, position.x, position.y
                );
            }
            TrayIconEvent::Enter { position, .. } => {
                debug!(
                    "鼠标进入托盘图标区域: 位置=({:.1}, {:.1})",
                    position.x, position.y
                );
            }
            TrayIconEvent::Leave { position, .. } => {
                debug!(
                    "鼠标离开托盘图标区域: 位置=({:.1}, {:.1})",
                    position.x, position.y
                );
            }
            TrayIconEvent::Move { position, .. } => {
                // 使用频率限制器来控制 Move 事件的日志输出
                if let Ok(mut limiter) = move_limiter.lock() {
                    if limiter.should_log() {
                        let count = limiter.get_count();
                        if count > 1 {
                            trace!(
                                "鼠标在托盘图标上移动 (过去5秒内共{}次): 当前位置=({:.1}, {:.1})",
                                count,
                                position.x,
                                position.y
                            );
                        } else {
                            trace!(
                                "鼠标在托盘图标上移动: 位置=({:.1}, {:.1})",
                                position.x,
                                position.y
                            );
                        }
                        limiter.reset();
                    }
                }
            }
            _ => {
                // 处理其他未知事件类型
                debug!("托盘图标其他事件: {:?}", event);
            }
        }
    }

    /// 初始化托盘
    ///
    /// # 参数
    ///
    /// * `app_handle` - Tauri 应用句柄
    ///
    /// # 返回值
    ///
    /// 如果初始化成功则返回 Ok(())，否则返回错误
    pub async fn initialize(&mut self, app_handle: &AppHandle) -> TrayResult<()> {
        if self.initialized {
            return Err(TrayError::configuration_error(
                "initialization",
                "托盘已经初始化",
            ));
        }

        // 创建托盘图标建造者
        let mut tray_builder = TrayIconBuilder::new().tooltip(&self.config.tooltip);

        // 设置图标
        if let Some(_icon_path) = &self.config.icon_path {
            if let Some(icon) = app_handle.default_window_icon() {
                tray_builder = tray_builder.icon(icon.clone());
            }
        }

        // 设置菜单
        if let Some(menu) = &self.menu {
            let tauri_menu = menu.to_tauri_menu(app_handle)?;
            tray_builder = tray_builder.menu(&tauri_menu);
        }

        // 克隆频率限制器以在闭包中使用
        let move_limiter = Arc::clone(&self.move_event_limiter);

        // 设置事件处理器
        tray_builder = tray_builder.on_tray_icon_event(move |_tray, event| {
            // 使用智能日志记录
            Self::log_tray_event_intelligently(&event, Arc::clone(&move_limiter));
        });

        // 设置菜单事件处理器
        if self.menu.is_some() {
            let main_window_label = self.main_window_label.clone();
            tray_builder = tray_builder.on_menu_event(move |app, event| {
                // 处理菜单点击事件
                info!("菜单事件: {:?}", event);

                // 获取菜单项ID
                let menu_id = event.id().as_ref();
                info!("处理菜单项点击: {}", menu_id);

                match menu_id {
                    "show_window" => {
                        // 显示主窗口
                        info!("显示主窗口: {}", main_window_label);
                        if let Some(window) = app.get_webview_window(&main_window_label) {
                            if let Err(e) = window.unminimize() {
                                warn!("恢复窗口失败: {}", e);
                            }
                            if let Err(e) = window.show() {
                                error!("显示窗口失败: {}", e);
                            } else {
                                info!("窗口已显示");
                            }
                            if let Err(e) = window.set_focus() {
                                warn!("设置窗口焦点失败: {}", e);
                            }
                        } else {
                            error!("找不到主窗口: {}", main_window_label);
                        }
                    }
                    "quit" => {
                        // 退出应用
                        info!("用户请求退出应用");
                        app.exit(0);
                    }
                    _ => {
                        warn!("未知的菜单项ID: {}", menu_id);
                    }
                }
            });
        }

        // 构建托盘图标
        let tray_icon = tray_builder
            .build(app_handle)
            .map_err(|e| TrayError::initialization_failed(format!("创建托盘图标失败: {}", e)))?;

        self.tray_icon = Some(tray_icon);
        self.initialized = true;

        Ok(())
    }

    /// 设置托盘菜单
    ///
    /// # 参数
    ///
    /// * `menu` - 托盘菜单
    ///
    /// # 返回值
    ///
    /// 如果设置成功则返回 Ok(())，否则返回错误
    pub fn set_menu(&mut self, menu: TrayMenu) -> TrayResult<()> {
        // 验证菜单
        menu.validate()?;
        self.menu = Some(menu);
        Ok(())
    }

    /// 设置托盘图标可见性
    ///
    /// # 参数
    ///
    /// * `visible` - 是否可见
    ///
    /// # 返回值
    ///
    /// 如果设置成功则返回 Ok(())，否则返回错误
    pub fn set_visible(&self, visible: bool) -> TrayResult<()> {
        if let Some(tray_icon) = &self.tray_icon {
            tray_icon
                .set_visible(visible)
                .map_err(|e| TrayError::icon_set_failed(format!("设置可见性失败: {}", e)))?;
        }
        Ok(())
    }

    /// 显示托盘图标
    ///
    /// # 返回值
    ///
    /// 如果显示成功则返回 Ok(())，否则返回错误
    pub fn show(&self) -> TrayResult<()> {
        self.set_visible(true)
    }

    /// 隐藏托盘图标
    ///
    /// # 返回值
    ///
    /// 如果隐藏成功则返回 Ok(())，否则返回错误
    pub fn hide(&self) -> TrayResult<()> {
        self.set_visible(false)
    }

    /// 获取托盘配置
    ///
    /// # 返回值
    ///
    /// 返回托盘配置的引用
    pub fn config(&self) -> &TrayConfig {
        &self.config
    }

    /// 检查托盘是否已初始化
    ///
    /// # 返回值
    ///
    /// 如果已初始化则返回 true
    pub fn is_initialized(&self) -> bool {
        self.initialized
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_tray_manager_creation() {
        let config = TrayConfig::builder()
            .title("测试应用")
            .tooltip("测试工具提示")
            .build()
            .unwrap();

        let manager = TrayManager::new(config).unwrap();
        assert!(!manager.is_initialized());
        assert_eq!(manager.config().title, "测试应用");
        assert_eq!(manager.config().tooltip, "测试工具提示");
    }

    #[test]
    fn test_menu_setting() {
        let config = TrayConfig::default();
        let mut manager = TrayManager::new(config).unwrap();

        let menu = TrayMenu::builder()
            .add_normal_item("test", "测试")
            .unwrap()
            .build()
            .unwrap();

        manager.set_menu(menu).unwrap();
    }

    #[test]
    fn test_event_rate_limiter() {
        let mut limiter = EventRateLimiter::new(Duration::from_millis(100));

        // 第一次调用应该返回 false（因为还没有达到间隔）
        assert!(!limiter.should_log());
        assert_eq!(limiter.get_count(), 1);

        // 快速连续调用应该返回 false
        assert!(!limiter.should_log());
        assert_eq!(limiter.get_count(), 2);

        // 等待超过间隔时间
        std::thread::sleep(Duration::from_millis(150));

        // 现在应该返回 true
        assert!(limiter.should_log());
        assert_eq!(limiter.get_count(), 3);

        // 重置后计数应该为 0
        limiter.reset();
        assert_eq!(limiter.get_count(), 0);
    }

    #[test]
    fn test_event_rate_limiter_reset() {
        let mut limiter = EventRateLimiter::new(Duration::from_secs(1));

        // 增加一些计数
        limiter.should_log();
        limiter.should_log();
        limiter.should_log();
        assert_eq!(limiter.get_count(), 3);

        // 重置
        limiter.reset();
        assert_eq!(limiter.get_count(), 0);
    }
}
