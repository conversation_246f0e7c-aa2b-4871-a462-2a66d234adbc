/**
 * 应用主入口组件
 * 提供认证状态管理和平台自适应路由
 */

import React from 'react';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider, useAuthState } from './contexts';
import { UserProvider, useUser } from './contexts/UserContext';
import { HybridCredentialsProvider } from './contexts/HybridCredentialsContext';
import { PlatformRouter } from './shared/components/PlatformRouter';
import { useOrmInitialization } from './hooks/useOrmInitialization';

// 桌面端组件
import { AppLayout } from './components';
import { AuthPage as DesktopAuthPage, MainPage as DesktopMainPage } from './pages';
import { OrmInitializationStatusComponent } from './components/OrmInitializationStatus';

// 移动端组件
import { MobileMainPage } from './mobile/pages/MobileMainPage';
import { MobileAuthPage } from './mobile/pages/MobileAuthPage';

import './App.css';

/**
 * 认证页面平台路由组件
 */
const AuthPageRouter: React.FC = () => {
  return (
    <PlatformRouter
      desktopComponent={DesktopAuthPage}
      mobileComponent={MobileAuthPage}
    />
  );
};

/**
 * 主页面平台路由组件
 */
const MainPageRouter: React.FC = () => {
  return (
    <HybridCredentialsProvider>
      <PlatformRouter
        desktopComponent={DesktopMainPage}
        mobileComponent={MobileMainPage}
      />
    </HybridCredentialsProvider>
  );
};

/**
 * 应用内容组件
 * 根据认证状态显示不同的页面
 */
const AppContent: React.FC = () => {
  const { loading: authLoading } = useAuthState();
  const { isLoggedIn, isLoading: userLoading, user } = useUser();
  const { isInitializing: ormInitializing, isError: ormError } = useOrmInitialization();

  console.log('ormInitializing', ormInitializing);

  // 显示加载状态（任一状态正在加载时）
  const loading = authLoading || userLoading;
  
  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <div className="text-gray-600">正在检查认证状态...</div>
          </div>
        </div>
      </AppLayout>
    );
  }

  // 使用用户登录状态作为主要的认证判断
  // 如果用户已登录，显示主页面；否则显示认证页面
  const shouldShowMainPage = isLoggedIn && user;

  return (
    <div className="app-container">
      {/* ORM 初始化状态显示 - 仅在桌面端显示 */}
      {(ormInitializing || ormError) && (
        <PlatformRouter
          desktopComponent={() => (
            <div style={{ margin: '16px', marginBottom: '8px' }}>
              <OrmInitializationStatusComponent />
            </div>
          )}
          mobileComponent={() => null}
        />
      )}
      
      {shouldShowMainPage ? (
        <MainPageRouter />
      ) : (
        <AuthPageRouter />
      )}
    </div>
  );
};

/**
 * 应用主组件
 */
const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <AntdApp>
        <UserProvider>
          <AuthProvider>
            <AppContent />
          </AuthProvider>
        </UserProvider>
      </AntdApp>
    </ConfigProvider>
  );
};

export default App;
