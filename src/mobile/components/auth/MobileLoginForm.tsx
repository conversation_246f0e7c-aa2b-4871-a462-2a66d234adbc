/**
 * 移动端登录表单组件
 * 针对移动端优化的用户登录表单
 */

import React, { useState } from 'react';
import {
  Form,
  Input,
  Button,
  Checkbox,
  Alert,
  message,
  Divider,
  Typography
} from 'antd';
import {
  MailOutlined,
  LockOutlined,
  SafetyOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone
} from '@ant-design/icons';
import { LoginFormData } from '../../../types';
import { useAuthActions } from '../../../contexts';

const { Link } = Typography;

interface MobileLoginFormProps {
  onSuccess?: () => void;
}

/**
 * 移动端登录表单组件
 */
export const MobileLoginForm: React.FC<MobileLoginFormProps> = ({ onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const { login } = useAuthActions();

  /**
   * 提交登录表单
   */
  const handleSubmit = async (values: LoginFormData) => {
    try {
      setLoading(true);
      setError('');

      const response = await login(values);

      if (response.success) {
        message.success('登录成功！');
        onSuccess?.();
      } else {
        setError(response.error || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      const errorMessage = error instanceof Error ? error.message : '登录过程出现错误';
      setError(errorMessage);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full">
      {error && (
        <Alert
          message="登录失败"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError('')}
          className="mb-4 rounded-lg"
        />
      )}

      <Form
        form={form}
        name="mobile-login"
        onFinish={handleSubmit}
        layout="vertical"
        autoComplete="off"
        size="middle"
        className="space-y-4"
      >
        {/* 邮箱/手机号 */}
        <Form.Item
          name="contact"
          label="邮箱或手机号"
          rules={[
            { required: true, message: '请输入邮箱或手机号' },
            {
              validator: (_, value) => {
                if (!value) return Promise.resolve();
                
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                const phonePattern = /^1[3-9]\d{9}$/;
                
                if (emailPattern.test(value) || phonePattern.test(value)) {
                  return Promise.resolve();
                }
                
                return Promise.reject(new Error('请输入有效的邮箱地址或手机号码'));
              }
            }
          ]}
        >
          <Input 
            prefix={<MailOutlined />} 
            placeholder="请输入邮箱地址或手机号码"
            className="rounded-lg h-11"
          />
        </Form.Item>

        {/* 密码 */}
        <Form.Item
          name="password"
          label="密码"
          rules={[
            { required: true, message: '请输入密码' }
          ]}
        >
          <Input.Password 
            prefix={<LockOutlined />}
            placeholder="请输入密码"
            className="rounded-lg h-11"
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        {/* 验证码 */}
        <Form.Item
          name="verificationCode"
          label="验证码（可选）"
          rules={[
            {
              validator: (_, value) => {
                if (!value) return Promise.resolve();
                if (!/^\d{6}$/.test(value)) {
                  return Promise.reject(new Error('验证码为6位数字'));
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <Input 
            prefix={<SafetyOutlined />}
            placeholder="如需要请输入6位验证码"
            className="rounded-lg h-11"
            maxLength={6}
          />
        </Form.Item>

        {/* 记住我 */}
        <Form.Item name="rememberMe" valuePropName="checked" className="mb-6">
          <Checkbox className="text-sm">记住登录状态</Checkbox>
        </Form.Item>

        <Divider className="my-4" />

        {/* 提交按钮 */}
        <Form.Item className="mb-0">
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={loading}
            block
            size="large"
            className="rounded-lg h-12 font-medium"
          >
            登录
          </Button>
        </Form.Item>
      </Form>

      {/* 底部链接 */}
      <div className="text-center mt-6 space-y-3">
        <div className="text-sm text-gray-500 dark:text-gray-400">
          还没有账户？
          <Link className="ml-1 text-blue-500 dark:text-blue-400">立即注册</Link>
        </div>
        <div>
          <Link className="text-xs text-gray-400 dark:text-gray-500">忘记密码？</Link>
        </div>
      </div>
    </div>
  );
};

export default MobileLoginForm; 