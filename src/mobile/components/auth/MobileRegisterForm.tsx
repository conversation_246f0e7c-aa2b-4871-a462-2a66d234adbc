/**
 * 移动端注册表单组件
 * 针对移动端优化的用户注册表单
 */

import React, { useState, useCallback } from 'react';
import {
  Form,
  Input,
  Button,
  Progress,
  Alert,
  message,
  Space,
  Divider
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  LockOutlined,
  SafetyOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone
} from '@ant-design/icons';
import { RegisterFormData, RegistrationType, PasswordStrengthResult } from '../../../types';
import { 
  registerCompleteFlow, 
  sendVerificationCode, 
  validatePasswordStrength,
  checkContactAvailability
} from '../../../api';

interface MobileRegisterFormProps {
  onSuccess?: () => void;
}

/**
 * 智能检测联系方式类型
 */
const detectContactType = (contact: string): RegistrationType => {
  return contact.includes('@') ? 'Email' : 'Phone';
};

/**
 * 根据联系方式生成默认用户名
 */
const generateUsername = (contact: string): string => {
  if (contact.includes('@')) {
    return contact.split('@')[0];
  } else {
    return `user_${contact.slice(-6)}`;
  }
};

/**
 * 移动端注册表单组件
 */
export const MobileRegisterForm: React.FC<MobileRegisterFormProps> = ({ onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrengthResult | null>(null);
  const [countdown, setCountdown] = useState(0);
  const [error, setError] = useState<string>('');

  // 倒计时逻辑
  React.useEffect(() => {
    let timer: number;
    if (countdown > 0) {
      timer = window.setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  /**
   * 检验密码强度
   */
  const checkPasswordStrength = useCallback(async (password: string) => {
    if (!password) {
      setPasswordStrength(null);
      return;
    }

    try {
      const response = await validatePasswordStrength(password);
      if (response.success && response.data) {
        setPasswordStrength(response.data);
      }
    } catch (error) {
      console.error('密码强度检验失败:', error);
    }
  }, []);

  /**
   * 发送验证码
   */
  const handleSendVerificationCode = async () => {
    try {
      const contact = form.getFieldValue('contact');

      if (!contact) {
        message.warning('请先填写邮箱或手机号');
        return;
      }

      const contactType = detectContactType(contact);

      // 检查联系方式格式和可用性
      const availabilityResponse = await checkContactAvailability(contact, contactType);
      if (!availabilityResponse.success || !availabilityResponse.data?.is_available) {
        message.error(availabilityResponse.data?.message || '该邮箱或手机号已被使用');
        return;
      }

      setLoading(true);
      const response = await sendVerificationCode(contact, contactType);
      
      if (response.success) {
        setCountdown(60);
        const typeText = contactType === 'Phone' ? '短信' : '邮箱';
        message.success(`验证码已发送到您的${typeText}，请查收`);
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      message.error('发送验证码失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 提交注册表单
   */
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      setError('');

      const contact = values.contact;
      const contactType = detectContactType(contact);
      const username = generateUsername(contact);

      // 验证密码确认
      if (values.password !== values.confirmPassword) {
        message.error('两次输入的密码不一致');
        return;
      }

      // 构造注册数据
      const registerData: RegisterFormData = {
        username,
        contact,
        registrationType: contactType,
        password: values.password,
        confirmPassword: values.confirmPassword,
        verificationCode: values.verificationCode,
        passwordHint: values.passwordHint
      };

      // 执行注册流程
      const response = await registerCompleteFlow(registerData);

      if (response.success && response.data?.remote_success) {
        message.success('注册成功！');
        onSuccess?.();
      } else {
        setError(response.error || '注册失败');
      }
    } catch (error) {
      console.error('注册失败:', error);
      const errorMessage = error instanceof Error ? error.message : '注册过程出现错误';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取密码强度颜色
   */
  const getPasswordStrengthColor = (strength: number): string => {
    if (strength >= 80) return '#52c41a';
    if (strength >= 60) return '#faad14';
    if (strength >= 40) return '#fa8c16';
    return '#ff4d4f';
  };

  /**
   * 获取密码强度文本
   */
  const getPasswordStrengthText = (strength: number): string => {
    if (strength >= 80) return '强';
    if (strength >= 60) return '中等';
    if (strength >= 40) return '一般';
    return '弱';
  };

  return (
    <div className="w-full">
      {error && (
        <Alert
          message="注册失败"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError('')}
          className="mb-4 rounded-lg"
        />
      )}

      <Form
        form={form}
        name="mobile-register"
        onFinish={handleSubmit}
        layout="vertical"
        autoComplete="off"
        size="middle"
        className="space-y-3"
      >
        {/* 邮箱/手机号 */}
        <Form.Item
          name="contact"
          label="邮箱或手机号"
          rules={[
            { required: true, message: '请输入邮箱或手机号' },
            {
              validator: (_, value) => {
                if (!value) return Promise.resolve();
                
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                const phonePattern = /^1[3-9]\d{9}$/;
                
                if (emailPattern.test(value) || phonePattern.test(value)) {
                  return Promise.resolve();
                }
                
                return Promise.reject(new Error('请输入有效的邮箱地址或手机号码'));
              }
            }
          ]}
        >
          <Input 
            prefix={<MailOutlined />} 
            placeholder="请输入邮箱地址或手机号码"
            className="rounded-lg h-11"
          />
        </Form.Item>

        {/* 密码 */}
        <Form.Item
          name="password"
          label="密码"
          rules={[
            { required: true, message: '请输入密码' },
            { min: 8, message: '密码至少8位字符' }
          ]}
        >
          <Input.Password 
            prefix={<LockOutlined />}
            placeholder="请输入密码（至少8位）"
            className="rounded-lg h-11"
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            onChange={(e) => checkPasswordStrength(e.target.value)}
          />
        </Form.Item>

        {/* 密码强度指示器 */}
        {passwordStrength && (
          <div className="mb-4">
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs text-gray-600">密码强度</span>
              <span className="text-xs font-medium" style={{ color: getPasswordStrengthColor(passwordStrength.strength) }}>
                {getPasswordStrengthText(passwordStrength.strength)}
              </span>
            </div>
            <Progress
              percent={passwordStrength.strength}
              strokeColor={getPasswordStrengthColor(passwordStrength.strength)}
              showInfo={false}
              size="small"
            />
          </div>
        )}

        {/* 确认密码 */}
        <Form.Item
          name="confirmPassword"
          label="确认密码"
          dependencies={['password']}
          rules={[
            { required: true, message: '请确认密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password 
            prefix={<LockOutlined />}
            placeholder="请再次输入密码"
            className="rounded-lg h-11"
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        {/* 验证码 */}
        <Form.Item
          name="verificationCode"
          label="验证码"
          rules={[
            { required: true, message: '请输入验证码' },
            { len: 6, message: '验证码为6位数字' }
          ]}
        >
          <Space.Compact style={{ display: 'flex' }}>
            <Input 
              prefix={<SafetyOutlined />}
              placeholder="请输入6位验证码"
              className="rounded-l-lg h-11"
              maxLength={6}
            />
            <Button 
              type="primary"
              onClick={handleSendVerificationCode}
              disabled={countdown > 0}
              loading={loading}
              className="rounded-r-lg h-11 px-3"
              style={{ minWidth: '80px' }}
            >
              {countdown > 0 ? `${countdown}s` : '发送'}
            </Button>
          </Space.Compact>
        </Form.Item>

        {/* 密码提示 */}
        <Form.Item
          name="passwordHint"
          label="密码提示（可选）"
        >
          <Input 
            prefix={<UserOutlined />}
            placeholder="帮助您记住密码的提示"
            className="rounded-lg h-11"
            maxLength={50}
          />
        </Form.Item>

        <Divider className="my-4" />

        {/* 提交按钮 */}
        <Form.Item className="mb-0">
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={loading}
            block
            size="large"
            className="rounded-lg h-12 font-medium"
          >
            创建账户
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default MobileRegisterForm; 