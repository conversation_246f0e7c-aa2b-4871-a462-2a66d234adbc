/**
 * 移动端布局组件测试
 */

import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { MobileLayout } from './MobileLayout';

describe('MobileLayout', () => {
  it('应该渲染子组件', () => {
    render(
      <MobileLayout>
        <div data-testid="test-content">测试内容</div>
      </MobileLayout>
    );

    expect(screen.getByTestId('test-content')).toBeInTheDocument();
  });

  it('应该应用移动端样式类', () => {
    const { container } = render(
      <MobileLayout>
        <div>内容</div>
      </MobileLayout>
    );

    const layoutElement = container.firstChild as HTMLElement;
    expect(layoutElement).toHaveClass('mobile-layout');
  });

  it('应该支持自定义类名', () => {
    const { container } = render(
      <MobileLayout className="custom-class">
        <div>内容</div>
      </MobileLayout>
    );

    const layoutElement = container.firstChild as HTMLElement;
    expect(layoutElement).toHaveClass('mobile-layout', 'custom-class');
  });

  it('应该支持全屏模式', () => {
    const { container } = render(
      <MobileLayout fullScreen>
        <div>内容</div>
      </MobileLayout>
    );

    const layoutElement = container.firstChild as HTMLElement;
    expect(layoutElement).toHaveClass('mobile-layout--fullscreen');
  });

  it('应该支持安全区域适配', () => {
    const { container } = render(
      <MobileLayout safeArea>
        <div>内容</div>
      </MobileLayout>
    );

    const layoutElement = container.firstChild as HTMLElement;
    expect(layoutElement).toHaveClass('mobile-layout--safe-area');
  });

  it('应该渲染顶部导航栏', () => {
    render(
      <MobileLayout 
        header={<div data-testid="header">顶部导航</div>}
      >
        <div>内容</div>
      </MobileLayout>
    );

    expect(screen.getByTestId('header')).toBeInTheDocument();
  });

  it('应该渲染底部导航栏', () => {
    render(
      <MobileLayout 
        footer={<div data-testid="footer">底部导航</div>}
      >
        <div>内容</div>
      </MobileLayout>
    );

    expect(screen.getByTestId('footer')).toBeInTheDocument();
  });
}); 