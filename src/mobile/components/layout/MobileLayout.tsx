/**
 * 移动端布局组件
 * 提供移动端专用的布局结构和样式
 */

import React from 'react';
import { cn } from '../../../lib/utils';

export interface MobileLayoutProps {
  /** 子组件 */
  children: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 顶部导航栏 */
  header?: React.ReactNode;
  /** 底部导航栏 */
  footer?: React.ReactNode;
  /** 是否全屏显示 */
  fullScreen?: boolean;
  /** 是否启用安全区域适配 */
  safeArea?: boolean;
  /** 背景色主题 */
  background?: 'default' | 'primary' | 'secondary';
}

/**
 * 移动端布局组件
 */
export const MobileLayout: React.FC<MobileLayoutProps> = ({
  children,
  className,
  header,
  footer,
  fullScreen = false,
  safeArea = true,
  background = 'default',
}) => {
  const layoutClasses = cn(
    'mobile-layout',
    'flex flex-col min-h-screen w-full',
    'transition-all duration-300 ease-in-out',
    {
      'mobile-layout--fullscreen': fullScreen,
      'mobile-layout--safe-area': safeArea,
      'bg-background': background === 'default',
      'bg-primary': background === 'primary',
      'bg-secondary': background === 'secondary',
    },
    // 安全区域适配
    safeArea && [
      'pt-safe-top pb-safe-bottom',
      'pl-safe-left pr-safe-right',
    ],
    className
  );

  const contentClasses = cn(
    'mobile-layout__content',
    'flex-1 flex flex-col',
    'overflow-hidden',
    {
      'h-screen': fullScreen,
    }
  );

  return (
    <div className={layoutClasses}>
      {/* 顶部导航栏 */}
      {header && (
        <header className="mobile-layout__header flex-shrink-0 z-50">
          {header}
        </header>
      )}

      {/* 主要内容区域 */}
      <main className={contentClasses}>
        {children}
      </main>

      {/* 底部导航栏 */}
      {footer && (
        <footer className="mobile-layout__footer flex-shrink-0 z-50">
          {footer}
        </footer>
      )}
    </div>
  );
}; 