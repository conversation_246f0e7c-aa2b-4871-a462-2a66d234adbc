/**
 * 移动端导航栏组件测试
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { MobileNavBar } from './MobileNavBar';

const mockNavigationItems = [
  {
    key: 'vault',
    label: '保险柜',
    icon: '🔒',
    path: '/vault',
  },
  {
    key: 'favorites',
    label: '收藏夹',
    icon: '⭐',
    path: '/favorites',
  },
  {
    key: 'settings',
    label: '设置',
    icon: '⚙️',
    path: '/settings',
  },
];

describe('MobileNavBar', () => {
  it('应该渲染所有导航项', () => {
    render(
      <MobileNavBar 
        items={mockNavigationItems}
        activeKey="vault"
        onItemClick={() => {}}
      />
    );

    expect(screen.getByText('保险柜')).toBeInTheDocument();
    expect(screen.getByText('收藏夹')).toBeInTheDocument();
    expect(screen.getByText('设置')).toBeInTheDocument();
  });

  it('应该高亮当前激活的导航项', () => {
    render(
      <MobileNavBar 
        items={mockNavigationItems}
        activeKey="favorites"
        onItemClick={() => {}}
      />
    );

    const favoriteItem = screen.getByText('收藏夹').closest('button');
    expect(favoriteItem).toHaveClass('mobile-nav-item--active');
  });

  it('应该在点击导航项时调用回调函数', () => {
    const onItemClick = vi.fn();
    
    render(
      <MobileNavBar 
        items={mockNavigationItems}
        activeKey="vault"
        onItemClick={onItemClick}
      />
    );

    fireEvent.click(screen.getByText('设置'));
    
    expect(onItemClick).toHaveBeenCalledWith('settings', '/settings');
  });

  it('应该支持徽章显示', () => {
    const itemsWithBadge = [
      {
        ...mockNavigationItems[0],
        badge: 5,
      },
    ];

    render(
      <MobileNavBar 
        items={itemsWithBadge}
        activeKey="vault"
        onItemClick={() => {}}
      />
    );

    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('应该支持禁用状态', () => {
    const itemsWithDisabled = [
      {
        ...mockNavigationItems[0],
        disabled: true,
      },
    ];

    render(
      <MobileNavBar 
        items={itemsWithDisabled}
        activeKey="vault"
        onItemClick={() => {}}
      />
    );

    const disabledItem = screen.getByText('保险柜').closest('button');
    expect(disabledItem).toBeDisabled();
  });

  it('应该支持自定义样式', () => {
    const { container } = render(
      <MobileNavBar 
        items={mockNavigationItems}
        activeKey="vault"
        onItemClick={() => {}}
        className="custom-nav"
      />
    );

    expect(container.firstChild).toHaveClass('custom-nav');
  });

  it('应该支持不同的主题', () => {
    const { container } = render(
      <MobileNavBar 
        items={mockNavigationItems}
        activeKey="vault"
        onItemClick={() => {}}
        theme="dark"
      />
    );

    expect(container.firstChild).toHaveClass('mobile-nav-bar--dark');
  });
}); 