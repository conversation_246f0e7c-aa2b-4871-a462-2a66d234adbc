/**
 * 移动端导航栏组件
 * 提供底部导航栏功能
 */

import React from 'react';
import { cn } from '../../../lib/utils';

export interface MobileNavItem {
  /** 唯一标识 */
  key: string;
  /** 显示标签 */
  label: string;
  /** 图标 */
  icon: React.ReactNode | string;
  /** 路径 */
  path: string;
  /** 徽章数量 */
  badge?: number;
  /** 是否禁用 */
  disabled?: boolean;
}

export interface MobileNavBarProps {
  /** 导航项列表 */
  items: MobileNavItem[];
  /** 当前激活的导航项 */
  activeKey: string;
  /** 点击导航项的回调 */
  onItemClick: (key: string, path: string) => void;
  /** 自定义类名 */
  className?: string;
  /** 主题 */
  theme?: 'light' | 'dark';
  /** 是否显示标签 */
  showLabels?: boolean;
}

/**
 * 移动端导航栏组件
 */
export const MobileNavBar: React.FC<MobileNavBarProps> = ({
  items,
  activeKey,
  onItemClick,
  className,
  theme = 'light',
  showLabels = true,
}) => {
  const navBarClasses = cn(
    'mobile-nav-bar',
    'flex items-center justify-around',
    'bg-background border-t border-border',
    'px-2 py-1 safe-area-inset-bottom',
    {
      'mobile-nav-bar--light': theme === 'light',
      'mobile-nav-bar--dark': theme === 'dark',
      'bg-gray-900 border-gray-700': theme === 'dark',
    },
    className
  );

  const renderNavItem = (item: MobileNavItem) => {
    const isActive = item.key === activeKey;
    
    const itemClasses = cn(
      'mobile-nav-item',
      'flex flex-col items-center justify-center',
      'min-w-0 flex-1 px-1 py-2',
      'transition-all duration-200 ease-in-out',
      'relative rounded-lg',
      {
        'mobile-nav-item--active': isActive,
        'text-primary': isActive && theme === 'light',
        'text-blue-400': isActive && theme === 'dark',
        'text-muted-foreground': !isActive && theme === 'light',
        'text-gray-400': !isActive && theme === 'dark',
        'opacity-50 cursor-not-allowed': item.disabled,
        'hover:bg-accent': !item.disabled && !isActive,
        'hover:bg-gray-800': !item.disabled && !isActive && theme === 'dark',
      }
    );

    const iconClasses = cn(
      'mobile-nav-item__icon',
      'text-xl mb-1',
      'transition-transform duration-200',
      {
        'scale-110': isActive,
      }
    );

    const labelClasses = cn(
      'mobile-nav-item__label',
      'text-xs font-medium',
      'truncate max-w-full',
      'leading-tight'
    );

    const handleClick = () => {
      if (!item.disabled) {
        onItemClick(item.key, item.path);
      }
    };

    return (
      <button
        key={item.key}
        className={itemClasses}
        onClick={handleClick}
        disabled={item.disabled}
        type="button"
        aria-label={item.label}
        aria-current={isActive ? 'page' : undefined}
      >
        {/* 图标容器 */}
        <div className="relative">
          <div className={iconClasses}>
            {typeof item.icon === 'string' ? (
              <span>{item.icon}</span>
            ) : (
              item.icon
            )}
          </div>
          
          {/* 徽章 */}
          {item.badge && item.badge > 0 && (
            <div className={cn(
              'absolute -top-1 -right-1',
              'min-w-[16px] h-4 px-1',
              'bg-red-500 text-white',
              'text-xs font-bold',
              'rounded-full',
              'flex items-center justify-center',
              'leading-none'
            )}>
              {item.badge > 99 ? '99+' : item.badge}
            </div>
          )}
        </div>

        {/* 标签 */}
        {showLabels && (
          <span className={labelClasses}>
            {item.label}
          </span>
        )}

        {/* 激活指示器 */}
        {isActive && (
          <div className={cn(
            'absolute top-0 left-1/2 transform -translate-x-1/2',
            'w-1 h-1 rounded-full',
            'bg-primary',
            theme === 'dark' && 'bg-blue-400'
          )} />
        )}
      </button>
    );
  };

  return (
    <nav className={navBarClasses} role="navigation" aria-label="主导航">
      {items.map(renderNavItem)}
    </nav>
  );
}; 