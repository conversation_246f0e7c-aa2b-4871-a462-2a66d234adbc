/**
 * 移动端收藏夹视图组件
 * 显示收藏的密码列表
 */

import React from 'react';
import { useHybridCredentials } from '../../../contexts/HybridCredentialsContext';
import { LoginCredentialOutput } from '../../../types';
import { cn } from '../../../lib/utils';

/**
 * 移动端收藏夹视图组件
 */
export const MobileFavoritesView: React.FC = () => {
  const { favoriteCredentials, isLoading, error } = useHybridCredentials();

  /**
   * 渲染收藏凭据卡片
   */
  const renderFavoriteCard = (credential: LoginCredentialOutput) => (
    <div
      key={credential.id}
      className={cn(
        'p-4 bg-card border border-border rounded-lg',
        'hover:shadow-md transition-all duration-200',
        'active:scale-95'
      )}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          {/* 网站图标 */}
          <div className="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center flex-shrink-0">
            <span className="text-white font-bold text-sm">
              {credential.name.charAt(0).toUpperCase()}
            </span>
          </div>
          
          {/* 凭据信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h3 className="font-medium text-foreground truncate">
                {credential.name}
              </h3>
              <span className="text-yellow-500 text-sm">⭐</span>
            </div>
            <p className="text-sm text-muted-foreground truncate">
              {credential.username || credential.website || '无用户名'}
            </p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-2 flex-shrink-0">
          <button
            className="p-2 hover:bg-accent rounded-full transition-colors"
            aria-label="取消收藏"
          >
            💔
          </button>
          <button
            className="p-2 hover:bg-accent rounded-full transition-colors"
            aria-label="更多操作"
          >
            ⋯
          </button>
        </div>
      </div>
    </div>
  );

  /**
   * 渲染空状态
   */
  const renderEmptyState = () => (
    <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
      <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
        <span className="text-2xl">⭐</span>
      </div>
      <h3 className="text-lg font-medium text-foreground mb-2">
        还没有收藏的密码
      </h3>
      <p className="text-muted-foreground mb-6 max-w-sm">
        在密码列表中点击星星图标来收藏您常用的密码
      </p>
      <button className="px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors">
        去添加收藏
      </button>
    </div>
  );

  /**
   * 渲染加载状态
   */
  const renderLoadingState = () => (
    <div className="flex-1 flex items-center justify-center">
      <div className="text-center">
        <div className="w-8 h-8 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-muted-foreground">加载收藏夹...</p>
      </div>
    </div>
  );

  /**
   * 渲染错误状态
   */
  const renderErrorState = () => (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="text-center">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
          <span className="text-2xl">⚠️</span>
        </div>
        <h3 className="text-lg font-medium text-foreground mb-2">
          加载失败
        </h3>
        <p className="text-muted-foreground mb-4">
          {error || '无法加载收藏夹数据'}
        </p>
        <button className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
          重试
        </button>
      </div>
    </div>
  );

  if (isLoading) {
    return renderLoadingState();
  }

  if (error) {
    return renderErrorState();
  }

  if (favoriteCredentials.length === 0) {
    return renderEmptyState();
  }

  return (
    <div className="flex flex-col h-full">
      {/* 头部信息 */}
      <div className="p-4 bg-background border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-foreground">收藏夹</h2>
            <p className="text-sm text-muted-foreground">
              {favoriteCredentials.length} 个收藏的密码
            </p>
          </div>
          <div className="text-2xl">⭐</div>
        </div>
      </div>
      
      {/* 收藏列表 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-3">
          {favoriteCredentials.map(renderFavoriteCard)}
        </div>
      </div>
    </div>
  );
}; 