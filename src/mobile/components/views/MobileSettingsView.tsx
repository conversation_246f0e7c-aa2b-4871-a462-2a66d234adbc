/**
 * 移动端设置视图组件
 * 提供应用设置和用户管理功能
 */

import React from 'react';
import { useUser } from '../../../contexts/UserContext';
import { cn } from '../../../lib/utils';

interface SettingItem {
  id: string;
  title: string;
  description?: string;
  icon: string;
  type: 'navigation' | 'toggle' | 'action';
  value?: boolean;
  onClick?: () => void;
}

/**
 * 移动端设置视图组件
 */
export const MobileSettingsView: React.FC = () => {
  const { user, logout } = useUser();

  // 设置项配置
  const settingsSections = [
    {
      title: '账户',
      items: [
        {
          id: 'profile',
          title: '个人资料',
          description: user?.contact || '未登录',
          icon: '👤',
          type: 'navigation' as const,
          onClick: () => console.log('打开个人资料'),
        },
        {
          id: 'security',
          title: '安全设置',
          description: '密码、生物识别等',
          icon: '🔐',
          type: 'navigation' as const,
          onClick: () => console.log('打开安全设置'),
        },
      ],
    },
    {
      title: '应用设置',
      items: [
        {
          id: 'theme',
          title: '深色模式',
          description: '切换应用主题',
          icon: '🌙',
          type: 'toggle' as const,
          value: false,
          onClick: () => console.log('切换主题'),
        },
        {
          id: 'notifications',
          title: '通知',
          description: '管理推送通知',
          icon: '🔔',
          type: 'navigation' as const,
          onClick: () => console.log('打开通知设置'),
        },
        {
          id: 'backup',
          title: '备份与同步',
          description: '数据备份设置',
          icon: '☁️',
          type: 'navigation' as const,
          onClick: () => console.log('打开备份设置'),
        },
      ],
    },
    {
      title: '其他',
      items: [
        {
          id: 'help',
          title: '帮助与支持',
          description: '常见问题、联系我们',
          icon: '❓',
          type: 'navigation' as const,
          onClick: () => console.log('打开帮助'),
        },
        {
          id: 'about',
          title: '关于',
          description: '版本信息、隐私政策',
          icon: 'ℹ️',
          type: 'navigation' as const,
          onClick: () => console.log('打开关于'),
        },
        {
          id: 'logout',
          title: '退出登录',
          description: '安全退出当前账户',
          icon: '🚪',
          type: 'action' as const,
          onClick: logout,
        },
      ],
    },
  ];

  /**
   * 渲染设置项
   */
  const renderSettingItem = (item: SettingItem) => (
    <button
      key={item.id}
      onClick={item.onClick}
      className={cn(
        'w-full p-4 bg-card border border-border rounded-lg',
        'hover:shadow-md transition-all duration-200',
        'active:scale-95',
        'text-left'
      )}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          {/* 图标 */}
          <div className="w-10 h-10 bg-accent rounded-lg flex items-center justify-center flex-shrink-0">
            <span className="text-lg">{item.icon}</span>
          </div>
          
          {/* 设置项信息 */}
          <div className="flex-1 min-w-0">
            <h3 className={cn(
              'font-medium truncate',
              item.type === 'action' && item.id === 'logout' 
                ? 'text-red-600' 
                : 'text-foreground'
            )}>
              {item.title}
            </h3>
            {item.description && (
              <p className="text-sm text-muted-foreground truncate">
                {item.description}
              </p>
            )}
          </div>
        </div>

        {/* 右侧控件 */}
        <div className="flex items-center space-x-2 flex-shrink-0">
          {item.type === 'toggle' && (
            <div className={cn(
              'w-12 h-6 rounded-full transition-colors',
              item.value ? 'bg-primary' : 'bg-gray-300'
            )}>
              <div className={cn(
                'w-5 h-5 bg-white rounded-full shadow-sm transition-transform',
                'mt-0.5',
                item.value ? 'ml-6' : 'ml-0.5'
              )} />
            </div>
          )}
          {item.type === 'navigation' && (
            <span className="text-muted-foreground">›</span>
          )}
        </div>
      </div>
    </button>
  );

  /**
   * 渲染设置分组
   */
  const renderSettingsSection = (section: typeof settingsSections[0]) => (
    <div key={section.title} className="space-y-3">
      <h2 className="text-sm font-medium text-muted-foreground uppercase tracking-wide px-4">
        {section.title}
      </h2>
      <div className="space-y-2">
        {section.items.map(renderSettingItem)}
      </div>
    </div>
  );

  return (
    <div className="flex flex-col h-full">
      {/* 头部 */}
      <div className="p-4 bg-background border-b border-border">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
            <span className="text-primary-foreground font-bold">
              {user?.contact?.charAt(0).toUpperCase() || 'U'}
            </span>
          </div>
          <div>
            <h1 className="text-lg font-semibold text-foreground">设置</h1>
            <p className="text-sm text-muted-foreground">
              {user?.contact || '未登录用户'}
            </p>
          </div>
        </div>
      </div>
      
      {/* 设置列表 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-6">
          {settingsSections.map(renderSettingsSection)}
        </div>
        
        {/* 底部版本信息 */}
        <div className="p-4 text-center">
          <p className="text-xs text-muted-foreground">
            Secure Password v1.0.0
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            © 2024 Secure Password. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
}; 