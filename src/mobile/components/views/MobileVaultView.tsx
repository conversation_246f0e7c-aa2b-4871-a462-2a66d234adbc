/**
 * 移动端保险柜视图组件
 * 显示密码列表和管理功能
 */

import React, { useState } from 'react';
import { useHybridCredentials } from '../../../contexts/HybridCredentialsContext';
import { LoginCredentialOutput } from '../../../types';
import { cn } from '../../../lib/utils';

/**
 * 移动端保险柜视图组件
 */
export const MobileVaultView: React.FC = () => {
  const { credentials, isLoading, error } = useHybridCredentials();
  const [searchQuery, setSearchQuery] = useState('');

  // 过滤凭据
  const filteredCredentials = credentials.filter(credential =>
    credential.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    credential.username?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    credential.website?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  /**
   * 渲染搜索栏
   */
  const renderSearchBar = () => (
    <div className="p-4 bg-background border-b border-border">
      <div className="relative">
        <input
          type="text"
          placeholder="搜索密码..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className={cn(
            'w-full pl-10 pr-4 py-3',
            'bg-accent rounded-lg',
            'border border-border',
            'text-foreground placeholder-muted-foreground',
            'focus:outline-none focus:ring-2 focus:ring-primary',
            'transition-all duration-200'
          )}
        />
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
          <span className="text-muted-foreground">🔍</span>
        </div>
        {searchQuery && (
          <button
            onClick={() => setSearchQuery('')}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
          >
            ✕
          </button>
        )}
      </div>
    </div>
  );

  /**
   * 渲染凭据卡片
   */
  const renderCredentialCard = (credential: LoginCredentialOutput) => (
    <div
      key={credential.id}
      className={cn(
        'p-4 bg-card border border-border rounded-lg',
        'hover:shadow-md transition-all duration-200',
        'active:scale-95'
      )}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          {/* 网站图标 */}
          <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
            <span className="text-primary-foreground font-bold text-sm">
              {credential.name.charAt(0).toUpperCase()}
            </span>
          </div>
          
          {/* 凭据信息 */}
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-foreground truncate">
              {credential.name}
            </h3>
            <p className="text-sm text-muted-foreground truncate">
              {credential.username || credential.website || '无用户名'}
            </p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-2 flex-shrink-0">
          {credential.favorite && (
            <span className="text-yellow-500">⭐</span>
          )}
          <button
            className="p-2 hover:bg-accent rounded-full transition-colors"
            aria-label="更多操作"
          >
            ⋯
          </button>
        </div>
      </div>
    </div>
  );

  /**
   * 渲染空状态
   */
  const renderEmptyState = () => (
    <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
      <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mb-4">
        <span className="text-2xl">🔒</span>
      </div>
      <h3 className="text-lg font-medium text-foreground mb-2">
        还没有保存的密码
      </h3>
      <p className="text-muted-foreground mb-6 max-w-sm">
        开始添加您的第一个密码，让我们帮您安全地管理它们
      </p>
      <button className="px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors">
        添加密码
      </button>
    </div>
  );

  /**
   * 渲染加载状态
   */
  const renderLoadingState = () => (
    <div className="flex-1 flex items-center justify-center">
      <div className="text-center">
        <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-muted-foreground">加载中...</p>
      </div>
    </div>
  );

  /**
   * 渲染错误状态
   */
  const renderErrorState = () => (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="text-center">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
          <span className="text-2xl">⚠️</span>
        </div>
        <h3 className="text-lg font-medium text-foreground mb-2">
          加载失败
        </h3>
        <p className="text-muted-foreground mb-4">
          {error || '无法加载密码数据'}
        </p>
        <button className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
          重试
        </button>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex flex-col h-full">
        {renderSearchBar()}
        {renderLoadingState()}
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-full">
        {renderSearchBar()}
        {renderErrorState()}
      </div>
    );
  }

  if (filteredCredentials.length === 0) {
    return (
      <div className="flex flex-col h-full">
        {renderSearchBar()}
        {searchQuery ? (
          <div className="flex-1 flex items-center justify-center p-8 text-center">
            <div>
              <h3 className="text-lg font-medium text-foreground mb-2">
                未找到匹配的密码
              </h3>
              <p className="text-muted-foreground">
                尝试使用不同的关键词搜索
              </p>
            </div>
          </div>
        ) : (
          renderEmptyState()
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {renderSearchBar()}
      
      {/* 凭据列表 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-3">
          {filteredCredentials.map(renderCredentialCard)}
        </div>
      </div>
    </div>
  );
}; 