/**
 * 移动端功能 React Hook
 * 提供移动端功能的状态管理和操作方法
 */

import { useState, useEffect, useCallback } from 'react';
import { Mobile, BiometricStatus, DeviceInfo } from '../utils/mobile-api';

/**
 * 移动端功能状态接口
 */
export interface MobileFeaturesState {
  /** 是否已初始化 */
  isInitialized: boolean;
  /** 是否正在初始化 */
  isInitializing: boolean;
  /** 设备信息 */
  deviceInfo: DeviceInfo | null;
  /** 生物识别状态 */
  biometricStatus: BiometricStatus | null;
  /** 错误信息 */
  error: string | null;
  /** 平台状态 */
  platformStatus: string | null;
}

/**
 * 移动端功能操作接口
 */
export interface MobileFeaturesActions {
  /** 初始化移动功能 */
  initialize: () => Promise<void>;
  /** 重新初始化 */
  reinitialize: () => Promise<void>;
  /** 刷新状态 */
  refreshStatus: () => Promise<void>;
  /** 检查生物识别可用性 */
  checkBiometric: () => Promise<void>;
  /** 执行生物识别认证 */
  authenticateBiometric: (reason?: string) => Promise<boolean>;
  /** 清除错误 */
  clearError: () => void;
}

/**
 * 移动端功能 Hook 返回值
 */
export interface UseMobileFeaturesReturn extends MobileFeaturesState, MobileFeaturesActions {}

/**
 * 移动端功能 Hook
 */
export function useMobileFeatures(): UseMobileFeaturesReturn {
  const [state, setState] = useState<MobileFeaturesState>({
    isInitialized: false,
    isInitializing: false,
    deviceInfo: null,
    biometricStatus: null,
    error: null,
    platformStatus: null,
  });

  /**
   * 更新状态的辅助函数
   */
  const updateState = useCallback((updates: Partial<MobileFeaturesState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * 设置错误信息
   */
  const setError = useCallback((error: string | null) => {
    updateState({ error });
  }, [updateState]);

  /**
   * 清除错误信息
   */
  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  /**
   * 初始化移动功能
   */
  const initialize = useCallback(async () => {
    if (state.isInitializing) return;

    updateState({ isInitializing: true, error: null });

    try {
      // 初始化移动功能管理器
      await Mobile.API.initializeMobileFeatureManager();
      
      // 获取设备信息
      const deviceInfo = await Mobile.API.getDeviceInfo();
      
      // 获取平台状态
      const platformStatus = await Mobile.API.getMobilePlatformStatus();
      
      // 检查生物识别状态
      let biometricStatus: BiometricStatus | null = null;
      try {
        biometricStatus = await Mobile.Biometric.checkAvailability();
      } catch (error) {
        console.warn('获取生物识别状态失败:', error);
      }

      updateState({
        isInitialized: true,
        isInitializing: false,
        deviceInfo,
        biometricStatus,
        platformStatus,
        error: null,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '初始化失败';
      updateState({
        isInitialized: false,
        isInitializing: false,
        error: errorMessage,
      });
    }
  }, [state.isInitializing, updateState]);

  /**
   * 重新初始化
   */
  const reinitialize = useCallback(async () => {
    updateState({ 
      isInitialized: false, 
      isInitializing: true, 
      error: null,
      deviceInfo: null,
      biometricStatus: null,
      platformStatus: null,
    });

    try {
      await Mobile.API.reinitializeMobilePlatform();
      await initialize();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '重新初始化失败';
      updateState({
        isInitialized: false,
        isInitializing: false,
        error: errorMessage,
      });
    }
  }, [initialize, updateState]);

  /**
   * 刷新状态
   */
  const refreshStatus = useCallback(async () => {
    if (!state.isInitialized) return;

    try {
      const [deviceInfo, platformStatus, biometricStatus] = await Promise.all([
        Mobile.API.getDeviceInfo(),
        Mobile.API.getMobilePlatformStatus(),
        Mobile.Biometric.checkAvailability().catch(() => null),
      ]);

      updateState({
        deviceInfo,
        platformStatus,
        biometricStatus,
        error: null,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '刷新状态失败';
      setError(errorMessage);
    }
  }, [state.isInitialized, updateState, setError]);

  /**
   * 检查生物识别可用性
   */
  const checkBiometric = useCallback(async () => {
    try {
      const biometricStatus = await Mobile.Biometric.checkAvailability();
      updateState({ biometricStatus, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '检查生物识别失败';
      setError(errorMessage);
    }
  }, [updateState, setError]);

  /**
   * 执行生物识别认证
   */
  const authenticateBiometric = useCallback(async (reason = '请验证您的身份'): Promise<boolean> => {
    try {
      clearError();
      const result = await Mobile.Biometric.authenticate(reason);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '生物识别认证失败';
      setError(errorMessage);
      return false;
    }
  }, [clearError, setError]);

  /**
   * 组件挂载时自动初始化
   */
  useEffect(() => {
    if (!state.isInitialized && !state.isInitializing) {
      initialize();
    }
  }, [state.isInitialized, state.isInitializing, initialize]);

  return {
    // 状态
    ...state,
    // 操作方法
    initialize,
    reinitialize,
    refreshStatus,
    checkBiometric,
    authenticateBiometric,
    clearError,
  };
}

/**
 * 安全存储 Hook
 */
export interface UseSecureStorageReturn {
  /** 保存数据 */
  saveData: (key: string, value: string) => Promise<void>;
  /** 获取数据 */
  getData: (key: string) => Promise<string | null>;
  /** 删除数据 */
  removeData: (key: string) => Promise<boolean>;
  /** 清空存储 */
  clearStorage: () => Promise<void>;
  /** 获取所有键名 */
  getKeys: () => Promise<string[]>;
  /** 检查键是否存在 */
  hasKey: (key: string) => Promise<boolean>;
  /** 错误信息 */
  error: string | null;
  /** 清除错误 */
  clearError: () => void;
}

/**
 * 安全存储 Hook
 */
export function useSecureStorage(): UseSecureStorageReturn {
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const handleError = useCallback((error: unknown, defaultMessage: string) => {
    const errorMessage = error instanceof Error ? error.message : defaultMessage;
    setError(errorMessage);
    throw new Error(errorMessage);
  }, []);

  const saveData = useCallback(async (key: string, value: string) => {
    try {
      clearError();
      await Mobile.SecureStorage.saveData(key, value);
    } catch (error) {
      handleError(error, '保存数据失败');
    }
  }, [clearError, handleError]);

  const getData = useCallback(async (key: string) => {
    try {
      clearError();
      return await Mobile.SecureStorage.getData(key);
    } catch (error) {
      handleError(error, '获取数据失败');
      return null;
    }
  }, [clearError, handleError]);

  const removeData = useCallback(async (key: string) => {
    try {
      clearError();
      return await Mobile.SecureStorage.removeData(key);
    } catch (error) {
      handleError(error, '删除数据失败');
      return false;
    }
  }, [clearError, handleError]);

  const clearStorage = useCallback(async () => {
    try {
      clearError();
      await Mobile.SecureStorage.clearStorage();
    } catch (error) {
      handleError(error, '清空存储失败');
    }
  }, [clearError, handleError]);

  const getKeys = useCallback(async () => {
    try {
      clearError();
      return await Mobile.SecureStorage.getStorageKeys();
    } catch (error) {
      handleError(error, '获取键名失败');
      return [];
    }
  }, [clearError, handleError]);

  const hasKey = useCallback(async (key: string) => {
    try {
      clearError();
      return await Mobile.SecureStorage.hasKey(key);
    } catch (error) {
      handleError(error, '检查键存在性失败');
      return false;
    }
  }, [clearError, handleError]);

  return {
    saveData,
    getData,
    removeData,
    clearStorage,
    getKeys,
    hasKey,
    error,
    clearError,
  };
}

export default useMobileFeatures; 