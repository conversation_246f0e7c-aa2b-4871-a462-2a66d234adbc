/**
 * 移动端认证页面测试
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { MobileAuthPage } from './MobileAuthPage';

// Mock 认证上下文
const mockLogin = vi.fn();
const mockRegister = vi.fn();

vi.mock('../../contexts', () => ({
  useAuthActions: () => ({
    login: mockLogin,
    register: mockRegister,
  }),
}));

// Mock API 调用
vi.mock('../../api', () => ({
  registerCompleteFlow: vi.fn(),
  sendVerificationCode: vi.fn(),
  validatePasswordStrength: vi.fn(),
  checkContactAvailability: vi.fn(),
}));

/**
 * 测试包装器组件
 */
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ConfigProvider locale={zhCN}>
    {children}
  </ConfigProvider>
);

describe('MobileAuthPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('页面渲染', () => {
    it('应该正确渲染移动端认证页面', () => {
      render(
        <TestWrapper>
          <MobileAuthPage />
        </TestWrapper>
      );

      // 检查页面标题
      expect(screen.getByText('Secure Vault')).toBeInTheDocument();
      expect(screen.getByText('安全的密码管理器')).toBeInTheDocument();

      // 检查默认显示注册标签
      expect(screen.getByText('注册账户')).toBeInTheDocument();
      expect(screen.getByText('登录账户')).toBeInTheDocument();
    });

    it('应该具有移动端适配的样式', () => {
      render(
        <TestWrapper>
          <MobileAuthPage />
        </TestWrapper>
      );

      const container = screen.getByTestId('mobile-auth-page');
      expect(container).toHaveClass('min-h-screen');
      expect(container).toHaveClass('bg-gradient-to-br');
    });
  });

  describe('标签切换', () => {
    it('应该能够在注册和登录之间切换', async () => {
      render(
        <TestWrapper>
          <MobileAuthPage />
        </TestWrapper>
      );

      // 默认显示注册表单
      expect(screen.getByPlaceholderText(/请输入邮箱地址或手机号码/)).toBeInTheDocument();

      // 切换到登录标签
      fireEvent.click(screen.getByText('登录账户'));

      await waitFor(() => {
        // 登录表单应该显示
        expect(screen.getByPlaceholderText(/请输入邮箱地址或手机号码/)).toBeInTheDocument();
      });
    });

    it('应该保持标签状态', () => {
      render(
        <TestWrapper>
          <MobileAuthPage />
        </TestWrapper>
      );

      // 切换到登录
      fireEvent.click(screen.getByText('登录账户'));

      // 检查活跃标签
      const loginTab = screen.getByText('登录账户').closest('.ant-tabs-tab');
      expect(loginTab).toHaveClass('ant-tabs-tab-active');
    });
  });

  describe('表单交互', () => {
    it('应该能够输入注册信息', async () => {
      render(
        <TestWrapper>
          <MobileAuthPage />
        </TestWrapper>
      );

      // 输入邮箱
      const emailInput = screen.getByPlaceholderText(/请输入邮箱地址或手机号码/);
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      expect(emailInput).toHaveValue('<EMAIL>');
    });

    it('应该能够输入登录信息', async () => {
      render(
        <TestWrapper>
          <MobileAuthPage />
        </TestWrapper>
      );

      // 切换到登录标签
      fireEvent.click(screen.getByText('登录账户'));

      await waitFor(() => {
        // 输入登录信息
        const contactInput = screen.getByPlaceholderText(/请输入邮箱地址或手机号码/);
        fireEvent.change(contactInput, { target: { value: '<EMAIL>' } });

        expect(contactInput).toHaveValue('<EMAIL>');
      });
    });
  });

  describe('响应式设计', () => {
    it('应该在小屏幕上正确显示', () => {
      // 模拟小屏幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(
        <TestWrapper>
          <MobileAuthPage />
        </TestWrapper>
      );

      const container = screen.getByTestId('mobile-auth-page');
      expect(container).toBeInTheDocument();
    });

    it('应该支持触摸交互', () => {
      render(
        <TestWrapper>
          <MobileAuthPage />
        </TestWrapper>
      );

      const loginTab = screen.getByText('登录账户');
      
      // 模拟触摸事件
      fireEvent.touchStart(loginTab);
      fireEvent.touchEnd(loginTab);
      fireEvent.click(loginTab);

      expect(loginTab.closest('.ant-tabs-tab')).toHaveClass('ant-tabs-tab-active');
    });
  });

  describe('无障碍支持', () => {
    it('应该具有正确的ARIA标签', () => {
      render(
        <TestWrapper>
          <MobileAuthPage />
        </TestWrapper>
      );

      // 检查主要区域的ARIA标签
      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    it('应该支持键盘导航', () => {
      render(
        <TestWrapper>
          <MobileAuthPage />
        </TestWrapper>
      );

      const loginTab = screen.getByText('登录账户');
      
      // 模拟键盘事件
      fireEvent.keyDown(loginTab, { key: 'Enter' });
      
      expect(loginTab.closest('.ant-tabs-tab')).toHaveClass('ant-tabs-tab-active');
    });
  });
}); 