/**
 * 移动端认证页面
 * 提供移动端优化的注册和登录功能
 */

import React, { useState } from 'react';
import { Tabs, Card } from 'antd';
import type { TabsProps } from 'antd';
import { MobileRegisterForm } from '../components/auth/MobileRegisterForm';
import { MobileLoginForm } from '../components/auth/MobileLoginForm';

/**
 * 移动端认证页面组件
 */
export const MobileAuthPage: React.FC = () => {
  const [activeKey, setActiveKey] = useState<string>('register');

  const tabItems: TabsProps['items'] = [
    {
      key: 'register',
      label: '注册账户',
      children: <MobileRegisterForm />
    },
    {
      key: 'login',
      label: '登录账户',
      children: <MobileLoginForm />
    }
  ];

  return (
    <main 
      className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4"
      data-testid="mobile-auth-page"
      role="main"
      aria-label="认证页面"
    >
      <div className="w-full max-w-sm mx-auto">
        <Card 
          className="shadow-xl border-0 rounded-2xl overflow-hidden backdrop-blur-sm bg-white/90 dark:bg-gray-800/90"
          bodyStyle={{ padding: '1.5rem' }}
        >
          {/* 应用标题 */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg">
              <svg 
                className="w-8 h-8 text-white" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" 
                />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-1">
              Secure Vault
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              安全的密码管理器
            </p>
          </div>

          {/* 认证标签页 */}
          <Tabs
            activeKey={activeKey}
            onChange={setActiveKey}
            centered
            size="large"
            items={tabItems}
            className="mobile-auth-tabs"
            tabBarStyle={{
              marginBottom: '1.5rem',
              borderBottom: '1px solid #f0f0f0'
            }}
            tabBarGutter={0}
          />
        </Card>

        {/* 底部安全提示 */}
        <div className="text-center mt-6 px-4">
          <div className="flex items-center justify-center text-xs text-gray-500 dark:text-gray-400">
            <svg 
              className="w-4 h-4 mr-1" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" 
              />
            </svg>
            <span>端到端加密保护您的数据安全</span>
          </div>
        </div>
      </div>
    </main>
  );
};

export default MobileAuthPage; 