/**
 * 移动端主页面组件测试
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { MobileMainPage } from './MobileMainPage';

// Mock dependencies
vi.mock('../../hooks/usePlatformRouter', () => ({
  usePlatformRouter: () => ({
    platform: 'mobile',
    isMobile: true,
    isDesktop: false,
  }),
}));

vi.mock('../../../contexts/HybridCredentialsContext', () => ({
  useHybridCredentials: () => ({
    credentials: [
      {
        id: 1,
        name: '测试账户',
        username: '<EMAIL>',
        password: 'password123',
        website: 'https://example.com',
        favorite: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      },
    ],
    isLoading: false,
    error: null,
    loadCredentials: vi.fn(),
  }),
}));

vi.mock('../../../contexts/UserContext', () => ({
  useUser: () => ({
    user: { contact: '<EMAIL>' },
    isLoggedIn: true,
    isLoading: false,
  }),
}));

describe('MobileMainPage', () => {
  it('应该渲染移动端主页面', () => {
    render(<MobileMainPage />);
    
    expect(screen.getByRole('navigation')).toBeInTheDocument();
  });

  it('应该显示底部导航栏', () => {
    render(<MobileMainPage />);
    
    expect(screen.getByText('保险柜')).toBeInTheDocument();
    expect(screen.getByText('收藏夹')).toBeInTheDocument();
    expect(screen.getByText('设置')).toBeInTheDocument();
  });

  it('应该在点击导航项时切换页面', () => {
    render(<MobileMainPage />);
    
    // 点击收藏夹
    fireEvent.click(screen.getByText('收藏夹'));
    
    // 验证页面切换（这里需要根据实际实现调整）
    expect(screen.getByText('收藏夹')).toHaveClass('mobile-nav-item--active');
  });

  it('应该显示用户信息', () => {
    render(<MobileMainPage />);
    
    // 这里需要根据实际的用户信息显示方式调整
    expect(screen.getByText(/<EMAIL>/)).toBeInTheDocument();
  });

  it('应该响应式适配移动端屏幕', () => {
    const { container } = render(<MobileMainPage />);
    
    expect(container.firstChild).toHaveClass('mobile-layout');
  });

  it('应该支持安全区域适配', () => {
    const { container } = render(<MobileMainPage />);
    
    const layout = container.firstChild as HTMLElement;
    expect(layout).toHaveClass('mobile-layout--safe-area');
  });
}); 