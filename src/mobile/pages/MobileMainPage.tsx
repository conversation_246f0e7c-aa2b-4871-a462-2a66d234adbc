/**
 * 移动端主页面组件
 * 提供移动端优化的密码管理界面
 */

import React, { useState, useEffect } from 'react';
import { MobileLayout } from '../components/layout/MobileLayout';
import { MobileNavBar, MobileNavItem } from '../components/navigation/MobileNavBar';
import { useHybridCredentials } from '../../contexts/HybridCredentialsContext';
import { useUser } from '../../contexts/UserContext';
import { usePlatformRouter } from '../../shared/hooks/usePlatformRouter';

// 移动端页面组件
import { MobileVaultView } from '../components/views/MobileVaultView';
import { MobileFavoritesView } from '../components/views/MobileFavoritesView';
import { MobileSettingsView } from '../components/views/MobileSettingsView';

/**
 * 移动端主页面组件
 */
export const MobileMainPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('vault');
  const { credentials, favoriteCredentials, loadCredentials, loadFavoriteCredentials } = useHybridCredentials();
  const { user } = useUser();
  const { } = usePlatformRouter();

  // 导航项配置
  const navigationItems: MobileNavItem[] = [
    {
      key: 'vault',
      label: '保险柜',
      icon: '🔒',
      path: '/vault',
      badge: credentials.length > 0 ? credentials.length : undefined,
    },
    {
      key: 'favorites',
      label: '收藏夹',
      icon: '⭐',
      path: '/favorites',
      badge: favoriteCredentials.length > 0 ? favoriteCredentials.length : undefined,
    },
    {
      key: 'settings',
      label: '设置',
      icon: '⚙️',
      path: '/settings',
    },
  ];

  // 初始化数据加载
  useEffect(() => {
    loadCredentials();
    loadFavoriteCredentials();
  }, [loadCredentials, loadFavoriteCredentials]);

  /**
   * 处理导航项点击
   */
  const handleNavItemClick = (key: string) => {
    setActiveTab(key);
  };

  /**
   * 渲染当前激活的页面内容
   */
  const renderActiveView = () => {
    switch (activeTab) {
      case 'vault':
        return <MobileVaultView />;
      case 'favorites':
        return <MobileFavoritesView />;
      case 'settings':
        return <MobileSettingsView />;
      default:
        return <MobileVaultView />;
    }
  };

  /**
   * 渲染顶部导航栏
   */
  const renderHeader = () => (
    <div className="flex items-center justify-between p-4 bg-background border-b border-border">
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
          <span className="text-primary-foreground text-sm font-bold">
            {user?.contact?.charAt(0).toUpperCase() || 'U'}
          </span>
        </div>
        <div>
          <h1 className="text-lg font-semibold text-foreground">
            {getPageTitle()}
          </h1>
          <p className="text-sm text-muted-foreground">
            {user?.contact || '未登录'}
          </p>
        </div>
      </div>
      
      {/* 右侧操作按钮 */}
      <div className="flex items-center space-x-2">
        <button
          className="p-2 rounded-full hover:bg-accent transition-colors"
          aria-label="搜索"
        >
          🔍
        </button>
        <button
          className="p-2 rounded-full hover:bg-accent transition-colors"
          aria-label="添加"
        >
          ➕
        </button>
      </div>
    </div>
  );

  /**
   * 获取页面标题
   */
  const getPageTitle = () => {
    const activeItem = navigationItems.find(item => item.key === activeTab);
    return activeItem?.label || '保险柜';
  };

  return (
    <MobileLayout
      className="mobile-layout--safe-area"
      safeArea={true}
      header={renderHeader()}
      footer={
        <MobileNavBar
          items={navigationItems}
          activeKey={activeTab}
          onItemClick={handleNavItemClick}
          theme="light"
        />
      }
    >
      {/* 主要内容区域 */}
      <div className="flex-1 overflow-hidden">
        {renderActiveView()}
      </div>
    </MobileLayout>
  );
}; 