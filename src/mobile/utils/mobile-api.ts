/**
 * 移动端 API 封装
 * 
 * 提供移动端功能的 TypeScript 接口
 */

import { invoke } from '@tauri-apps/api/core';

/**
 * 生物识别状态接口
 */
export interface BiometricStatus {
  /** 是否可用 */
  is_available: boolean;
  /** 错误信息（如果不可用） */
  error?: string;
  /** 支持的生物识别类型 */
  biometric_types: string[];
}

/**
 * 设备信息接口
 */
export interface DeviceInfo {
  /** 平台类型 */
  platform: string;
  /** 平台版本 */
  version: string;
  /** 设备型号 */
  model: string;
  /** 设备制造商 */
  manufacturer: string;
}

/**
 * 移动端 API 类
 */
export class MobileAPI {
  /**
   * 初始化移动功能管理器
   * 
   * @returns 初始化结果
   */
  static async initializeMobileFeatureManager(): Promise<string> {
    try {
      return await invoke<string>('initialize_mobile_feature_manager');
    } catch (error) {
      throw new Error(`初始化移动功能管理器失败: ${error}`);
    }
  }

  /**
   * 获取移动平台状态
   * 
   * @returns 平台状态信息
   */
  static async getMobilePlatformStatus(): Promise<string> {
    try {
      return await invoke<string>('get_mobile_platform_status');
    } catch (error) {
      throw new Error(`获取移动平台状态失败: ${error}`);
    }
  }

  /**
   * 重新初始化移动平台
   * 
   * @returns 重新初始化结果
   */
  static async reinitializeMobilePlatform(): Promise<string> {
    try {
      return await invoke<string>('reinitialize_mobile_platform');
    } catch (error) {
      throw new Error(`重新初始化移动平台失败: ${error}`);
    }
  }

  /**
   * 获取设备信息
   * 
   * @returns 设备信息
   */
  static async getDeviceInfo(): Promise<DeviceInfo> {
    try {
      return await invoke<DeviceInfo>('get_device_info');
    } catch (error) {
      throw new Error(`获取设备信息失败: ${error}`);
    }
  }

  /**
   * 保存安全数据
   * 
   * @param key 存储键名
   * @param value 存储值
   */
  static async saveSecureData(key: string, value: string): Promise<void> {
    try {
      await invoke<void>('save_secure_data', { key, value });
    } catch (error) {
      throw new Error(`保存安全数据失败: ${error}`);
    }
  }

  /**
   * 获取安全数据
   * 
   * @param key 存储键名
   * @returns 存储的值，如果不存在返回 null
   */
  static async getSecureData(key: string): Promise<string | null> {
    try {
      return await invoke<string | null>('get_secure_data', { key });
    } catch (error) {
      throw new Error(`获取安全数据失败: ${error}`);
    }
  }

  /**
   * 删除安全数据
   * 
   * @param key 存储键名
   * @returns 是否删除成功
   */
  static async removeSecureData(key: string): Promise<boolean> {
    try {
      return await invoke<boolean>('remove_secure_data', { key });
    } catch (error) {
      throw new Error(`删除安全数据失败: ${error}`);
    }
  }

  /**
   * 清空安全存储
   */
  static async clearSecureStorage(): Promise<void> {
    try {
      await invoke<void>('clear_secure_storage');
    } catch (error) {
      throw new Error(`清空安全存储失败: ${error}`);
    }
  }

  /**
   * 获取安全存储键名列表
   * 
   * @returns 所有键名的列表
   */
  static async getSecureStorageKeys(): Promise<string[]> {
    try {
      return await invoke<string[]>('get_secure_storage_keys');
    } catch (error) {
      throw new Error(`获取存储键名失败: ${error}`);
    }
  }

  /**
   * 获取移动功能状态
   * 
   * @returns 状态摘要
   */
  static async getMobileStatus(): Promise<string> {
    try {
      return await invoke<string>('get_mobile_status');
    } catch (error) {
      throw new Error(`获取移动功能状态失败: ${error}`);
    }
  }
}

/**
 * 生物识别工具类
 */
export class BiometricUtils {
  /**
   * 检查生物识别可用性
   * 
   * @returns 生物识别状态信息
   */
  static async checkAvailability(): Promise<BiometricStatus> {
    try {
      return await invoke<BiometricStatus>('check_biometric_availability');
    } catch (error) {
      throw new Error(`检查生物识别可用性失败: ${error}`);
    }
  }

  /**
   * 使用生物识别进行认证
   * 
   * @param reason 认证原因
   * @returns 认证是否成功
   */
  static async authenticate(reason: string): Promise<boolean> {
    try {
      return await invoke<boolean>('authenticate_with_biometric', { reason });
    } catch (error) {
      throw new Error(`生物识别认证失败: ${error}`);
    }
  }

  /**
   * 检查生物识别是否可用（简化版本）
   * 
   * @returns 是否可用
   */
  static async isAvailable(): Promise<boolean> {
    try {
      const status = await this.checkAvailability();
      return status.is_available;
    } catch (error) {
      console.error('检查生物识别可用性时出错:', error);
      return false;
    }
  }

  /**
   * 获取支持的生物识别类型
   * 
   * @returns 支持的类型列表
   */
  static async getSupportedTypes(): Promise<string[]> {
    try {
      const status = await this.checkAvailability();
      return status.biometric_types;
    } catch (error) {
      console.error('获取生物识别类型失败:', error);
      return [];
    }
  }
}

/**
 * 安全存储工具类
 */
export class SecureStorageUtils {
  /**
   * 保存安全数据
   * @param key 存储键名
   * @param value 存储值
   */
  static async saveData(key: string, value: string): Promise<void> {
    try {
      await invoke<void>('save_secure_data', { key, value });
    } catch (error) {
      throw new Error(`保存安全数据失败: ${error}`);
    }
  }

  /**
   * 获取安全数据
   * @param key 存储键名
   */
  static async getData(key: string): Promise<string | null> {
    try {
      return await invoke<string | null>('get_secure_data', { key });
    } catch (error) {
      throw new Error(`获取安全数据失败: ${error}`);
    }
  }

  /**
   * 删除安全数据
   * @param key 存储键名
   */
  static async removeData(key: string): Promise<boolean> {
    try {
      return await invoke<boolean>('remove_secure_data', { key });
    } catch (error) {
      throw new Error(`删除安全数据失败: ${error}`);
    }
  }

  /**
   * 清空安全存储
   */
  static async clearStorage(): Promise<void> {
    try {
      await invoke<void>('clear_secure_storage');
    } catch (error) {
      throw new Error(`清空安全存储失败: ${error}`);
    }
  }

  /**
   * 获取所有存储键名
   */
  static async getStorageKeys(): Promise<string[]> {
    try {
      return await invoke<string[]>('get_secure_storage_keys');
    } catch (error) {
      throw new Error(`获取存储键名失败: ${error}`);
    }
  }

  /**
   * 检查键是否存在
   * @param key 存储键名
   */
  static async hasKey(key: string): Promise<boolean> {
    try {
      const data = await this.getData(key);
      return data !== null;
    } catch (error) {
      console.error('检查键是否存在时出错:', error);
      return false;
    }
  }

  /**
   * 存储用户凭据
   * 
   * @param username 用户名
   * @param password 密码
   */
  static async storeCredentials(username: string, password: string): Promise<void> {
    await this.saveData(`credentials_${username}`, password);
  }

  /**
   * 获取用户凭据
   * 
   * @param username 用户名
   * @returns 密码，如果不存在返回 null
   */
  static async getCredentials(username: string): Promise<string | null> {
    return await this.getData(`credentials_${username}`);
  }

  /**
   * 删除用户凭据
   * 
   * @param username 用户名
   * @returns 是否删除成功
   */
  static async removeCredentials(username: string): Promise<boolean> {
    return await this.removeData(`credentials_${username}`);
  }

  /**
   * 存储应用设置
   * 
   * @param key 设置键名
   * @param value 设置值
   */
  static async storeSetting(key: string, value: any): Promise<void> {
    const jsonValue = JSON.stringify(value);
    await this.saveData(`setting_${key}`, jsonValue);
  }

  /**
   * 获取应用设置
   * 
   * @param key 设置键名
   * @param defaultValue 默认值
   * @returns 设置值
   */
  static async getSetting<T>(key: string, defaultValue: T): Promise<T> {
    try {
      const jsonValue = await this.getData(`setting_${key}`);
      if (jsonValue) {
        return JSON.parse(jsonValue);
      }
      return defaultValue;
    } catch (error) {
      console.error('获取设置失败:', error);
      return defaultValue;
    }
  }
}

/**
 * 移动端功能统一导出
 */
export const Mobile = {
  API: MobileAPI,
  Biometric: BiometricUtils,
  SecureStorage: SecureStorageUtils,
};

export default Mobile; 