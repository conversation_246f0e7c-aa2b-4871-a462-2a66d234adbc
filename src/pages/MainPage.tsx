/**
 * 主页面组件 - 密码管理器的核心界面
 * 组合各个子组件构成完整的主页面
 * 更新为使用 Hybrid Storage 系统，支持页面切换
 */

import React, { useState } from 'react';
import { Layout } from 'antd';
import type { LoginCredentialOutput, CredentialOutput } from '../types';
import { useHybridCredentials } from '../contexts/HybridCredentialsContext';
import AppLayout from '../components/layout/AppLayout';
import { 
  LeftSidebar, 
  TopToolbar, 
  CredentialDetail,
  VaultView,
  FavoritesView,
  SharedView,
  ArchivedView,
  TrashView
} from '../components/main';

const { Content } = Layout;

/**
 * 将 LoginCredentialOutput 转换为 CredentialOutput 以兼容现有组件
 */
const adaptCredentialForDetail = (credential: LoginCredentialOutput): CredentialOutput => {
  return {
    id: credential.id,
    service_name: credential.name,
    username: credential.username || '',
    password: credential.password,
    notes: credential.notes || '',
    created_at: credential.created_at,
    updated_at: credential.updated_at,
  };
};

/**
 * 主页面组件
 */
const MainPage: React.FC = () => {
  const { credentials, isLoading } = useHybridCredentials();
  const [selectedKey, setSelectedKey] = useState('vault');
  const [searchValue, setSearchValue] = useState('');
  const [selectedCredential, setSelectedCredential] = useState<LoginCredentialOutput | null>(null);

  /**
   * 处理菜单点击
   */
  const handleMenuClick = (key: string) => {
    setSelectedKey(key);
    setSelectedCredential(null); // 切换页面时清除选中的凭据
  };

  /**
   * 处理凭据点击
   */
  const handleCredentialClick = (credential: LoginCredentialOutput) => {
    setSelectedCredential(credential);
  };

  /**
   * 渲染当前选中的页面内容
   */
  const renderCurrentView = () => {
    const commonProps = {
      selectedCredential,
      onCredentialClick: handleCredentialClick,
      searchValue,
    };

    switch (selectedKey) {
      case 'vault':
        return (
          <VaultView
            credentials={credentials}
            {...commonProps}
          />
        );
      
      case 'favorites':
        return (
          <FavoritesView
            {...commonProps}
          />
        );
      
      case 'shared':
        return <SharedView />;
      
      case 'archived':
        return <ArchivedView />;
      
      case 'trash':
        return <TrashView />;
      
      default:
        return (
          <VaultView
            credentials={credentials}
            {...commonProps}
          />
        );
    }
  };

  // 显示加载状态
  if (isLoading) {
    return (
      <Layout className="h-screen bg-white">
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <div className="text-gray-600">正在加载密码数据...</div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <AppLayout>
      <Layout className="h-screen bg-white">
        {/* 左侧导航栏 */}
        <LeftSidebar 
          selectedKey={selectedKey}
          onMenuClick={handleMenuClick}
        />

        {/* 主内容区域 */}
        <Layout>
          {/* 顶部工具栏 */}
          <TopToolbar
            searchValue={searchValue}
            onSearchChange={setSearchValue}
            userName="186****4998"
            isOnline={true}
          />

          {/* 内容区域 */}
          <Content className="flex h-full overflow-y-auto">
            {/* 当前选中的视图 */}
            <div className="flex-1">
              {renderCurrentView()}
            </div>

            {/* 右侧详情面板 - 只在有选中凭据且不是占位页面时显示 */}
            {selectedCredential && (selectedKey === 'vault' || selectedKey === 'favorites' || selectedKey === 'trash') && (
              <CredentialDetail 
                credential={adaptCredentialForDetail(selectedCredential)} 
                onClose={() => setSelectedCredential(null)} 
              />
            )}
          </Content>
        </Layout>
      </Layout>
    </AppLayout>
  );
};

export default MainPage; 