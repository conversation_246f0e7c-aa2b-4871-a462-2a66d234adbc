/**
 * 平台路由器组件测试
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { PlatformRouter } from './PlatformRouter';
import * as platformDetector from '../utils/platform-detector';

// Mock platform detector
vi.mock('../utils/platform-detector');
const mockDetectPlatform = vi.mocked(platformDetector.detectPlatform);

// Mock components
const MockDesktopComponent = () => <div data-testid="desktop-component">桌面端组件</div>;
const MockMobileComponent = () => <div data-testid="mobile-component">移动端组件</div>;

describe('PlatformRouter', () => {
  it('应该在桌面端渲染桌面组件', () => {
    mockDetectPlatform.mockReturnValue('desktop');

    render(
      <PlatformRouter
        desktopComponent={MockDesktopComponent}
        mobileComponent={MockMobileComponent}
      />
    );

    expect(screen.getByTestId('desktop-component')).toBeInTheDocument();
    expect(screen.queryByTestId('mobile-component')).not.toBeInTheDocument();
  });

  it('应该在移动端渲染移动组件', () => {
    mockDetectPlatform.mockReturnValue('mobile');

    render(
      <PlatformRouter
        desktopComponent={MockDesktopComponent}
        mobileComponent={MockMobileComponent}
      />
    );

    expect(screen.getByTestId('mobile-component')).toBeInTheDocument();
    expect(screen.queryByTestId('desktop-component')).not.toBeInTheDocument();
  });

  it('应该支持渲染函数形式的组件', () => {
    mockDetectPlatform.mockReturnValue('mobile');

    render(
      <PlatformRouter
        desktopComponent={() => <div data-testid="desktop-render">桌面端渲染</div>}
        mobileComponent={() => <div data-testid="mobile-render">移动端渲染</div>}
      />
    );

    expect(screen.getByTestId('mobile-render')).toBeInTheDocument();
    expect(screen.queryByTestId('desktop-render')).not.toBeInTheDocument();
  });

  it('应该传递props给子组件', () => {
    mockDetectPlatform.mockReturnValue('desktop');

    const DesktopWithProps = ({ testProp }: { testProp: string }) => (
      <div data-testid="desktop-with-props">{testProp}</div>
    );

    render(
      <PlatformRouter
        desktopComponent={DesktopWithProps}
        mobileComponent={MockMobileComponent}
        testProp="测试属性"
      />
    );

    expect(screen.getByText('测试属性')).toBeInTheDocument();
  });

  it('应该在平台检测失败时渲染桌面端组件作为默认', () => {
    mockDetectPlatform.mockImplementation(() => {
      throw new Error('检测失败');
    });

    render(
      <PlatformRouter
        desktopComponent={MockDesktopComponent}
        mobileComponent={MockMobileComponent}
      />
    );

    expect(screen.getByTestId('desktop-component')).toBeInTheDocument();
  });
}); 