/**
 * 平台路由器组件
 * 根据设备类型自动渲染对应的移动端或桌面端组件
 */

import React from 'react';
import { usePlatformRouter } from '../hooks/usePlatformRouter';

export interface PlatformRouterProps {
  /** 桌面端组件 */
  desktopComponent: React.ComponentType<any> | (() => React.ReactElement);
  /** 移动端组件 */
  mobileComponent: React.ComponentType<any> | (() => React.ReactElement);
  /** 传递给子组件的额外props */
  [key: string]: any;
}

/**
 * 平台路由器组件
 * 
 * 根据当前设备类型自动选择渲染移动端或桌面端组件
 * 支持响应式切换，当屏幕尺寸变化时自动切换组件
 */
export const PlatformRouter: React.FC<PlatformRouterProps> = ({
  desktopComponent: DesktopComponent,
  mobileComponent: MobileComponent,
  ...props
}) => {
  const { platform } = usePlatformRouter();

  try {
    // 根据平台选择组件
    const ComponentToRender = platform === 'mobile' ? MobileComponent : DesktopComponent;

    // 直接渲染组件
    return <ComponentToRender {...props} />;
  } catch (error) {
    console.error('PlatformRouter: 渲染组件时发生错误', error);
    
    // 错误回退：默认渲染桌面端组件
    try {
      if (typeof DesktopComponent === 'function') {
        return <DesktopComponent {...props} />;
      }
      return null;
    } catch (fallbackError) {
      console.error('PlatformRouter: 回退渲染也失败了', fallbackError);
      return (
        <div className="p-4 text-center text-red-600">
          <h3>组件加载失败</h3>
          <p>请刷新页面重试</p>
        </div>
      );
    }
  }
}; 