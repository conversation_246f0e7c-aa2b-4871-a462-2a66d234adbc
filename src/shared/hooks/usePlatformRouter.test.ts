/**
 * 平台路由Hook测试
 * 测试根据平台自动路由的功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { usePlatformRouter } from './usePlatformRouter';
import * as platformDetector from '../utils/platform-detector';
import type { PlatformType } from '../utils/platform-detector';

// Mock platform detectorx
vi.mock('../utils/platform-detector');

const mockDetectPlatform = vi.mocked(platformDetector.detectPlatform);
const mockOnPlatformChange = vi.mocked(platformDetector.onPlatformChange);

describe('usePlatformRouter', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockOnPlatformChange.mockReturnValue(() => {});
  });

  it('应该返回移动端平台信息', () => {
    mockDetectPlatform.mockReturnValue('mobile');

    const { result } = renderHook(() => usePlatformRouter());

    expect(result.current.platform).toBe('mobile');
    expect(result.current.isMobile).toBe(true);
    expect(result.current.isDesktop).toBe(false);
  });

  it('应该返回桌面端平台信息', () => {
    mockDetectPlatform.mockReturnValue('desktop');

    const { result } = renderHook(() => usePlatformRouter());

    expect(result.current.platform).toBe('desktop');
    expect(result.current.isMobile).toBe(false);
    expect(result.current.isDesktop).toBe(true);
  });

  it('应该监听平台变化', () => {
    mockDetectPlatform.mockReturnValue('desktop');
    
    renderHook(() => usePlatformRouter());

    expect(mockOnPlatformChange).toHaveBeenCalledWith(
      expect.any(Function)
    );
  });

  it('应该在平台变化时更新状态', () => {
    mockDetectPlatform.mockReturnValue('desktop');
    
    let platformChangeCallback: (platform: PlatformType) => void;
    mockOnPlatformChange.mockImplementation((callback: (platform: PlatformType) => void) => {
      platformChangeCallback = callback;
      return () => {};
    });

    const { result } = renderHook(() => usePlatformRouter());

    expect(result.current.platform).toBe('desktop');

    // 模拟平台变化
    act(() => {
      platformChangeCallback('mobile');
    });

    expect(result.current.platform).toBe('mobile');
    expect(result.current.isMobile).toBe(true);
    expect(result.current.isDesktop).toBe(false);
  });

  it('应该在组件卸载时清理监听器', () => {
    const cleanup = vi.fn();
    mockOnPlatformChange.mockReturnValue(cleanup);

    const { unmount } = renderHook(() => usePlatformRouter());

    unmount();

    expect(cleanup).toHaveBeenCalled();
  });

  it('应该提供正确的路由路径', () => {
    mockDetectPlatform.mockReturnValue('mobile');

    const { result } = renderHook(() => usePlatformRouter());

    expect(result.current.getRoutePath('/home')).toBe('/mobile/home');
    expect(result.current.getComponentPath('Button')).toBe('mobile/components/Button');
  });

  it('应该为桌面端提供正确的路由路径', () => {
    mockDetectPlatform.mockReturnValue('desktop');

    const { result } = renderHook(() => usePlatformRouter());

    expect(result.current.getRoutePath('/home')).toBe('/desktop/home');
    expect(result.current.getComponentPath('Button')).toBe('desktop/components/Button');
  });
}); 