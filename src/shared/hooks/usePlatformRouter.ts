/**
 * 平台路由Hook
 * 根据设备类型提供平台相关的路由和状态管理
 */

import { useState, useEffect } from 'react';
import { 
  detectPlatform, 
  onPlatformChange, 
  type PlatformType 
} from '../utils/platform-detector';

export interface PlatformRouterState {
  platform: PlatformType;
  isMobile: boolean;
  isDesktop: boolean;
  getRoutePath: (path: string) => string;
  getComponentPath: (componentName: string) => string;
}

/**
 * 平台路由Hook
 * 提供平台检测和路由生成功能
 */
export const usePlatformRouter = (): PlatformRouterState => {
  const [platform, setPlatform] = useState<PlatformType>(() => detectPlatform());

  useEffect(() => {
    // 监听平台变化
    const cleanup = onPlatformChange((newPlatform) => {
      setPlatform(newPlatform);
    });

    return cleanup;
  }, []);

  /**
   * 生成平台特定的路由路径
   */
  const getRoutePath = (path: string): string => {
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    return `/${platform}/${cleanPath}`;
  };

  /**
   * 生成平台特定的组件路径
   */
  const getComponentPath = (componentName: string): string => {
    return `${platform}/components/${componentName}`;
  };

  return {
    platform,
    isMobile: platform === 'mobile',
    isDesktop: platform === 'desktop',
    getRoutePath,
    getComponentPath,
  };
}; 