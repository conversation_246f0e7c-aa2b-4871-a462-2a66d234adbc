/**
 * 平台检测工具测试
 * 测试移动端和桌面端设备识别功能，包括 Tauri OS 插件集成
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock Tauri OS plugin
vi.mock('@tauri-apps/plugin-os', () => ({
  platform: vi.fn(),
}));

import { 
  detectPlatform, 
  detectPlatformAsync,
  isMobile, 
  isMobileAsync,
  isDesktop, 
  isDesktopAsync,
  getDeviceInfo,
  getDeviceInfoAsync
} from './platform-detector';

// Mock window object
const mockWindow = (userAgent: string, innerWidth: number = 1024) => {
  Object.defineProperty(window, 'navigator', {
    value: { userAgent },
    writable: true,
  });
  Object.defineProperty(window, 'innerWidth', {
    value: innerWidth,
    writable: true,
  });
};

// Mock Tauri environment
const mockTauriEnvironment = (enabled: boolean = false) => {
  if (enabled) {
    (window as any).__TAURI__ = {};
  } else {
    delete (window as any).__TAURI__;
  }
};

describe('平台检测工具', () => {
  let mockTauriPlatform: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    mockTauriEnvironment(false); // 默认非 Tauri 环境
    
    // 获取 mock 函数
    const { platform } = await import('@tauri-apps/plugin-os');
    mockTauriPlatform = vi.mocked(platform);
  });

  describe('detectPlatform (同步)', () => {
    it('应该检测到移动端设备 - iPhone', () => {
      mockWindow('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)');
      expect(detectPlatform()).toBe('mobile');
    });

    it('应该检测到移动端设备 - Android', () => {
      mockWindow('Mozilla/5.0 (Linux; Android 10; SM-G975F)');
      expect(detectPlatform()).toBe('mobile');
    });

    it('应该检测到桌面端设备 - Windows', () => {
      mockWindow('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      expect(detectPlatform()).toBe('desktop');
    });

    it('应该检测到桌面端设备 - macOS', () => {
      mockWindow('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36');
      expect(detectPlatform()).toBe('desktop');
    });

    it('应该基于屏幕宽度检测移动端', () => {
      mockWindow('Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 480);
      expect(detectPlatform()).toBe('mobile');
    });

    it('应该基于屏幕宽度检测桌面端', () => {
      mockWindow('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', 1200);
      expect(detectPlatform()).toBe('desktop');
    });
  });

  describe('detectPlatformAsync (异步)', () => {
    it('在非 Tauri 环境中应该回退到浏览器检测', async () => {
      mockWindow('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)');
      mockTauriEnvironment(false);
      
      const platform = await detectPlatformAsync();
      expect(platform).toBe('mobile');
    });

    it('在 Tauri 环境中应该使用 OS 插件检测 - Android', async () => {
      mockTauriPlatform.mockReturnValue('android');
      mockTauriEnvironment(true);
      
      const platform = await detectPlatformAsync();
      expect(platform).toBe('mobile');
      expect(mockTauriPlatform).toHaveBeenCalled();
    });

    it('在 Tauri 环境中应该使用 OS 插件检测 - Windows', async () => {
      mockTauriPlatform.mockReturnValue('windows');
      mockTauriEnvironment(true);
      
      const platform = await detectPlatformAsync();
      expect(platform).toBe('desktop');
      expect(mockTauriPlatform).toHaveBeenCalled();
    });

    it('在 Tauri 环境中应该使用 OS 插件检测 - iOS', async () => {
      mockTauriPlatform.mockReturnValue('ios');
      mockTauriEnvironment(true);
      
      const platform = await detectPlatformAsync();
      expect(platform).toBe('mobile');
      expect(mockTauriPlatform).toHaveBeenCalled();
    });

    it('在 Tauri OS 插件失败时应该回退到浏览器检测', async () => {
      mockTauriPlatform.mockImplementation(() => {
        throw new Error('Plugin failed');
      });
      
      mockTauriEnvironment(true);
      mockWindow('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)');
      
      const platform = await detectPlatformAsync();
      expect(platform).toBe('mobile');
    });
  });

  describe('isMobile / isMobileAsync', () => {
    it('移动端设备应该返回true (同步)', () => {
      mockWindow('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)');
      expect(isMobile()).toBe(true);
    });

    it('桌面端设备应该返回false (同步)', () => {
      mockWindow('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      expect(isMobile()).toBe(false);
    });

    it('移动端设备应该返回true (异步)', async () => {
      mockWindow('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)');
      expect(await isMobileAsync()).toBe(true);
    });

    it('桌面端设备应该返回false (异步)', async () => {
      mockWindow('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      expect(await isMobileAsync()).toBe(false);
    });
  });

  describe('isDesktop / isDesktopAsync', () => {
    it('桌面端设备应该返回true (同步)', () => {
      mockWindow('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      expect(isDesktop()).toBe(true);
    });

    it('移动端设备应该返回false (同步)', () => {
      mockWindow('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)');
      expect(isDesktop()).toBe(false);
    });

    it('桌面端设备应该返回true (异步)', async () => {
      mockWindow('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      expect(await isDesktopAsync()).toBe(true);
    });

    it('移动端设备应该返回false (异步)', async () => {
      mockWindow('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)');
      expect(await isDesktopAsync()).toBe(false);
    });
  });

  describe('getDeviceInfo / getDeviceInfoAsync', () => {
    it('应该返回完整的设备信息 (同步)', () => {
      mockWindow('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', 375);
      
      const deviceInfo = getDeviceInfo();
      
      expect(deviceInfo).toEqual({
        platform: 'mobile',
        userAgent: expect.stringContaining('iPhone'),
        screenWidth: 375,
        screenHeight: expect.any(Number),
        isTouchDevice: expect.any(Boolean),
        isRetina: expect.any(Boolean),
      });
    });

    it('应该返回包含 Tauri 平台信息的设备信息 (异步)', async () => {
      mockTauriPlatform.mockReturnValue('ios');
      mockTauriEnvironment(true);
      mockWindow('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', 375);
      
      const deviceInfo = await getDeviceInfoAsync();
      
      expect(deviceInfo).toEqual({
        platform: 'mobile',
        userAgent: expect.stringContaining('iPhone'),
        screenWidth: 375,
        screenHeight: expect.any(Number),
        isTouchDevice: expect.any(Boolean),
        isRetina: expect.any(Boolean),
        tauriPlatform: 'ios',
      });
    });

    it('应该正确检测触摸设备', () => {
      // Mock touch support
      Object.defineProperty(window, 'ontouchstart', {
        value: null,
        writable: true,
      });
      
      mockWindow('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)');
      
      const deviceInfo = getDeviceInfo();
      expect(deviceInfo.isTouchDevice).toBe(true);
    });

    it('应该正确检测Retina显示屏', () => {
      Object.defineProperty(window, 'devicePixelRatio', {
        value: 2,
        writable: true,
      });
      
      mockWindow('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)');
      
      const deviceInfo = getDeviceInfo();
      expect(deviceInfo.isRetina).toBe(true);
    });
  });
}); 