/**
 * 平台检测工具
 * 用于识别用户设备类型（移动端/桌面端）
 * 集成 Tauri OS 插件进行更准确的平台检测
 */

import { platform as getTauriPlatform } from '@tauri-apps/plugin-os'; // 必须在导入之前 mock

export type PlatformType = 'mobile' | 'desktop';

export interface DeviceInfo {
  platform: PlatformType;
  userAgent: string;
  screenWidth: number;
  screenHeight: number;
  isTouchDevice: boolean;
  isRetina: boolean;
  tauriPlatform?: string;
}

/**
 * 移动端设备的用户代理字符串模式
 */
const MOBILE_USER_AGENTS = [
  /Android/i,
  /webOS/i,
  /iPhone/i,
  /iPad/i,
  /iPod/i,
  /BlackBerry/i,
  /Windows Phone/i,
  /Mobile/i,
];

/**
 * 移动端屏幕宽度阈值
 */
const MOBILE_BREAKPOINT = 768;

/**
 * Tauri 移动端平台列表
 */
const TAURI_MOBILE_PLATFORMS = ['android', 'ios'];

/**
 * 检测是否在 Tauri 环境中运行
 */
const isTauriEnvironment = (): boolean => {
  return typeof window !== 'undefined' && 
         typeof (window as any).__TAURI__ !== 'undefined';
};

/**
 * 使用 Tauri OS 插件检测平台（同步）
 */
const detectTauriPlatform = (): string | null => {
  if (!isTauriEnvironment()) {
    return null;
  }

  try {
    const tauriPlatform = getTauriPlatform();
    return tauriPlatform;
  } catch (error) {
    console.warn('Failed to get Tauri platform:', error);
    return null;
  }
};

/**
 * 基于浏览器信息检测平台类型（回退方案）
 */
const detectBrowserPlatform = (): PlatformType => {
  // 检查用户代理字符串
  const userAgent = navigator.userAgent;
  const isMobileUserAgent = MOBILE_USER_AGENTS.some(pattern => 
    pattern.test(userAgent)
  );

  // 检查屏幕宽度
  const screenWidth = window.innerWidth;
  const isMobileScreen = screenWidth < MOBILE_BREAKPOINT;

  // 如果用户代理表明是移动设备，但屏幕很大，则认为是桌面端
  // 如果用户代理表明是桌面设备，但屏幕很小，则认为是移动端
  if (isMobileUserAgent && !isMobileScreen && screenWidth > 1024) {
    return 'desktop';
  }
  
  if (!isMobileUserAgent && isMobileScreen) {
    return 'mobile';
  }

  return isMobileUserAgent ? 'mobile' : 'desktop';
};

/**
 * 检测当前平台类型
 * @returns 平台类型：'mobile' 或 'desktop'
 */
export const detectPlatform = (): PlatformType => {
  // 尝试使用 Tauri OS 插件
  const tauriPlatform = detectTauriPlatform();
  
  if (tauriPlatform) {
    // 根据 Tauri 平台信息判断是否为移动端
    return TAURI_MOBILE_PLATFORMS.includes(tauriPlatform) ? 'mobile' : 'desktop';
  }

  // 回退到浏览器检测
  return detectBrowserPlatform();
};

/**
 * 异步检测当前平台类型（为了向后兼容保留，实际上是同步的）
 * @returns Promise<平台类型>
 * @deprecated 使用 detectPlatform() 代替，因为 Tauri platform() 是同步的
 */
export const detectPlatformAsync = async (): Promise<PlatformType> => {
  return detectPlatform();
};

/**
 * 检查是否为移动端设备
 * @returns 是否为移动端
 */
export const isMobile = (): boolean => {
  return detectPlatform() === 'mobile';
};

/**
 * 异步检查是否为移动端设备（为了向后兼容保留）
 * @returns Promise<是否为移动端>
 * @deprecated 使用 isMobile() 代替
 */
export const isMobileAsync = async (): Promise<boolean> => {
  return isMobile();
};

/**
 * 检查是否为桌面端设备
 * @returns 是否为桌面端
 */
export const isDesktop = (): boolean => {
  return detectPlatform() === 'desktop';
};

/**
 * 异步检查是否为桌面端设备（为了向后兼容保留）
 * @returns Promise<是否为桌面端>
 * @deprecated 使用 isDesktop() 代替
 */
export const isDesktopAsync = async (): Promise<boolean> => {
  return isDesktop();
};

/**
 * 检测是否为触摸设备
 * @returns 是否支持触摸
 */
const isTouchDevice = (): boolean => {
  return (
    'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    // @ts-ignore
    navigator.msMaxTouchPoints > 0
  );
};

/**
 * 检测是否为Retina显示屏
 * @returns 是否为高分辨率显示屏
 */
const isRetinaDisplay = (): boolean => {
  return window.devicePixelRatio > 1;
};

/**
 * 获取完整的设备信息
 * @returns 设备信息对象
 */
export const getDeviceInfo = (): DeviceInfo => {
  const tauriPlatform = detectTauriPlatform();
  
  return {
    platform: detectPlatform(),
    userAgent: navigator.userAgent,
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight,
    isTouchDevice: isTouchDevice(),
    isRetina: isRetinaDisplay(),
    tauriPlatform: tauriPlatform || undefined,
  };
};

/**
 * 异步获取完整的设备信息（为了向后兼容保留）
 * @returns Promise<设备信息对象>
 * @deprecated 使用 getDeviceInfo() 代替
 */
export const getDeviceInfoAsync = async (): Promise<DeviceInfo> => {
  return getDeviceInfo();
};

/**
 * 监听屏幕尺寸变化
 * @param callback 回调函数
 * @returns 清理函数
 */
export const onPlatformChange = (
  callback: (platform: PlatformType) => void
): (() => void) => {
  let currentPlatform = detectPlatform();

  const handleResize = () => {
    const newPlatform = detectPlatform();
    if (newPlatform !== currentPlatform) {
      currentPlatform = newPlatform;
      callback(newPlatform);
    }
  };

  window.addEventListener('resize', handleResize);
  
  return () => {
    window.removeEventListener('resize', handleResize);
  };
}; 