/**
 * Tauri OS 插件集成测试
 * 验证平台检测功能在 Tauri 环境中的工作情况
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock Tauri OS plugin - 必须在导入之前
vi.mock('@tauri-apps/plugin-os', () => ({
  platform: vi.fn(),
}));

import { detectPlatform, getDeviceInfo } from '../shared/utils/platform-detector';

describe('Tauri OS 插件集成测试', () => {
  let mockTauriPlatform: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    // 清理 Tauri 环境
    delete (window as any).__TAURI__;
    
    // 获取 mock 函数
    const { platform } = await import('@tauri-apps/plugin-os');
    mockTauriPlatform = vi.mocked(platform);
  });

  describe('在浏览器环境中', () => {
    it('应该回退到浏览器检测 - 移动端', () => {
      // 模拟移动端用户代理
      Object.defineProperty(window, 'navigator', {
        value: { userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)' },
        writable: true,
      });
      Object.defineProperty(window, 'innerWidth', {
        value: 375,
        writable: true,
      });

      const platform = detectPlatform();
      expect(platform).toBe('mobile');
      
      // 确保没有调用 Tauri 插件
      expect(mockTauriPlatform).not.toHaveBeenCalled();
    });

    it('应该回退到浏览器检测 - 桌面端', () => {
      // 模拟桌面端用户代理
      Object.defineProperty(window, 'navigator', {
        value: { userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' },
        writable: true,
      });
      Object.defineProperty(window, 'innerWidth', {
        value: 1920,
        writable: true,
      });

      const platform = detectPlatform();
      expect(platform).toBe('desktop');
      
      // 确保没有调用 Tauri 插件
      expect(mockTauriPlatform).not.toHaveBeenCalled();
    });
  });

  describe('在 Tauri 环境中', () => {
    beforeEach(() => {
      // 模拟 Tauri 环境
      (window as any).__TAURI__ = {};
    });

    it('应该使用 Tauri OS 插件检测 Android 平台', () => {
      mockTauriPlatform.mockReturnValue('android');

      const platform = detectPlatform();
      
      expect(platform).toBe('mobile');
      expect(mockTauriPlatform).toHaveBeenCalled();
    });

    it('应该使用 Tauri OS 插件检测 iOS 平台', () => {
      mockTauriPlatform.mockReturnValue('ios');

      const platform = detectPlatform();
      
      expect(platform).toBe('mobile');
      expect(mockTauriPlatform).toHaveBeenCalled();
    });

    it('应该使用 Tauri OS 插件检测 Windows 平台', () => {
      mockTauriPlatform.mockReturnValue('windows');

      const platform = detectPlatform();
      
      expect(platform).toBe('desktop');
      expect(mockTauriPlatform).toHaveBeenCalled();
    });

    it('应该使用 Tauri OS 插件检测 macOS 平台', () => {
      mockTauriPlatform.mockReturnValue('macos');

      const platform = detectPlatform();
      
      expect(platform).toBe('desktop');
      expect(mockTauriPlatform).toHaveBeenCalled();
    });

    it('在插件失败时应该回退到浏览器检测', () => {
      mockTauriPlatform.mockImplementation(() => {
        throw new Error('Plugin failed');
      });
      
      // 设置浏览器检测的回退值
      Object.defineProperty(window, 'navigator', {
        value: { userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)' },
        writable: true,
      });
      Object.defineProperty(window, 'innerWidth', {
        value: 375,
        writable: true,
      });

      const platform = detectPlatform();
      
      expect(platform).toBe('mobile');
      expect(mockTauriPlatform).toHaveBeenCalled();
    });

    it('应该在设备信息中包含 Tauri 平台信息', () => {
      mockTauriPlatform.mockReturnValue('android');
      
      Object.defineProperty(window, 'navigator', {
        value: { userAgent: 'Mozilla/5.0 (Linux; Android 10)' },
        writable: true,
      });
      Object.defineProperty(window, 'innerWidth', {
        value: 375,
        writable: true,
      });
      Object.defineProperty(window, 'innerHeight', {
        value: 667,
        writable: true,
      });

      const deviceInfo = getDeviceInfo();
      
      expect(deviceInfo).toEqual({
        platform: 'mobile',
        userAgent: expect.stringContaining('Android'),
        screenWidth: 375,
        screenHeight: 667,
        isTouchDevice: expect.any(Boolean),
        isRetina: expect.any(Boolean),
        tauriPlatform: 'android',
      });
      
      expect(mockTauriPlatform).toHaveBeenCalled();
    });
  });

  describe('平台映射测试', () => {
    beforeEach(() => {
      (window as any).__TAURI__ = {};
    });

    const testCases = [
      { tauriPlatform: 'android', expectedPlatform: 'mobile' },
      { tauriPlatform: 'ios', expectedPlatform: 'mobile' },
      { tauriPlatform: 'windows', expectedPlatform: 'desktop' },
      { tauriPlatform: 'macos', expectedPlatform: 'desktop' },
      { tauriPlatform: 'linux', expectedPlatform: 'desktop' },
      { tauriPlatform: 'freebsd', expectedPlatform: 'desktop' },
      { tauriPlatform: 'openbsd', expectedPlatform: 'desktop' },
      { tauriPlatform: 'netbsd', expectedPlatform: 'desktop' },
      { tauriPlatform: 'dragonfly', expectedPlatform: 'desktop' },
      { tauriPlatform: 'solaris', expectedPlatform: 'desktop' },
    ];

    testCases.forEach(({ tauriPlatform, expectedPlatform }) => {
      it(`应该将 ${tauriPlatform} 映射为 ${expectedPlatform}`, () => {
        mockTauriPlatform.mockReturnValue(tauriPlatform);

        const platform = detectPlatform();
        
        expect(platform).toBe(expectedPlatform);
        expect(mockTauriPlatform).toHaveBeenCalled();
      });
    });
  });
}); 